
import { Order } from "@/types";

interface OrderAddOnsInfoProps {
  order: Order;
}

export function OrderAddOnsInfo({ order }: OrderAddOnsInfoProps) {
  const hasAddOns = order.useDetergent || order.useFabricConditioner || order.useStainRemover;
  
  if (!hasAddOns) return null;
  
  return (
    <div className="text-sm text-muted-foreground">
      <p>Additional services:</p>
      <ul className="list-disc pl-5">
        {order.useDetergent && <li>Detergent</li>}
        {order.useFabricConditioner && <li>Fabric Conditioner</li>}
        {order.useStainRemover && <li>Stain Remover</li>}
      </ul>
    </div>
  );
}
