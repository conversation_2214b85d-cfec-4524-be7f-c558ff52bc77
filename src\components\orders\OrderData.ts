
import { 
  createOrder as addOrderFromService, 
  getOrdersStore as getAllOrders, 
  updateOrderPayment, 
  updateOrderStatus as updateStatus 
} from "@/services/orders";
import { createOrderNotification } from "@/services/notifications";

console.log("OrderData.ts loaded");
console.log("Imported functions:", {
  addOrderFromService: typeof addOrderFromService,
  getAllOrders: typeof getAllOrders,
  updateOrderPayment: typeof updateOrderPayment,
  updateStatus: typeof updateStatus
});

// Function wrapper for debugging
export async function addOrder(orderData: any) {
  console.log("OrderData.ts: addOrder called with:", orderData);
  
  try {
    // Validate required fields
    if (!orderData.customerType) {
      console.error("OrderData.ts: Missing customerType field");
      throw new Error("Customer type is required");
    }
    
    // Make sure numberOfPieces exists and is properly formatted
    if (orderData.numberOfPieces === undefined || orderData.numberOfPieces === null) {
      console.log("OrderData.ts: Setting default numberOfPieces to 1");
      orderData.numberOfPieces = "1";
    }
    
    // Make sure weightKilos exists and is properly formatted
    if (orderData.weightKilos === undefined || orderData.weightKilos === null) {
      console.log("OrderData.ts: Setting default weightKilos to 0");
      orderData.weightKilos = "0";
    }
    
    // Process walk-in orders differently from client orders
    if (orderData.customerType === "walk-in") {
      console.log("OrderData.ts: Walk-in order detected, preparing walk-in specific data");
      
      // Explicitly mark as walk-in
      orderData.customer_type = "walk-in";
      
      // Walk-in orders should not be associated with specific clients
      orderData.clientId = undefined;
      
      // Ensure customer information is properly stored
      if (orderData.customerName) {
        orderData.customer_name = orderData.customerName;
      }
      
      if (orderData.phoneNumber) {
        orderData.phone_number = orderData.phoneNumber;
      }
      
      // For walk-in orders, if customer name is missing, set a default
      if (!orderData.customer_name && !orderData.customerName) {
        orderData.customer_name = "Walk-in Customer";
      }
    } else {
      // For client orders, ensure they have a valid clientId
      if (!orderData.clientId) {
        console.error("OrderData.ts: Missing clientId for client order");
        throw new Error("Client ID is required for client orders");
      }
      
      // Mark as client order type
      orderData.customer_type = "client";
    }
    
    // Ensure order amount, VAT, and subtotal are numbers
    if (orderData.orderAmount) {
      orderData.orderAmount = parseFloat(orderData.orderAmount);
    }
    
    if (orderData.vatAmount) {
      orderData.vatAmount = parseFloat(orderData.vatAmount);
    }
    
    if (orderData.subtotal) {
      orderData.subtotal = parseFloat(orderData.subtotal);
    }
    
    // Add await keyword to properly resolve the Promise before accessing the result
    const result = await addOrderFromService(orderData);
    console.log("OrderData.ts: addOrder result:", result);
    
    // Create notification for new order
    try {
      const customerName = orderData.customer?.name || orderData.customerName || "Customer";
      const customerType = orderData.customerType || "walk-in";
      
      await createOrderNotification(
        "New Order Created",
        `${customerName} (${customerType}) order has been created by staff`,
        result.id
      );
    } catch (notificationError) {
      console.error("OrderData.ts: Error creating notification:", notificationError);
    }
    
    return result;
  } catch (error) {
    console.error("OrderData.ts: Error in addOrder:", error);
    throw error;
  }
}

// Export the other functions directly
export { 
  getAllOrders as getOrders,
  updateOrderPayment, 
  updateStatus as updateOrderStatus 
};

console.log("OrderData.ts exports completed");
