
import { 
  createOrder as addOrderFromService, 
  getOrdersStore as getAllOrders, 
  updateOrderPayment, 
  updateOrderStatus as updateStatus 
} from "@/services/orders";
import { createOrderNotification } from "@/services/notifications";

console.log("OrderData.ts loaded");

// Function wrapper for debugging
export async function addOrder(orderData: any) {
  console.log("OrderData.ts: addOrder called with:", orderData);
  
  try {
    // Validate required fields
    if (!orderData.customerType) {
      console.error("OrderData.ts: Missing customerType field");
      throw new Error("Customer type is required");
    }
    
    // Make sure numberOfPieces exists and is properly formatted
    if (orderData.numberOfPieces === undefined || orderData.numberOfPieces === null) {
      console.log("OrderData.ts: Setting default numberOfPieces to 1");
      orderData.numberOfPieces = "1";
    }
    
    // Make sure weightKilos exists and is properly formatted
    if (orderData.weightKilos === undefined || orderData.weightKilos === null) {
      console.log("OrderData.ts: Setting default weightKilos to 0");
      orderData.weightKilos = "0";
    }
    
    // Process walk-in orders differently from client orders
    if (orderData.customerType === "walk-in") {
      console.log("OrderData.ts: Walk-in order detected, preparing walk-in specific data");
      
      // Explicitly set in database format
      orderData.customer_type = "walk-in";
      
      // Walk-in orders should not be associated with specific clients
      orderData.clientId = undefined;
      
      // Generate a walk-in reference code
      const timestamp = Date.now().toString().slice(-6);
      orderData.reference_code = `ORD-${timestamp}`;
      console.log("Generated walk-in reference code:", orderData.reference_code);
      
      // Ensure customer information is properly stored
      if (orderData.customerName) {
        orderData.customer_name = orderData.customerName;
      }
      
      if (orderData.phoneNumber) {
        orderData.phone_number = orderData.phoneNumber;
      }
      
      // For walk-in orders, if customer name is missing, set a default
      if (!orderData.customer_name && !orderData.customerName) {
        orderData.customer_name = "Walk-in Customer";
      }
    } else {
      // For client orders, ensure they have a valid clientId
      if (!orderData.clientId) {
        console.error("OrderData.ts: Missing clientId for client order");
        throw new Error("Client ID is required for client orders");
      }
      
      // Explicitly mark as client order type in database format
      orderData.customer_type = "client";
      
      // Generate client reference code with correct prefix format
      // Check for prefix in this specific order: explicitly provided prefix, clientPrefix, or default
      let prefix = "";
      if (orderData.prefix) {
        prefix = orderData.prefix.toUpperCase();
        console.log("Using provided prefix for reference code:", prefix);
      } else if (orderData.clientPrefix) {
        prefix = orderData.clientPrefix.toUpperCase();
        console.log("Using clientPrefix for reference code:", prefix);
      } else {
        prefix = "CLT"; // Fallback prefix
        console.log("Using default prefix for reference code:", prefix);
      }

      const timestamp = Date.now().toString().slice(-6);
      orderData.reference_code = `${prefix}-${timestamp}`;
      console.log("Generated client reference code:", orderData.reference_code);
    }
    
    // Ensure order amount, VAT, and subtotal are numbers
    if (orderData.orderAmount) {
      orderData.orderAmount = parseFloat(orderData.orderAmount);
    }
    
    if (orderData.vatAmount) {
      orderData.vatAmount = parseFloat(orderData.vatAmount);
    }
    
    if (orderData.subtotal) {
      orderData.subtotal = parseFloat(orderData.subtotal);
    }
    
    console.log("OrderData.ts: Final order data before sending:", {
      customerType: orderData.customerType,
      customer_type: orderData.customer_type,
      clientId: orderData.clientId,
      reference_code: orderData.reference_code,
      prefix: orderData.prefix
    });
    
    // Add await keyword to properly resolve the Promise before accessing the result
    const result = await addOrderFromService(orderData);
    console.log("OrderData.ts: addOrder result:", result);
    
    // Create notification for new order
    try {
      const customerName = orderData.customer?.name || orderData.customerName || "Customer";
      const customerType = orderData.customerType || "walk-in";
      
      await createOrderNotification(
        "New Order Created",
        `${customerName} (${customerType}) order has been created by staff`,
        result.uuid // Use UUID not ID for notifications
      ).catch(err => {
        // Catch and handle notification errors to prevent them from breaking the main flow
        console.log("Failed to create notification, but order was created successfully:", err);
      });
    } catch (notificationError) {
      console.error("OrderData.ts: Error creating notification:", notificationError);
      // Don't throw the error - notifications are non-critical
    }
    
    return result;
  } catch (error) {
    console.error("OrderData.ts: Error in addOrder:", error);
    throw error;
  }
}

// Export the other functions directly
export { 
  getAllOrders as getOrders,
  updateOrderPayment, 
  updateStatus as updateOrderStatus 
};

console.log("OrderData.ts exports completed");
