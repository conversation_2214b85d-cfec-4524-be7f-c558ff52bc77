
import { 
  createOrder as addOrderFromService, 
  getOrdersStore as getAllOrders, 
  updateOrderPayment, 
  updateOrderStatus as updateStatus 
} from "@/services/orders";
import { 
  createOrderNotification as createNotification 
} from "@/services/notifications";

console.log("OrderData.ts loaded");

// Function wrapper for debugging
export async function addOrder(orderData: any) {
  console.log("OrderData.ts: addOrder called with:", orderData);
  
  try {
    // Validate required fields
    if (!orderData.customerType) {
      console.error("OrderData.ts: Missing customerType field");
      throw new Error("Customer type is required");
    }
    
    // Make sure numberOfPieces exists and is properly formatted
    if (orderData.numberOfPieces === undefined || orderData.numberOfPieces === null) {
      console.log("OrderData.ts: Setting default numberOfPieces to 1");
      orderData.numberOfPieces = "1";
    }
    
    // Make sure weightKilos exists and is properly formatted
    if (orderData.weightKilos === undefined || orderData.weightKilos === null) {
      console.log("OrderData.ts: Setting default weightKilos to 0");
      orderData.weightKilos = "0";
    }
    
    // Handle dry cleaning information properly
    const isDryCleaning = orderData.isDryCleaning || 
                        orderData.selectedServiceTypes?.includes(SERVICE_TYPES.DRY_CLEANING) || 
                        orderData.serviceType === SERVICE_TYPES.DRY_CLEANING;
                        
    orderData.isDryCleaning = isDryCleaning;
    
    if (isDryCleaning) {
      console.log("OrderData.ts: Dry cleaning order detected");
      // Make sure dry cleaning items are properly formatted as an array
      if (orderData.dryCleaningItems) {
        // If it's not an array, convert it
        if (!Array.isArray(orderData.dryCleaningItems)) {
          orderData.dryCleaningItems = [orderData.dryCleaningItems];
        }
        
        // Ensure each item has the required properties
        orderData.dryCleaningItems = orderData.dryCleaningItems.map((item: any, index: number) => ({
          id: item.id || `dry-item-${index}-${Date.now()}`,
          type: item.type || item.name || "unknown",
          name: item.name || item.type || "Unknown Item",
          price: Number(item.price || item.unitPrice || 0),
          quantity: Number(item.quantity || 1),
          total: Number(item.total || (item.price * item.quantity) || 0)
        }));
      } else {
        // If no dry cleaning items, initialize with an empty array
        orderData.dryCleaningItems = [];
      }
      
      // Always set service_type for dry cleaning orders
      orderData.service_type = SERVICE_TYPES.DRY_CLEANING;
    }
    
    // Process walk-in orders differently from client orders
    if (orderData.customerType === "walk-in") {
      console.log("OrderData.ts: Walk-in order detected, preparing walk-in specific data");
      
      // Explicitly set in database format
      orderData.customer_type = "walk-in";
      
      // Walk-in orders should not be associated with specific clients
      orderData.clientId = undefined;
      
      // Generate a walk-in reference code
      const timestamp = Date.now().toString().slice(-6);
      orderData.reference_code = `ORD-${timestamp}`;
      console.log("Generated walk-in reference code:", orderData.reference_code);
      
      // Ensure customer information is properly stored
      if (orderData.customerName) {
        orderData.customer_name = orderData.customerName;
      }
      
      if (orderData.phoneNumber) {
        orderData.phone_number = orderData.phoneNumber;
      }
      
      // For walk-in orders, if customer name is missing, set a default
      if (!orderData.customer_name && !orderData.customerName) {
        orderData.customer_name = "Walk-in Customer";
      }
    } else {
      // For client orders, ensure they have a valid clientId
      if (!orderData.clientId) {
        console.error("OrderData.ts: Missing clientId for client order");
        throw new Error("Client ID is required for client orders");
      }
      
      // Explicitly mark as client order type in database format
      orderData.customer_type = "client";
      
      // We'll let the createOrder service handle the client prefix and reference code generation
      // This ensures that it fetches the latest client prefix from the database
      console.log("OrderData.ts: Client order detected, letting service handle reference code with proper prefix");
      
      // Pass through any explicitly provided prefix if available
      if (orderData.prefix) {
        console.log("OrderData.ts: Using provided prefix for reference code:", orderData.prefix);
      }
      
      if (orderData.clientPrefix) {
        console.log("OrderData.ts: Using clientPrefix for reference code:", orderData.clientPrefix);
      }
    }
    
    // Ensure order amount, VAT, and subtotal are numbers
    if (orderData.orderAmount) {
      orderData.orderAmount = parseFloat(orderData.orderAmount);
    }
    
    if (orderData.vatAmount) {
      orderData.vatAmount = parseFloat(orderData.vatAmount);
    }
    
    if (orderData.subtotal) {
      orderData.subtotal = parseFloat(orderData.subtotal);
    }
    
    console.log("OrderData.ts: Final order data before sending:", {
      customerType: orderData.customerType,
      customer_type: orderData.customer_type,
      clientId: orderData.clientId,
      reference_code: orderData.reference_code,
      prefix: orderData.prefix,
      isDryCleaning: orderData.isDryCleaning,
      service_type: orderData.service_type,
      dryCleaningItems: orderData.dryCleaningItems?.length || 0
    });
    
    // Add await keyword to properly resolve the Promise before accessing the result
    const result = await addOrderFromService(orderData);
    console.log("OrderData.ts: addOrder result:", result);
    
    // Create notification for new order
    try {
      const customerName = orderData.customer?.name || orderData.customerName || "Customer";
      const customerType = orderData.customerType || "walk-in";
      
      await createNotification(
        "New Order Created",
        `${customerName} (${customerType}) order has been created by staff`,
        result.uuid // Use UUID not ID for notifications
      ).catch(err => {
        // Catch and handle notification errors to prevent them from breaking the main flow
        console.log("Failed to create notification, but order was created successfully:", err);
      });
    } catch (notificationError) {
      console.error("OrderData.ts: Error creating notification:", notificationError);
      // Don't throw the error - notifications are non-critical
    }
    
    return result;
  } catch (error) {
    console.error("OrderData.ts: Error in addOrder:", error);
    throw error;
  }
}

// Make sure SERVICE_TYPES is imported or defined
const SERVICE_TYPES = {
  WASH_DRY_FOLD: 'wash_dry_fold',
  DRY_CLEANING: 'dry_cleaning',
  PRESS_ONLY: 'press_only',
  WASH_ONLY: 'wash_only',
  DRY_ONLY: 'dry_only'
};

// Export the other functions directly
export { 
  getAllOrders as getOrders,
  updateOrderPayment, 
  updateStatus as updateOrderStatus 
};

console.log("OrderData.ts exports completed");
