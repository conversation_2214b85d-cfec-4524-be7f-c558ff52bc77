
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Printer, FileText } from "lucide-react";

interface PrintButtonsProps {
  onPrintReceipt: () => void;
  onPrintJobOrder: () => void;
  isPrinting: boolean;
  isStaff: boolean;
}

export function PrintButtons({ 
  onPrintReceipt, 
  onPrintJobOrder, 
  isPrinting, 
  isStaff 
}: PrintButtonsProps) {
  return (
    <>
      {/* Print Receipt Button */}
      <Button 
        variant="outline" 
        onClick={onPrintReceipt}
        disabled={isPrinting}
        className="w-full"
      >
        <Printer className="h-4 w-4 mr-2" />
        Print Receipt
      </Button>

      {/* Print Job Order Button - only for staff and admin */}
      {isStaff && (
        <Button 
          variant="outline" 
          onClick={onPrintJobOrder}
          disabled={isPrinting}
          className="w-full"
        >
          <FileText className="h-4 w-4 mr-2" />
          Print Job Order
        </Button>
      )}
    </>
  );
}
