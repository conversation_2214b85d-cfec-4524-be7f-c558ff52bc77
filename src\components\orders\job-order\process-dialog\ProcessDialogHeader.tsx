
import { Dialog<PERSON>eader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ClipboardCheck, Package2, X } from "lucide-react";
import { Order } from "@/types";
import { SERVICE_TYPES } from "../../pricing/constants";

interface ProcessDialogHeaderProps {
  order: Order;
  onClose: () => void;
}

export function ProcessDialogHeader({ order, onClose }: ProcessDialogHeaderProps) {
  const isDryCleaningService = order?.serviceType === SERVICE_TYPES.DRY_CLEANING;
  const serviceTypeDisplay = order?.serviceType?.replace(/_/g, ' ') || "Job Order";
  
  return (
    <div className="flex items-center justify-between">
      <DialogHeader className="flex-1">
        <DialogTitle className="flex items-center gap-2">
          {isDryCleaningService ? 
            <Package2 className="h-5 w-5" /> :
            <ClipboardCheck className="h-5 w-5" />
          }
          Process Job Order {order?.reference_code || order?.id}
        </DialogTitle>
        <DialogDescription>
          {isDryCleaningService 
            ? "Manage dry cleaning items, treatments, and order status" 
            : `Process ${serviceTypeDisplay} order, manage items and update status`}
        </DialogDescription>
      </DialogHeader>
      
      <Button 
        variant="outline" 
        size="icon" 
        className="rounded-full"
        onClick={onClose}
      >
        <X className="h-4 w-4" />
      </Button>
    </div>
  );
}
