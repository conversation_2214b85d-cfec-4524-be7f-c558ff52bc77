
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Eye } from 'lucide-react';
import { OrderStatusBadge } from '@/components/orders/OrderStatusBadge';
import { Order } from '@/types';
import { useState } from 'react';
import { ViewOrderDialog } from '@/components/orders/ViewOrderDialog';
import { useSimpleOrderActions } from '@/hooks/orders/useSimpleOrderActions';
import { useToast } from '@/hooks/use-toast';

interface ClientOrderTableProps {
  filteredOrders: Order[];
  status: string;
}

export function ClientOrderTable({ filteredOrders, status }: ClientOrderTableProps) {
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const { handleStatusChange } = useSimpleOrderActions();
  const { toast } = useToast();

  const viewOrder = (order: Order) => {
    console.log(`Viewing order: ${order.id}`);
    setSelectedOrder(order);
    setIsViewDialogOpen(true);
  };

  const onStatusChange = async (orderId: string, newStatus: string): Promise<void> => {
    console.log(`Changing status of order ${orderId} to ${newStatus}`);
    try {
      const success = await handleStatusChange(orderId, newStatus);
      if (!success) {
        toast({
          title: "Status Update Failed",
          description: "There was a problem updating the order status",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error updating status:", error);
      toast({
        title: "Status Update Error",
        description: "An unexpected error occurred while updating status",
        variant: "destructive",
      });
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-PH", {
      style: "currency",
      currency: "PHP",
    }).format(amount);
  };

  return (
    <>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Order ID</TableHead>
              <TableHead>Order Date</TableHead>
              <TableHead>Delivery Date</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Amount</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredOrders.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center h-24 text-muted-foreground">
                  {status === 'all'
                    ? "You don't have any orders yet."
                    : `No ${status} orders found.`}
                </TableCell>
              </TableRow>
            ) : (
              filteredOrders.map((order) => (
                <TableRow key={order.id}>
                  <TableCell className="font-medium">
                    {order.id}
                    {order.reference_code && order.reference_code !== order.id && (
                      <div className="text-xs text-muted-foreground">Ref: {order.reference_code}</div>
                    )}
                  </TableCell>
                  <TableCell>{order.orderDate}</TableCell>
                  <TableCell>{order.deliveryDate}</TableCell>
                  <TableCell>
                    <OrderStatusBadge status={order.status} />
                  </TableCell>
                  <TableCell className="text-right">
                    {formatCurrency(order.amount)}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => viewOrder(order)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Order details dialog */}
      {selectedOrder && (
        <ViewOrderDialog 
          order={selectedOrder}
          open={isViewDialogOpen}
          onOpenChange={setIsViewDialogOpen}
          onStatusChange={onStatusChange}
        />
      )}
    </>
  );
}
