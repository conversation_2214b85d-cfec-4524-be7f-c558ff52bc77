
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Order } from "@/components/orders/status/types";
import { format } from "date-fns";

export function useOrderStatus() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchOrders = async () => {
    try {
      setIsLoading(true);
      
      const { data, error } = await supabase
        .from("orders")
        .select("*")
        .order("created_at", { ascending: false });
        
      if (error) {
        throw error;
      }
      
      if (data) {
        // Map the data to our Order type
        const formattedOrders: Order[] = data.map(order => {
          // Parse items JSON if it's a string
          let parsedItems = [];
          try {
            if (order.items) {
              parsedItems = typeof order.items === 'string' 
                ? JSON.parse(order.items) 
                : order.items;
            }
          } catch (e) {
            console.error("Error parsing items:", e);
            parsedItems = [];
          }
          
          // Extract items for display in a simplified format
          const orderItems = parsedItems.map((item: any) => ({
            name: item.name || '',
            qty: Number(item.quantity) || 1,
            price: Number(item.unitPrice || item.price || 0)
          }));
          
          return {
            id: order.reference_code || order.id,
            uuid: order.id,
            customer: order.customer_name || "Unknown",
            phone: order.phone_number || "N/A",
            items: orderItems,
            totalAmount: Number(order.amount) || 0,
            status: order.status || "processing",
            // Use status as stage for now, they're essentially the same in our system
            stage: order.status || "processing",
            timestamp: format(new Date(order.updated_at || order.created_at), "yyyy-MM-dd hh:mm a"),
            isClientOrder: order.customer_type === "client"
          };
        });
        
        setOrders(formattedOrders);
      }
    } catch (err: any) {
      console.error("Error fetching orders:", err);
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchOrders();
    
    // Set up real-time subscription to orders updates
    const channel = supabase
      .channel('orders-changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'orders' },
        (payload) => {
          console.log('Orders updated:', payload);
          fetchOrders();
        }
      )
      .subscribe();
      
    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  return { 
    orders, 
    isLoading, 
    error, 
    refreshOrders: fetchOrders 
  };
}
