
import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Plus, Trash2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { ClientItem, getClientItems, addClientItem, deleteClientItem } from "@/services/clientItem";
import { AddClientItemDialog } from "../AddClientItemDialog";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { DialogFooter } from "@/components/ui/dialog";
import { useClientItems } from "@/hooks/useClientItems";
import { formatCurrency } from "@/lib/utils";

interface ClientItemsTabProps {
  clientId: string;
  onClose: () => void;
}

export function ClientItemsTab({ clientId, onClose }: ClientItemsTabProps) {
  const { toast } = useToast();
  const [isAddItemDialogOpen, setIsAddItemDialogOpen] = useState(false);
  const { items, isLoading } = useClientItems(clientId);
  const [clientItems, setClientItems] = useState<ClientItem[]>([]);

  // Update local state when items are fetched
  useEffect(() => {
    if (items && Array.isArray(items)) {
      setClientItems([...items]);
    }
  }, [items]);

  const handleAddItem = async (data: { name: string; unitPrice: number }) => {
    try {
      const newItem = await addClientItem({
        client_id: clientId,
        name: data.name,
        unit_price: data.unitPrice
      });
      
      if (newItem) {
        // Refresh the items list
        const updatedItems = await getClientItems(clientId);
        if (updatedItems) {
          setClientItems([...updatedItems]);
        }
        
        toast({
          title: "Item Added",
          description: `"${newItem.name}" has been added to this client.`
        });
      }
    } catch (error) {
      console.error("Failed to add item:", error);
      toast({
        title: "Error",
        description: "Failed to add item. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleDeleteItem = async (itemId: string, itemName: string) => {
    try {
      // Fix: Pass both itemId and clientId as required by the function
      const success = await deleteClientItem(itemId, clientId);
      
      if (success) {
        setClientItems(clientItems.filter(item => item.id !== itemId));
        
        toast({
          title: "Item Deleted",
          description: `"${itemName}" has been removed from this client.`
        });
      } else {
        throw new Error("Failed to delete item");
      }
    } catch (error) {
      console.error("Failed to delete item:", error);
      toast({
        title: "Error",
        description: "Failed to delete item. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="space-y-6">
      <div className="mb-4">
        <h3 className="text-sm font-medium mb-3">Client Service Items</h3>
        <div className="border rounded-md p-4 min-h-[200px] bg-muted/20">
          {isLoading ? (
            <div className="space-y-2">
              <Skeleton className="h-8 w-1/3" />
              <Skeleton className="h-8 w-1/2" />
              <Skeleton className="h-8 w-2/5" />
            </div>
          ) : clientItems.length > 0 ? (
            <div className="flex flex-wrap gap-2">
              {clientItems.map(item => (
                <Badge 
                  key={item.id} 
                  variant="secondary" 
                  className="px-3 py-1.5 flex items-center gap-1"
                >
                  {item.name} • {formatCurrency(item.unit_price || 0)}
                  <button 
                    onClick={() => handleDeleteItem(item.id, item.name)}
                    className="ml-1 rounded-full hover:bg-muted p-0.5" 
                    type="button"
                  >
                    <Trash2 className="h-3 w-3 text-muted-foreground" />
                  </button>
                </Badge>
              ))}
            </div>
          ) : (
            <div className="text-center text-muted-foreground py-8">
              No service items added yet. Add items using the button below.
            </div>
          )}
        </div>
      </div>

      <Button 
        onClick={() => setIsAddItemDialogOpen(true)}
        className="w-full"
      >
        <Plus className="mr-2 h-4 w-4" /> Add Service Item
      </Button>

      <DialogFooter>
        <Button 
          type="button" 
          variant="outline" 
          onClick={onClose}
        >
          Close
        </Button>
      </DialogFooter>

      <AddClientItemDialog
        clientId={clientId}
        isOpen={isAddItemDialogOpen}
        onOpenChange={setIsAddItemDialogOpen}
        onAddItem={handleAddItem}
      />
    </div>
  );
}
