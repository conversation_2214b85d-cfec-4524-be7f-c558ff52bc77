
import { ClientItem } from "@/services/clientItemService";
import { ClientItemWithQuantity } from "@/components/orders/OrderFormTypes";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ClientItemRow } from "./ClientItemRow";

interface ClientItemsTableProps {
  items: ClientItem[];
  selectedItems: ClientItemWithQuantity[];
  onItemToggle: (item: ClientItem, isSelected: boolean) => void;
  onQuantityChange: (itemId: string, newQuantity: number) => void;
  unitPrice: number;
}

export function ClientItemsTable({ 
  items, 
  selectedItems, 
  onItemToggle, 
  onQuantityChange,
  unitPrice
}: ClientItemsTableProps) {
  if (items.length === 0) {
    return (
      <div className="text-center p-8 border rounded-md bg-muted/20">
        <p className="text-muted-foreground">No items available for this client.</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg border shadow-sm overflow-hidden">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[60px]"></TableHead>
            <TableHead>Item</TableHead>
            <TableHead className="w-[160px]">Quantity</TableHead>
            <TableHead className="text-right">Total</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {items.map((item) => {
            const selectedItem = selectedItems.find(i => i.id === item.id);
            const isSelected = !!selectedItem;
            
            return (
              <ClientItemRow
                key={item.id}
                item={item}
                selectedItem={selectedItem}
                isSelected={isSelected}
                onToggle={onItemToggle}
                onQuantityChange={onQuantityChange}
                unitPrice={unitPrice}
              />
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
}
