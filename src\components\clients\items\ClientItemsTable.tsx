
import { ClientItem } from "@/services/clientItemService";
import { ClientItemWithQuantity } from "@/components/orders/OrderFormTypes";
import { ClientItemRow } from "./ClientItemRow";

interface ClientItemsTableProps {
  items: ClientItem[];
  selectedItems: ClientItemWithQuantity[];
  onItemToggle: (item: ClientItem, isSelected: boolean) => void;
  onQuantityChange: (itemId: string, newQuantity: number) => void;
  unitPrice: number;
}

export function ClientItemsTable({
  items,
  selectedItems,
  onItemToggle,
  onQuantityChange,
  unitPrice
}: ClientItemsTableProps) {
  if (items.length === 0) {
    return (
      <div className="text-center p-8 border rounded-md bg-muted/20">
        <p className="text-muted-foreground">No items available for this client.</p>
      </div>
    );
  }

  // Group items by type for better organization
  const itemsByType = items.reduce((acc: Record<string, ClientItem[]>, item) => {
    const type = item.item_type || 'standard';
    if (!acc[type]) {
      acc[type] = [];
    }
    acc[type].push(item);
    return acc;
  }, {});

  return (
    <div className="bg-white rounded-lg border shadow-sm p-4">
      <h3 className="font-semibold text-lg mb-4 flex items-center">
        Service Items
      </h3>

      {Object.entries(itemsByType).map(([type, typeItems]) => (
        <div key={type} className="mb-6">
          <h4 className="font-medium text-base mb-3 text-gray-700 border-b pb-1">
            {type.charAt(0).toUpperCase() + type.slice(1)} Items
          </h4>
          <div className="space-y-2">
            {typeItems.map((item) => {
              const selectedItem = selectedItems.find(i => i.id === item.id);
              const isSelected = !!selectedItem;

              return (
                <ClientItemRow
                  key={item.id}
                  item={item}
                  selectedItem={selectedItem}
                  isSelected={isSelected}
                  onToggle={onItemToggle}
                  onQuantityChange={onQuantityChange}
                  unitPrice={unitPrice}
                />
              );
            })}
          </div>
        </div>
      ))}
    </div>
  );
}
