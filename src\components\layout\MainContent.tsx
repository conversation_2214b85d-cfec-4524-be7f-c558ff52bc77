
import { ReactNode } from "react";
import { TopBar } from "./TopBar";
import { useIsMobile } from "@/hooks/use-mobile";

interface MainContentProps {
  onMenuClick: () => void;
  children: ReactNode;
}

export function MainContent({ onMenuClick, children }: MainContentProps) {
  const isMobile = useIsMobile();
  
  return (
    <div className="flex-1 flex flex-col overflow-hidden">
      <TopBar onMenuClick={onMenuClick} />
      <main className={`flex-1 overflow-x-auto overflow-y-auto ${isMobile ? 'p-2' : 'p-4 md:p-6 lg:p-8'}`}>
        <div className="w-full max-w-7xl mx-auto">
          {children}
        </div>
      </main>
    </div>
  );
}
