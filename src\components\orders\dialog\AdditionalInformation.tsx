
import { Order } from "@/types";

interface AdditionalInformationProps {
  order: Order;
}

export function AdditionalInformation({ order }: AdditionalInformationProps) {
  return (
    <div className="space-y-2">
      <h4 className="text-sm font-medium">Additional Information</h4>
      <div className="text-sm">
        {order.weightKilos && (
          <div>
            <strong>Weight:</strong> {order.weightKilos} kg
          </div>
        )}
        {order.numberOfPieces && (
          <div>
            <strong>Pieces:</strong> {order.numberOfPieces}
          </div>
        )}
        {/* Show add-ons information */}
        {(order.useDetergent || order.useFabricConditioner || order.useStainRemover || order.useBleach) && (
          <div className="mt-2">
            <strong>Add-ons:</strong>
            <ul className="list-disc ml-5 mt-1">
              {order.useDetergent && (
                <li>Detergent {order.detergentType ? `(${order.detergentType})` : ''} x{order.detergentQuantity || 1}</li>
              )}
              {order.useFabricConditioner && (
                <li>Fabric Conditioner {order.conditionerType ? `(${order.conditionerType})` : ''} x{order.conditionerQuantity || 1}</li>
              )}
              {order.useStainRemover && <li>Stain Remover</li>}
              {order.useBleach && <li>Bleach Treatment</li>}
            </ul>
          </div>
        )}
        {order.notes && (
          <div className="mt-2">
            <strong>Notes:</strong> {order.notes}
          </div>
        )}
      </div>
    </div>
  );
}
