
import { UseFormReturn } from "react-hook-form";

/**
 * Extract all form errors into a standardized format
 */
export function extractFormErrors(form: UseFormReturn<any>) {
  // Log key information for debugging
  const orderType = form.getValues("orderType") || "walk-in";
  console.log("extractFormErrors - Order type:", orderType);
  
  // Get all errors from the form
  const formErrors = form.formState.errors;
  console.log("extractFormErrors - Raw form errors:", formErrors);
  
  // Format the errors into a standardized structure
  const errorMessages: Array<{ field: string; message: string }> = [];
  
  // Process each error to create standardized error messages
  Object.entries(formErrors).forEach(([field, error]) => {
    // For walk-in orders, exclude clientId validation
    if (orderType === "walk-in" && field === "clientId") {
      console.log("Skipping clientId validation for walk-in order");
      return; // Skip this error for walk-in orders
    }
    
    // Include all valid errors
    if (error && error.message && typeof error.message === "string") {
      errorMessages.push({
        field,
        message: error.message
      });
    }
  });
  
  console.log("extractFormErrors - Filtered error messages:", errorMessages);
  
  return {
    errorMessages,
    hasErrors: errorMessages.length > 0 // Base hasErrors on filtered messages, not raw errors
  };
}

/**
 * Log form errors to console for debugging
 */
export function logFormErrors(form: UseFormReturn<any>) {
  const errors = form.formState.errors;
  if (Object.keys(errors).length > 0) {
    console.log("Form validation errors:", errors);
    
    Object.entries(errors).forEach(([field, error]) => {
      console.log(`Field '${field}' error:`, error);
    });
  }
}
