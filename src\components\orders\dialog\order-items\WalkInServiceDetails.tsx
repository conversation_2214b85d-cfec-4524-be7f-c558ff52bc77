
import { Order } from "@/types";
import { parseServiceItems } from "./utils";

interface WalkInServiceDetailsProps {
  order: Order;
}

export function WalkInServiceDetails({ order }: WalkInServiceDetailsProps) {
  const serviceItems = parseServiceItems(order);
  const hasServiceItems = serviceItems && serviceItems.length > 0;
  
  // Determine display name for service type
  const getServiceTypeName = () => {
    if (!order.serviceType) return 'Standard Service';
    
    // Format the service type for display
    return order.serviceType.replace(/_/g, ' ').toUpperCase();
  };
  
  return (
    <div className="space-y-2">
      <h4 className="text-sm font-medium">Walk-in Service Details</h4>
      
      <div className="text-sm border rounded-md p-3 bg-green-50">
        <div className="flex justify-between py-1">
          <span className="font-medium">{getServiceTypeName()}</span>
          <span>₱{order.subtotalBeforeVAT?.toFixed(2) || order.amount.toFixed(2)}</span>
        </div>
        
        {/* Show customer information */}
        <div className="mt-1 text-sm">
          <span className="font-medium">Customer: </span>
          {order.customer?.name || "Walk-in Customer"}
          {order.customer?.phone ? ` • ${order.customer.phone}` : ""}
        </div>
        
        {/* Show weight/pieces information */}
        <div className="mt-1 text-sm text-muted-foreground">
          {order.weightKilos ? `Weight: ${order.weightKilos} kg` : ''}
          {order.weightKilos && order.numberOfPieces ? ' · ' : ''}
          {order.numberOfPieces ? `Pieces: ${order.numberOfPieces}` : ''}
        </div>
        
        {/* Display service items with improved handling */}
        <div className="mt-3 border-t pt-2">
          <p className="font-medium mb-1">Service Items:</p>
          {hasServiceItems ? (
            <ul className="list-disc list-inside space-y-1 pl-2">
              {serviceItems.map((item, idx) => (
                <li key={idx} className="text-sm">
                  {item.name || item.type} 
                  {item.quantity > 1 ? ` × ${item.quantity}` : ''} 
                  {item.unitPrice > 0 ? ` - ₱${item.unitPrice.toFixed(2)}` : ''}
                  {item.treatmentDescription && (
                    <span className="text-xs text-muted-foreground ml-1">
                      ({item.treatmentDescription})
                    </span>
                  )}
                </li>
              ))}
            </ul>
          ) : (
            <div className="text-sm text-muted-foreground">
              <p>No specific service items</p>
            </div>
          )}
        </div>
        
        {/* Show add-ons section if any are enabled */}
        {(order.useDetergent || order.useFabricConditioner || order.useStainRemover || order.useBleach) && (
          <div className="mt-3 border-t pt-2">
            <p className="font-medium mb-1">Add-ons:</p>
            <ul className="text-sm text-muted-foreground pl-2">
              {order.useDetergent && (
                <li>Detergent {order.detergentQuantity ? `× ${order.detergentQuantity}` : ''}</li>
              )}
              {order.useFabricConditioner && (
                <li>Fabric Conditioner {order.conditionerQuantity ? `× ${order.conditionerQuantity}` : ''}</li>
              )}
              {order.useStainRemover && <li>Stain Removal Treatment</li>}
              {order.useBleach && <li>Bleach Treatment</li>}
            </ul>
          </div>
        )}
        
        {/* Show VAT information if present */}
        {order.vatAmount > 0 && (
          <div className="flex justify-between pt-2 mt-2 border-t">
            <span>VAT ({(order.vatAmount / (order.subtotalBeforeVAT || 1) * 100).toFixed(0)}%):</span>
            <span>₱{order.vatAmount.toFixed(2)}</span>
          </div>
        )}
        
        {/* Show total */}
        <div className="flex justify-between pt-2 mt-2 border-t font-medium">
          <span>Total:</span>
          <span>₱{order.amount.toFixed(2)}</span>
        </div>
      </div>
    </div>
  );
}
