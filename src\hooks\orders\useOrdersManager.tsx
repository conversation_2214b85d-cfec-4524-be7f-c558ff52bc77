import { useState } from "react";
import { Order } from "@/types";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { auditService } from "@/services/audit/auditService";

export function useOrdersManager(orderList: Order[]) {
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [isViewOrderOpen, setIsViewOrderOpen] = useState(false);
  const { toast } = useToast();

  // Handle order status change
  const handleStatusChange = async (orderId: string, newStatus: string): Promise<void> => {
    console.log(`Updating order ${orderId} status to ${newStatus}`);
    
    try {
      // Find the order to update
      const order = orderList.find(o => o.id === orderId);
      if (!order) {
        throw new Error(`Order ${orderId} not found`);
      }
      
      // If we have a UUID, use that for the database operation
      const dbId = order.uuid || orderId;
      
      // Update in Supabase
      const { error } = await supabase
        .from('orders')
        .update({ status: newStatus })
        .eq('id', dbId);
        
      if (error) {
        console.error('Error updating order in Supabase:', error);
        throw error;
      }
      
      toast({
        title: "Order Updated",
        description: `Order ${orderId} status updated to ${newStatus}`,
      });
      
      // Update the selected order if it's the one that was changed
      if (selectedOrder?.id === orderId) {
        setSelectedOrder({
          ...selectedOrder,
          status: newStatus
        });
      }
      
      // Dispatch event for real-time update
      window.dispatchEvent(new CustomEvent('order-status-updated', { 
        detail: { orderId, newStatus } 
      }));
      
    } catch (error) {
      console.error('Error updating order status:', error);
      toast({
        title: "Update Failed",
        description: "Could not update order status",
        variant: "destructive",
      });
      throw error; // Re-throw to allow calling code to handle it
    }
  };

  // Handle batch status update for multiple orders
  const handleBatchStatusUpdate = async (orderIds: string[], newStatus: string): Promise<void> => {
    console.log(`Batch updating ${orderIds.length} orders to status: ${newStatus}`);
    
    try {
      // Update each order one by one
      for (const orderId of orderIds) {
        await handleStatusChange(orderId, newStatus);
      }
      
      toast({
        title: "Batch Update Complete",
        description: `Updated ${orderIds.length} orders to ${newStatus}`,
      });
    } catch (error) {
      console.error('Error in batch update:', error);
      toast({
        title: "Batch Update Failed",
        description: "Could not update all orders. Please try again.",
        variant: "destructive",
      });
      throw error; // Re-throw to allow calling code to handle it
    }
  };

  const handlePrintOrder = (order: Order) => {
    try {
      // Get add-on quantities from order data
      const detergentQty = order.detergentQuantity || 1;
      const conditionerQty = order.conditionerQuantity || 1;
      
      // Format the order data for printing
      const printContent = `
CMC LAUNDRY
-----------
ORDER SLIP
----------
Order ID: ${order.id}
Date: ${order.orderDate}
Customer: ${order.customer.name}
Phone: ${order.customer.phone}

Items:
${order.lineItems?.map(item => 
  `${item.name} x${item.quantity} - ₱${item.total.toFixed(2)}`
).join('\n') || 'No items'}

Add-ons:
${order.useDetergent ? `- Detergent x${detergentQty}` : ''}
${order.useFabricConditioner ? `- Fabric Conditioner x${conditionerQty}` : ''}
${order.useStainRemover ? '- Stain Remover' : ''}
${order.useBleach ? '- Bleach Treatment' : ''}

Total Amount: ₱${order.amount.toFixed(2)}
Paid Amount: ₱${order.paidAmount.toFixed(2)}
Balance: ₱${(order.amount - order.paidAmount).toFixed(2)}

Status: ${order.status.toUpperCase()}
      `.trim();

      // Send to printer
      const printWindow = window.open('', '', 'width=600,height=600');
      if (!printWindow) {
        throw new Error('Could not open print window');
      }

      printWindow.document.open();
      printWindow.document.write(`
        <html>
          <head>
            <title>Order Slip - ${order.id}</title>
            <style>
              body {
                font-family: monospace;
                font-size: 12px;
                white-space: pre;
                margin: 0;
                padding: 20px;
              }
              @media print {
                body { margin: 0; }
              }
            </style>
          </head>
          <body>${printContent}</body>
        </html>
      `);
      printWindow.document.close();
      printWindow.print();
      printWindow.close();

      toast({
        title: "Print Initiated",
        description: `Order slip for ${order.id} has been sent to printer`,
      });
    } catch (error) {
      console.error('Print error:', error);
      toast({
        title: "Print Failed",
        description: "Could not print the order slip. Please try again.",
        variant: "destructive",
      });
    }
  };

  // New function to handle order deletion
  const handleDeleteOrder = async (orderId: string): Promise<boolean> => {
    try {
      console.log(`Deleting order ${orderId}`);
      
      // Find the order to delete
      const order = orderList.find(o => o.id === orderId);
      if (!order) {
        throw new Error(`Order ${orderId} not found`);
      }
      
      // If we have a UUID, use that for the database operation
      const dbId = order.uuid || orderId;
      
      // Record in audit log before deletion
      await auditService.logOrderAction('delete', orderId, {
        uuid: dbId,
        reference: order.id,
        deleted_at: new Date().toISOString(),
      });
      
      // Delete from Supabase
      const { error } = await supabase
        .from('orders')
        .delete()
        .eq('id', dbId);
        
      if (error) {
        console.error('Error deleting order from Supabase:', error);
        throw error;
      }
      
      toast({
        title: "Order Deleted",
        description: `Order ${orderId} has been successfully deleted`,
      });
      
      // Clear selected order if it was the one that was deleted
      if (selectedOrder?.id === orderId) {
        setSelectedOrder(null);
        setIsViewOrderOpen(false);
      }
      
      // Dispatch event for real-time update
      window.dispatchEvent(new CustomEvent('order-deleted', { 
        detail: { orderId } 
      }));
      
      return true;
    } catch (error) {
      console.error('Error deleting order:', error);
      toast({
        title: "Delete Failed",
        description: "Could not delete the order",
        variant: "destructive",
      });
      return false;
    }
  };

  return {
    selectedOrder,
    setSelectedOrder,
    isViewOrderOpen,
    setIsViewOrderOpen,
    handleStatusChange,
    handleBatchStatusUpdate,
    handlePrintOrder,
    handleDeleteOrder
  };
}
