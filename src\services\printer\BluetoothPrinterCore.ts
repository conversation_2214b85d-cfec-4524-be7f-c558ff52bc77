
import { COMMANDS, BLUETOOTH_SERVICES, BLUETOOTH_CHARACTERISTICS, PRINTER_FILTERS } from './constants';
import { PrinterStatus } from './types';
import { BluetoothDevice, BluetoothRemoteGATTCharacteristic, getBluetoothNavigator } from './bluetooth-types';

export class BluetoothPrinterCore {
  private device: BluetoothDevice | null = null;
  private characteristic: BluetoothRemoteGATTCharacteristic | null = null;
  private statusListeners: ((status: PrinterStatus) => void)[] = [];
  private status: PrinterStatus = 'disconnected';
  
  // Check if Web Bluetooth API is available
  public isSupported(): boolean {
    const bluetoothNav = getBluetoothNavigator();
    return 'bluetooth' in bluetoothNav && bluetoothNav.bluetooth !== undefined;
  }
  
  // Get current connection status
  public getStatus(): PrinterStatus {
    return this.status;
  }
  
  // Add a listener for status changes
  public onStatusChange(callback: (status: PrinterStatus) => void): () => void {
    this.statusListeners.push(callback);
    return () => {
      this.statusListeners = this.statusListeners.filter(listener => listener !== callback);
    };
  }
  
  // Update status and notify listeners
  protected updateStatus(newStatus: PrinterStatus): void {
    this.status = newStatus;
    this.statusListeners.forEach(listener => listener(newStatus));
  }
  
  // Discover and connect to a printer
  public async connectPrinter(): Promise<boolean> {
    try {
      if (!this.isSupported()) {
        console.error('Web Bluetooth is not supported in this browser');
        this.updateStatus('error');
        return false;
      }
      
      this.updateStatus('connecting');
      
      const bluetoothNav = getBluetoothNavigator();
      this.device = await bluetoothNav.bluetooth?.requestDevice({
        filters: PRINTER_FILTERS,
        optionalServices: BLUETOOTH_SERVICES
      }) || null;
      
      if (!this.device) {
        throw new Error('No printer selected');
      }
      
      console.log('Printer selected:', this.device.name);
      
      // Connect to GATT server
      const server = await this.device.gatt?.connect();
      if (!server) {
        throw new Error('Could not connect to GATT server');
      }
      
      // Get primary service - try multiple service UUIDs
      let service;
      for (const serviceId of BLUETOOTH_SERVICES) {
        try {
          service = await server.getPrimaryService(serviceId);
          break; // Exit the loop if we found a service
        } catch (e) {
          // Continue to the next service ID
          console.log(`Service ${serviceId} not found, trying next...`);
        }
      }
      
      if (!service) {
        throw new Error('Could not find printer service');
      }
      
      // Get characteristic - try multiple characteristic UUIDs
      for (const characteristicId of BLUETOOTH_CHARACTERISTICS) {
        try {
          this.characteristic = await service.getCharacteristic(characteristicId);
          break; // Exit the loop if we found a characteristic
        } catch (e) {
          // Continue to the next characteristic ID
          console.log(`Characteristic ${characteristicId} not found, trying next...`);
        }
      }
      
      if (!this.characteristic) {
        throw new Error('Could not find printer characteristic');
      }
      
      // Setup disconnect listener
      this.device.addEventListener('gattserverdisconnected', () => {
        console.log('Printer disconnected');
        this.characteristic = null;
        this.updateStatus('disconnected');
      });
      
      // Store the printer ID in localStorage for future connections
      localStorage.setItem('lastConnectedPrinter', this.device.id);
      
      this.updateStatus('connected');
      return true;
    } catch (error) {
      console.error('Error connecting to printer:', error);
      this.updateStatus('error');
      return false;
    }
  }
  
  // Disconnect from printer
  public async disconnect(): Promise<void> {
    if (this.device && this.device.gatt?.connected) {
      this.device.gatt.disconnect();
    }
    this.device = null;
    this.characteristic = null;
    this.updateStatus('disconnected');
  }
  
  // Send raw data to printer
  protected async sendData(data: string): Promise<void> {
    if (!this.characteristic) {
      throw new Error('Printer not connected');
    }
    
    const encoder = new TextEncoder();
    const dataArray = encoder.encode(data);
    await this.characteristic.writeValue(dataArray);
  }
  
  // Print data
  public async print(data: string): Promise<boolean> {
    if (!this.characteristic || !this.device?.gatt?.connected) {
      console.error('Printer not connected');
      this.updateStatus('error');
      return false;
    }
    
    try {
      this.updateStatus('printing');
      
      // Initialize printer
      const initCommand = COMMANDS.INIT;
      await this.sendData(initCommand);
      
      // Send data in chunks to avoid buffer overflow
      const dataWithCommands = data + COMMANDS.FEED_LINES(3) + COMMANDS.CUT;
      const chunkSize = 20; // Adjust based on printer buffer size
      
      for (let i = 0; i < dataWithCommands.length; i += chunkSize) {
        const chunk = dataWithCommands.substring(i, i + chunkSize);
        await this.sendData(chunk);
        // Small delay between chunks
        await new Promise(resolve => setTimeout(resolve, 50));
      }
      
      this.updateStatus('connected');
      return true;
    } catch (error) {
      console.error('Error printing:', error);
      this.updateStatus('error');
      return false;
    }
  }
}

// Create a singleton instance
export const bluetoothPrinterCore = new BluetoothPrinterCore();
