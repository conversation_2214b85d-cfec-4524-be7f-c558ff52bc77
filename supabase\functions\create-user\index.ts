
// Follow Deno deploy pattern: https://deno.com/deploy/docs/deployctl
// supabase/functions/create-user/index.ts
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.5';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface RequestBody {
  email: string;
  password: string;
  role?: string;
  first_name?: string;
  last_name?: string;
  phone?: string;
  position?: string;
  client_id?: string;
}

Deno.serve(async (req) => {
  // Handle CORS preflight request
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  // Get request body
  const body: RequestBody = await req.json();
  
  try {
    // Create a Supabase client with the Admin key
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      { auth: { autoRefreshToken: false, persistSession: false } }
    );
    
    // Create the user
    const { data, error } = await supabaseAdmin.auth.admin.createUser({
      email: body.email,
      password: body.password,
      email_confirm: true, // Auto-confirm the email
      user_metadata: {
        first_name: body.first_name,
        last_name: body.last_name
      }
    });
    
    if (error) {
      throw error;
    }

    const userId = data.user.id;
    
    // Update the profile
    await supabaseAdmin
      .from('profiles')
      .update({
        first_name: body.first_name || null,
        last_name: body.last_name || null,
        phone: body.phone || null,
        position: body.position || null,
      })
      .eq('id', userId);

    // Set the user role if provided
    if (body.role) {
      await supabaseAdmin
        .from('user_roles')
        .insert({
          user_id: userId,
          role: body.role,
        });
    }

    // Link to client if client_id provided
    if (body.client_id) {
      await supabaseAdmin
        .from('users_clients')
        .insert({
          user_id: userId,
          client_id: body.client_id,
        });
    }

    // Return the user ID
    return new Response(
      JSON.stringify({ userId }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200 
      }
    );
  } catch (error) {
    console.error('Error creating user:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400 
      }
    );
  }
});
