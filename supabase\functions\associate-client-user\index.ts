
// supabase/functions/associate-client-user/index.ts
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.5';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface RequestBody {
  userId: string;
  clientId: string;
}

Deno.serve(async (req) => {
  // Handle CORS preflight request
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Get authorization header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      throw new Error('Authorization header is required');
    }

    // Get request body
    const body: RequestBody = await req.json();
    
    if (!body.userId || !body.clientId) {
      throw new Error('userId and clientId are required');
    }

    // Create a Supabase client with the Service Role key
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      { auth: { autoRefreshToken: false, persistSession: false } }
    );

    // Check if the association already exists
    const { data: existingAssociation, error: checkError } = await supabaseAdmin
      .from('users_clients')
      .select('id')
      .eq('user_id', body.userId)
      .eq('client_id', body.clientId)
      .maybeSingle();
      
    if (checkError) {
      throw checkError;
    }
    
    if (existingAssociation) {
      return new Response(
        JSON.stringify({ success: true, message: 'Association already exists', id: existingAssociation.id }),
        { 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200 
        }
      );
    }

    // Create the association using the service role (bypasses RLS)
    const { data, error } = await supabaseAdmin
      .from('users_clients')
      .insert({
        user_id: body.userId,
        client_id: body.clientId
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    return new Response(
      JSON.stringify({ success: true, id: data.id }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 201 
      }
    );
  } catch (error) {
    console.error('Error associating client with user:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400 
      }
    );
  }
});
