
import { ClientLoginInfo } from "../ClientLoginInfo";
import { ClientWithUser } from "./types";

interface ClientLoginInfoGridProps {
  successfulCreations: ClientWithUser[];
}

export function ClientLoginInfoGrid({ successfulCreations }: ClientLoginInfoGridProps) {
  return (
    <div className="mt-8 grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {successfulCreations.map((client) => (
        <ClientLoginInfo 
          key={client.client.id}
          clientName={client.client.name}
          email={client.email}
        />
      ))}
    </div>
  );
}
