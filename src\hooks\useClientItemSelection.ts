
import { useState, useEffect } from "react";
import { UseFormReturn } from "react-hook-form";
import { ClientItem } from "@/services/clientItem";
import { ClientItemWithQuantity, OrderFormValues } from "@/components/orders/OrderFormTypes";

export function useClientItemsSelection(form: UseFormReturn<OrderFormValues>) {
  const [selectedItems, setSelectedItems] = useState<ClientItemWithQuantity[]>([]);
  const DEFAULT_UNIT_PRICE = 0; // Only used as a fallback if item.unit_price is undefined

  // Update local state when form values change
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === "selectedClientItems" || name === undefined) {
        const items = form.getValues("selectedClientItems") || [];
        setSelectedItems(items);
      }
    });
    
    return () => subscription.unsubscribe();
  }, [form]);

  // Initialize from form values
  useEffect(() => {
    const initialItems = form.getValues("selectedClientItems") || [];
    setSelectedItems(initialItems);
  }, [form]);

  const handleItemToggle = (item: ClientItem, isSelected: boolean) => {
    let updatedItems: ClientItemWithQuantity[];
    
    if (isSelected) {
      // Get the unit price from the database or use the default
      const unitPrice = item.unit_price || DEFAULT_UNIT_PRICE;
      
      // Add item with its actual unit_price from the database and calculate the total
      updatedItems = [
        ...selectedItems,
        {
          id: item.id,
          name: item.name,
          item_type: item.item_type || 'standard', // Store item_type as property
          quantity: 1,
          unitPrice: unitPrice, // Use unitPrice instead of unit_price
          treatments: {
            useStainRemoval: false,
            useBeachTreatment: false,
            detergentType: 'none',
            conditionerType: 'none'
          },
          instanceId: crypto.randomUUID() // Generate a unique ID
        }
      ];
    } else {
      // Remove all instances of this item
      updatedItems = selectedItems.filter(i => i.id !== item.id);
    }
    
    console.log("Items after toggle:", updatedItems);
    setSelectedItems(updatedItems);
    form.setValue("selectedClientItems", updatedItems);
  };

  const updateItemQuantity = (instanceId: string, newQuantity: number) => {
    if (newQuantity < 1) return;
    
    const updatedItems = selectedItems.map(item => {
      if (item.instanceId === instanceId) {
        return { ...item, quantity: newQuantity };
      }
      return item;
    });
    
    setSelectedItems(updatedItems);
    form.setValue("selectedClientItems", updatedItems);
  };

  const updateItemTreatments = (instanceId: string, treatmentUpdates: Partial<ClientItemWithQuantity['treatments']>) => {
    const updatedItems = selectedItems.map(item => {
      if (item.instanceId === instanceId) {
        return {
          ...item,
          treatments: {
            ...item.treatments,
            ...treatmentUpdates
          }
        };
      }
      return item;
    });
    
    setSelectedItems(updatedItems);
    form.setValue("selectedClientItems", updatedItems);
  };

  const duplicateItem = (instanceId: string) => {
    const itemToDuplicate = selectedItems.find(item => item.instanceId === instanceId);
    if (!itemToDuplicate) return;

    const duplicatedItem: ClientItemWithQuantity = {
      ...itemToDuplicate,
      instanceId: crypto.randomUUID(), // Generate a new unique ID
      treatments: { ...itemToDuplicate.treatments } // Copy treatments
    };

    const updatedItems = [...selectedItems, duplicatedItem];
    setSelectedItems(updatedItems);
    form.setValue("selectedClientItems", updatedItems);
  };

  const removeItemInstance = (instanceId: string) => {
    const updatedItems = selectedItems.filter(item => item.instanceId !== instanceId);
    setSelectedItems(updatedItems);
    form.setValue("selectedClientItems", updatedItems);
  };

  return {
    selectedItems,
    handleItemToggle,
    updateItemQuantity,
    updateItemTreatments,
    duplicateItem,
    removeItemInstance,
    DEFAULT_UNIT_PRICE
  };
}
