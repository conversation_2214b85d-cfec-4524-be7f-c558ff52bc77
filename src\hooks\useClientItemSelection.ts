
import { useState, useEffect } from "react";
import { UseFormReturn } from "react-hook-form";
import { ClientItem } from "@/services/clientItem";
import { ClientItemWithQuantity, OrderFormValues } from "@/components/orders/OrderFormTypes";

export function useClientItemsSelection(form: UseFormReturn<OrderFormValues>) {
  const [selectedItems, setSelectedItems] = useState<ClientItemWithQuantity[]>([]);
  const DEFAULT_UNIT_PRICE = 0; // Only used as a fallback if item.unit_price is undefined

  // Update local state when form values change
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === "selectedClientItems" || name === undefined) {
        const items = form.getValues("selectedClientItems") || [];
        setSelectedItems(items);
      }
    });
    
    return () => subscription.unsubscribe();
  }, [form]);

  // Initialize from form values
  useEffect(() => {
    const initialItems = form.getValues("selectedClientItems") || [];
    setSelectedItems(initialItems);
  }, [form]);

  const handleItemToggle = (item: ClientItem, isSelected: boolean) => {
    let updatedItems: ClientItemWithQuantity[];
    
    if (isSelected) {
      // Get the unit price from the database or use the default
      const unitPrice = item.unit_price || DEFAULT_UNIT_PRICE;
      
      // Add item with its actual unit_price from the database and calculate the total
      updatedItems = [
        ...selectedItems,
        {
          id: item.id,
          name: item.name,
          quantity: 1,
          unit_price: unitPrice,
          total: unitPrice // Initial total equals unit_price * quantity(1)
        }
      ];
    } else {
      // Remove item
      updatedItems = selectedItems.filter(i => i.id !== item.id);
    }
    
    console.log("Items after toggle:", updatedItems);
    setSelectedItems(updatedItems);
    form.setValue("selectedClientItems", updatedItems);
  };

  const updateItemQuantity = (itemId: string, newQuantity: number) => {
    if (newQuantity < 1) return;
    
    const updatedItems = selectedItems.map(item => {
      if (item.id === itemId) {
        const total = item.unit_price * newQuantity;
        return { ...item, quantity: newQuantity, total };
      }
      return item;
    });
    
    setSelectedItems(updatedItems);
    form.setValue("selectedClientItems", updatedItems);
  };

  return {
    selectedItems,
    handleItemToggle,
    updateItemQuantity,
    DEFAULT_UNIT_PRICE
  };
}
