
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

export function useDashboardData() {
  const [orderStats, setOrderStats] = useState({
    pending: 0,
    processing: 0,
    ready: 0,
    delivered: 0,
    total: 0
  });
  const [recentOrders, setRecentOrders] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  const fetchDashboardData = async () => {
    setIsLoading(true);
    
    try {
      // Fetch order stats
      const { data: orderData, error: orderError } = await supabase
        .from('orders')
        .select('status')
        
      if (orderError) throw orderError;

      // Calculate stats
      const stats = {
        pending: 0,
        processing: 0,
        ready: 0,
        delivered: 0,
        total: orderData ? orderData.length : 0
      };

      if (orderData) {
        orderData.forEach(order => {
          if (order.status === 'pending') stats.pending++;
          if (order.status === 'processing') stats.processing++;
          if (order.status === 'ready') stats.ready++;
          if (order.status === 'delivered') stats.delivered++;
        });
      }

      setOrderStats(stats);

      // Fetch recent orders
      const { data: recentOrdersData, error: recentOrdersError } = await supabase
        .from('orders')
        .select(`
          id,
          created_at,
          client_id,
          reference_code,
          status,
          amount,
          customer_type,
          customer_name,
          clients (name)
        `)
        .order('created_at', { ascending: false })
        .limit(5);

      if (recentOrdersError) throw recentOrdersError;

      if (recentOrdersData) {
        const formattedRecentOrders = recentOrdersData.map(order => {
          // Determine customer name based on order type
          let customerName = '';
          
          if (order.customer_type === 'walk-in') {
            // For walk-in orders, use the customer_name field
            customerName = order.customer_name || "Walk-in Customer";
          } else {
            // For client orders, use the client name from the joined clients table
            customerName = order.clients?.name || "Client";
          }
          
          return {
            id: order.reference_code || `ORD-${order.id.substring(0, 4)}`,
            customerName: customerName,
            date: new Date(order.created_at).toLocaleString(),
            status: order.status,
            amount: order.amount,
            customerType: order.customer_type || (order.client_id ? 'client' : 'walk-in')
          };
        });

        setRecentOrders(formattedRecentOrders);
      }
    } catch (error) {
      console.error("Error fetching dashboard data:", error);
      toast({
        title: "Error fetching dashboard data",
        description: "Please try again later",
        variant: "destructive"
      });
      
      // Fallback with sample data
      setRecentOrders([
        {
          id: "ORD-1001",
          customerName: "Metro Hospital",
          date: new Date().toLocaleString(),
          status: "processing",
          amount: 560,
          customerType: "client"
        },
        {
          id: "ORD-1002",
          customerName: "John Smith",
          date: new Date().toLocaleString(),
          status: "pending",
          amount: 250,
          customerType: "walk-in"
        },
        {
          id: "ORD-1003",
          customerName: "Grand Hotel",
          date: new Date(Date.now() - 86400000).toLocaleString(),
          status: "ready",
          amount: 1200,
          customerType: "client"
        }
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  // Set up real-time subscription for orders
  useEffect(() => {
    fetchDashboardData();
    
    // Set up subscription for real-time updates
    const channel = supabase
      .channel('dashboard-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'orders'
        },
        () => {
          fetchDashboardData();
        }
      )
      .subscribe();
    
    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  return {
    orderStats,
    recentOrders,
    isLoading,
    refreshDashboard: fetchDashboardData
  };
}
