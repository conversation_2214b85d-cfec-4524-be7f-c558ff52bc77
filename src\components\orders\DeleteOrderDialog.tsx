
import React, { useState } from "react";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { auditService } from "@/services/audit/auditService";
import { useAuth } from "@/contexts/auth";
import { Loader2 } from "lucide-react";

interface DeleteOrderDialogProps {
  orderId: string;
  orderReference: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onOrderDeleted: () => void;
}

export function DeleteOrderDialog({
  orderId,
  orderReference,
  open,
  onOpenChange,
  onOrderDeleted
}: DeleteOrderDialogProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const { toast } = useToast();
  const { user, userRole } = useAuth();

  const handleDelete = async () => {
    // Only admins can delete orders
    if (userRole !== 'admin') {
      toast({
        title: "Permission Denied",
        description: "Only administrators can delete orders",
        variant: "destructive"
      });
      return;
    }

    setIsDeleting(true);

    try {
      // Record the audit log first before deleting the order
      await auditService.logOrderAction('delete', orderId, {
        reference: orderReference,
        deleted_by: user?.email,
        deleted_at: new Date().toISOString(),
      });

      // Delete the order
      const { error } = await supabase
        .from('orders')
        .delete()
        .eq('id', orderId);

      if (error) {
        throw error;
      }

      toast({
        title: "Order Deleted",
        description: `Order ${orderReference} has been deleted and logged`,
      });

      // Close the dialog and refresh the orders list
      onOpenChange(false);
      onOrderDeleted();
    } catch (error) {
      console.error('Error deleting order:', error);
      toast({
        title: "Delete Failed",
        description: "Could not delete the order. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Delete Order</AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to delete order {orderReference}? This action cannot be undone,
            but will be recorded in the audit log.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={(e) => {
              e.preventDefault();
              handleDelete();
            }}
            className="bg-red-600 hover:bg-red-700"
            disabled={isDeleting}
          >
            {isDeleting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Deleting...
              </>
            ) : (
              "Delete Order"
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
