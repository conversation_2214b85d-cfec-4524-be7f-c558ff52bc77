
import React, { useState } from "react";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { auditService } from "@/services/audit/auditService";
import { useAuth } from "@/contexts/auth";
import { Loader2, X } from "lucide-react";
import { getOrderUuidFromShortId } from "@/services/orders/mutations/helpers";

interface DeleteOrderDialogProps {
  orderId: string;
  orderReference: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onOrderDeleted: () => void;
}

export function DeleteOrderDialog({
  orderId,
  orderReference,
  open,
  onOpenChange,
  onOrderDeleted
}: DeleteOrderDialogProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const { toast } = useToast();
  const { user, userRole } = useAuth();

  const handleDelete = async () => {
    // Only admins can delete orders
    if (userRole !== 'admin') {
      toast({
        title: "Permission Denied",
        description: "Only administrators can delete orders",
        variant: "destructive"
      });
      return;
    }

    setIsDeleting(true);

    try {
      console.log(`Attempting to delete order: ${orderId}, reference: ${orderReference}`);
      
      // Ensure we have a valid UUID for database operations
      let validUuid = orderId;
      
      // If the orderId doesn't look like a UUID, try to retrieve it
      if (!orderId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
        console.log(`Order ID ${orderId} is not a valid UUID, attempting to retrieve UUID`);
        const uuid = await getOrderUuidFromShortId(orderId);
        if (!uuid) {
          throw new Error(`Could not find UUID for order reference: ${orderReference}`);
        }
        validUuid = uuid;
        console.log(`Retrieved UUID ${validUuid} for order reference ${orderReference}`);
      }
      
      // Record the audit log first before deleting the order
      await auditService.logOrderAction('delete', orderReference, {
        reference: orderReference,
        deleted_by: user?.email,
        deleted_at: new Date().toISOString(),
      });

      // Delete the order using the valid UUID
      const { error, data } = await supabase
        .from('orders')
        .delete()
        .eq('id', validUuid)
        .select();

      if (error) {
        console.error('Error deleting order from database:', error);
        throw error;
      }

      console.log('Delete response:', data);
      
      toast({
        title: "Order Deleted",
        description: `Order ${orderReference} has been deleted and logged`,
      });

      // Close the dialog and refresh the orders list
      onOpenChange(false);
      onOrderDeleted();
    } catch (error) {
      console.error('Error deleting order:', error);
      toast({
        title: "Delete Failed",
        description: "Could not delete the order. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <div className="flex items-center justify-between">
          <AlertDialogHeader className="flex-1">
            <AlertDialogTitle>Delete Order</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete order {orderReference}? This action cannot be undone,
              but will be recorded in the audit log.
            </AlertDialogDescription>
          </AlertDialogHeader>
          
          {/* Replace default X close button with a styled button similar to delete button */}
          <Button 
            variant="outline" 
            size="icon" 
            className="rounded-full" 
            onClick={() => onOpenChange(false)}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
        
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={(e) => {
              e.preventDefault();
              handleDelete();
            }}
            className="bg-red-600 hover:bg-red-700"
            disabled={isDeleting}
          >
            {isDeleting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Deleting...
              </>
            ) : (
              "Delete Order"
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
