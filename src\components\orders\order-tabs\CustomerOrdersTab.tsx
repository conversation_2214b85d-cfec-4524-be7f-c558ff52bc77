
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { AddOrderDialog } from "@/components/orders/AddOrderDialog";
import { Order } from "@/types";
import { useIsMobile } from "@/hooks/use-mobile";
import { OrdersDataTable } from "@/components/orders/table/OrdersDataTable";
import { useToast } from "@/hooks/use-toast";
import { useOrdersManager } from "@/hooks/orders/useOrdersManager";
import { OrderFilters } from "../filters/OrderFilters";
import { useOrderFilters } from "@/hooks/orders/useOrderFilters";

interface CustomerOrdersTabProps {
  orders: Order[];
  onOrderAdded: () => void;
}

export function CustomerOrdersTab({
  orders,
  onOrderAdded
}: CustomerOrdersTabProps) {
  const [isAddOrderOpen, setIsAddOrderOpen] = useState(false);
  const isMobile = useIsMobile();
  const { toast } = useToast();
  
  // Filter for walk-in orders only - improved filtering logic
  const customerOrders = orders.filter(order => order.customerType === "walk-in");
  
  console.log(`CustomerOrdersTab: Filtered ${orders.length} orders to ${customerOrders.length} walk-in orders`);
  
  // Use the shared hook to handle order actions
  const {
    handleStatusChange,
    handleBatchStatusUpdate,
    handlePrintOrder
  } = useOrdersManager(customerOrders);
  
  // Use the filter hook
  const {
    searchTerm,
    setSearchTerm,
    statusFilter,
    setStatusFilter,
    dateFilter,
    setDateFilter,
    filteredOrders
  } = useOrderFilters(customerOrders);
  
  return (
    <>
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-4">
        <h2 className={`${isMobile ? "text-base" : "text-lg"} font-semibold`}>Customer Orders</h2>
        <Button 
          className="bg-laundry-blue hover:bg-laundry-darkBlue w-full md:w-auto"
          onClick={() => setIsAddOrderOpen(true)}
        >
          <Plus className="mr-2 h-4 w-4" /> {isMobile ? "New Customer Order" : "Add New Walk-in Order"}
        </Button>
      </div>
      
      <div className="mb-6">
        <OrderFilters
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          statusFilter={statusFilter}
          setStatusFilter={setStatusFilter}
          dateFilter={dateFilter}
          setDateFilter={setDateFilter}
          isClientView={false}
        />
      </div>
      
      <OrdersDataTable 
        data={filteredOrders} 
        filterType="walk-in"
        onStatusChange={handleStatusChange}
        onBatchStatusChange={handleBatchStatusUpdate}
        onPrintOrder={handlePrintOrder}
      />
      
      <AddOrderDialog 
        open={isAddOrderOpen} 
        onOpenChange={setIsAddOrderOpen} 
        onOrderAdded={onOrderAdded}
        initialOrderType="walk-in"
      />
    </>
  );
}
