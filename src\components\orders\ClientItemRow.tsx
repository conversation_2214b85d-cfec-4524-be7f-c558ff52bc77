
import React from "react";
import { ClientItem } from "@/services/clientItemService";
import { ClientItemWithQuantity } from "./OrderFormTypes";
import { TableRow, TableCell } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Plus, Minus } from "lucide-react";

interface ClientItemRowProps {
  item: ClientItem;
  isSelected: boolean;
  selectedItem?: ClientItemWithQuantity;
  onToggle: (item: ClientItem, isSelected: boolean) => void;
  onQuantityChange: (itemId: string, quantity: number) => void;
  unitPrice: number;
  isMobile?: boolean;
}

export function ClientItemRow({
  item,
  isSelected,
  selectedItem,
  onToggle,
  onQuantityChange,
  unitPrice,
  isMobile = false
}: ClientItemRowProps) {
  // Use the selectedItem quantity or default to 1
  const quantity = selectedItem?.quantity || 1;
  
  // Use the unit_price from the database item, not the default value
  const itemUnitPrice = item.unit_price || unitPrice;
  const total = itemUnitPrice * quantity;

  return (
    <TableRow>
      <TableCell className={isMobile ? "px-2" : ""}>
        <Checkbox 
          checked={isSelected} 
          onCheckedChange={(checked) => onToggle(item, !!checked)}
        />
      </TableCell>
      <TableCell className={isMobile ? "px-2" : ""}>
        {item.name}
      </TableCell>
      <TableCell className={isMobile ? "px-1" : ""}>
        {isSelected && (
          <div className={`flex items-center ${isMobile ? "space-x-1" : ""}`}>
            <Button 
              type="button"
              size="sm" 
              variant="outline"
              className={isMobile ? "h-7 w-7 p-0" : "h-8 w-8 p-0"}
              onClick={() => onQuantityChange(item.id, quantity - 1)}
              disabled={quantity <= 1}
            >
              <Minus className={isMobile ? "h-3 w-3" : "h-4 w-4"} />
            </Button>
            <Input 
              className={isMobile ? "h-7 w-10 text-center px-1 mx-1" : "h-8 w-16 mx-2 text-center"}
              type="number"
              value={quantity}
              onChange={(e) => onQuantityChange(item.id, parseInt(e.target.value) || 1)}
              min="1"
            />
            <Button 
              type="button"
              size="sm" 
              variant="outline"
              className={isMobile ? "h-7 w-7 p-0" : "h-8 w-8 p-0"}
              onClick={() => onQuantityChange(item.id, quantity + 1)}
            >
              <Plus className={isMobile ? "h-3 w-3" : "h-4 w-4"} />
            </Button>
          </div>
        )}
      </TableCell>
      <TableCell className={`text-right ${isMobile ? "px-2" : ""}`}>
        {isSelected && (
          <span className={isMobile ? "text-sm" : ""}>₱ {total.toFixed(2)}</span>
        )}
      </TableCell>
    </TableRow>
  );
}
