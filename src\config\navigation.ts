
import { 
  LayoutDashboard, 
  BookText, 
  ListChecks, 
  ShoppingCart, 
  Users, 
  Settings, 
  Building, 
  BarChart,
  History,
  DollarSign
} from "lucide-react";

export const dashboardConfig = {
  mainNav: [{
    title: "Documentation",
    href: "/docs"
  }, {
    title: "Support",
    href: "/support",
    disabled: true
  }],
  sidebarNav: [{
    title: "General",
    items: [{
      title: "Dashboard",
      href: "/dashboard",
      icon: LayoutDashboard,
      staffAccess: true
    }, {
      title: "Orders",
      href: "/orders",
      icon: ShoppingCart,
      staffAccess: true
    }, {
      title: "Customers",
      href: "/customers",
      icon: Users,
      staffAccess: true
    }, {
      title: "Clients",
      href: "/clients",
      icon: Building,
      staffAccess: true
    }, {
      title: "Inventory",
      href: "/inventory",
      icon: ListChecks,
      staffAccess: true
    }, {
      title: "Expenses",
      href: "/expenses",
      icon: DollarSign,
      staffAccess: true
    }, {
      title: "Accounting",
      path: "/accounting",
      icon: BookText,
      staffAccess: false
    }, {
      title: "Reports",
      href: "/reports",
      icon: <PERSON><PERSON><PERSON>,
      staffAccess: false  // Changed from true to false
    }]
  }, {
    title: "Settings",
    items: [{
      title: "Settings",
      href: "/settings",
      icon: Settings,
      staffAccess: false  // Changed to false as only admins should access Settings
    }, {
      title: "Audit Logs",
      href: "/audit-logs",
      icon: History,
      staffAccess: false  // Only admins can access
    }]
  }]
};
