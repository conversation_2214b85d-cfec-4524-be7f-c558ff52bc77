
/**
 * Processes selected client items into line items format
 */
export function processLineItems(orderData: any): any[] {
  // If no client items, return empty array
  if (!orderData.selectedClientItems || !orderData.selectedClientItems.length) {
    return orderData.lineItems || [];
  }
  
  // Format client items for storage
  return orderData.selectedClientItems.map((item: any) => ({
    name: item.name || '',
    quantity: parseInt(item.quantity) || 1,
    unitPrice: parseFloat(item.unit_price) || parseFloat(item.unitPrice) || 0,
    total: (parseFloat(item.unit_price || item.unitPrice) || 0) * (parseInt(item.quantity) || 1)
  }));
}
