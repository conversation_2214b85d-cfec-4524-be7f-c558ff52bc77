import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import { useIsMobile } from "@/hooks/use-mobile";
import {
  Settings2,
  Store,
  Bell,
  User,
  CreditCard,
  Receipt,
  Lock,
  Smartphone,
  Mail,
  Save,
  Clock,
  Printer,
  AlertCircle
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { BluetoothPrinterSettings } from "@/components/printer/BluetoothPrinterSettings";
import { UsersManagement } from "@/components/settings/users/UsersManagement";

// Import Badge for user roles
import { Badge } from "@/components/ui/badge";
import { Plus } from "lucide-react";

// Import Table components for the user management section
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

export default function SettingsPage() {
  const { toast } = useToast();
  const isMobile = useIsMobile();
  const [storeName, setStoreName] = useState("PVOSyncPOS Laundry");
  const [email, setEmail] = useState("<EMAIL>");
  const [phone, setPhone] = useState("+63 ************");
  const [address, setAddress] = useState("123 Laundry St., Metro Manila, Philippines");
  
  const [notifications, setNotifications] = useState({
    orders: true,
    marketing: false,
    reminders: true,
    inventory: true,
  });

  const handleSaveSettings = () => {
    toast({
      title: "Settings Saved",
      description: "Your changes have been successfully saved.",
    });
  };

  // Get URL parameter to determine active tab
  const [activeTab, setActiveTab] = useState("business");
  
  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const tab = params.get('tab');
    if (tab && ['business', 'notifications', 'users', 'payment', 'printing'].includes(tab)) {
      setActiveTab(tab);
    }
  }, []);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold tracking-tight">Settings</h1>
        <Button onClick={handleSaveSettings}>
          <Save className="mr-2 h-4 w-4" /> Save Changes
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className={`grid grid-cols-5 w-full ${isMobile ? 'text-xs' : ''}`}>
          <TabsTrigger value="business" className="flex items-center gap-1">
            <Store className="h-4 w-4" />
            {!isMobile ? "Business" : ""}
          </TabsTrigger>
          <TabsTrigger value="notifications" className="flex items-center gap-1">
            <Bell className="h-4 w-4" />
            {!isMobile ? "Notifications" : ""}
          </TabsTrigger>
          <TabsTrigger value="users" className="flex items-center gap-1">
            <User className="h-4 w-4" />
            {!isMobile ? "Users" : ""}
          </TabsTrigger>
          <TabsTrigger value="payment" className="flex items-center gap-1">
            <CreditCard className="h-4 w-4" />
            {!isMobile ? "Payment" : ""}
          </TabsTrigger>
          <TabsTrigger value="printing" className="flex items-center gap-1">
            <Printer className="h-4 w-4" />
            {!isMobile ? "Receipts" : ""}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="business">
          <Card>
            <CardHeader>
              <CardTitle>Business Information</CardTitle>
              <CardDescription>
                Manage your business details and profile
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="storeName">Store Name</Label>
                  <Input 
                    id="storeName"
                    value={storeName}
                    onChange={(e) => setStoreName(e.target.value)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="email">Email Address</Label>
                  <Input 
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="phone">Phone Number</Label>
                  <Input 
                    id="phone"
                    value={phone}
                    onChange={(e) => setPhone(e.target.value)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="address">Business Address</Label>
                  <Input 
                    id="address"
                    value={address}
                    onChange={(e) => setAddress(e.target.value)}
                  />
                </div>
              </div>
              
              <Separator />
              
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Business Hours</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Monday - Friday</Label>
                    <div className="flex items-center gap-2">
                      <Input defaultValue="8:00 AM" className="w-24" />
                      <span>to</span>
                      <Input defaultValue="7:00 PM" className="w-24" />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>Saturday</Label>
                    <div className="flex items-center gap-2">
                      <Input defaultValue="9:00 AM" className="w-24" />
                      <span>to</span>
                      <Input defaultValue="5:00 PM" className="w-24" />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>Sunday</Label>
                    <div className="flex items-center gap-2">
                      <Input defaultValue="10:00 AM" className="w-24" />
                      <span>to</span>
                      <Input defaultValue="3:00 PM" className="w-24" />
                    </div>
                  </div>
                </div>
              </div>
              
              <Separator />
              
              <div>
                <h3 className="text-lg font-medium mb-4">Tax Settings</h3>
                <div className="flex items-center space-x-2 mb-4">
                  <Switch id="tax-enabled" defaultChecked />
                  <Label htmlFor="tax-enabled">Enable Tax Calculation</Label>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="tax-name">Tax Name</Label>
                    <Input id="tax-name" defaultValue="VAT" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="tax-rate">Tax Rate (%)</Label>
                    <Input id="tax-rate" type="number" defaultValue="12" />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notifications">
          <Card>
            <CardHeader>
              <CardTitle>Notification Settings</CardTitle>
              <CardDescription>
                Configure how you receive notifications
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium mb-4">Email Notifications</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-muted-foreground" />
                        <Label htmlFor="email-orders">Order Updates</Label>
                      </div>
                      <Switch 
                        id="email-orders" 
                        checked={notifications.orders}
                        onCheckedChange={(checked) => 
                          setNotifications({...notifications, orders: checked})
                        }
                      />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-muted-foreground" />
                        <Label htmlFor="email-marketing">Marketing & Promotions</Label>
                      </div>
                      <Switch 
                        id="email-marketing"
                        checked={notifications.marketing}
                        onCheckedChange={(checked) => 
                          setNotifications({...notifications, marketing: checked})
                        }
                      />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-muted-foreground" />
                        <Label htmlFor="email-reminders">Customer Reminders</Label>
                      </div>
                      <Switch 
                        id="email-reminders"
                        checked={notifications.reminders}
                        onCheckedChange={(checked) => 
                          setNotifications({...notifications, reminders: checked})
                        }
                      />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-muted-foreground" />
                        <Label htmlFor="email-inventory">Inventory Alerts</Label>
                      </div>
                      <Switch 
                        id="email-inventory"
                        checked={notifications.inventory}
                        onCheckedChange={(checked) => 
                          setNotifications({...notifications, inventory: checked})
                        }
                      />
                    </div>
                  </div>
                </div>
                
                <Separator />
                
                <div>
                  <h3 className="text-lg font-medium mb-4">SMS Notifications</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Smartphone className="h-4 w-4 text-muted-foreground" />
                        <Label htmlFor="sms-orders">Order Updates</Label>
                      </div>
                      <Switch id="sms-orders" />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Smartphone className="h-4 w-4 text-muted-foreground" />
                        <Label htmlFor="sms-reminders">Customer Reminders</Label>
                      </div>
                      <Switch id="sms-reminders" />
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="users">
          <Card>
            <CardHeader>
              <CardTitle>User Management</CardTitle>
              <CardDescription>
                Manage staff accounts and permissions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <UsersManagement />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="payment">
          <Card>
            <CardHeader>
              <CardTitle>Payment Methods</CardTitle>
              <CardDescription>
                Configure accepted payment methods
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <CreditCard className="h-4 w-4 text-muted-foreground" />
                      <Label htmlFor="payment-cash">Cash</Label>
                    </div>
                    <Switch id="payment-cash" defaultChecked />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <CreditCard className="h-4 w-4 text-muted-foreground" />
                      <Label htmlFor="payment-card">Credit/Debit Card</Label>
                    </div>
                    <Switch id="payment-card" defaultChecked />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <CreditCard className="h-4 w-4 text-muted-foreground" />
                      <Label htmlFor="payment-gcash">GCash</Label>
                    </div>
                    <Switch id="payment-gcash" defaultChecked />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <CreditCard className="h-4 w-4 text-muted-foreground" />
                      <Label htmlFor="payment-maya">Maya</Label>
                    </div>
                    <Switch id="payment-maya" defaultChecked />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <CreditCard className="h-4 w-4 text-muted-foreground" />
                      <Label htmlFor="payment-bank">Bank Transfer</Label>
                    </div>
                    <Switch id="payment-bank" />
                  </div>
                </div>
                
                <Separator />
                
                <div>
                  <h3 className="text-lg font-medium mb-4">Payment Settings</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Receipt className="h-4 w-4 text-muted-foreground" />
                        <Label htmlFor="setting-receipt">Generate Receipt Automatically</Label>
                      </div>
                      <Switch id="setting-receipt" defaultChecked />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Lock className="h-4 w-4 text-muted-foreground" />
                        <Label htmlFor="setting-approval">Require Approval for Refunds</Label>
                      </div>
                      <Switch id="setting-approval" defaultChecked />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        <Label htmlFor="setting-timeout">Enable Payment Timeout (5 minutes)</Label>
                      </div>
                      <Switch id="setting-timeout" />
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="printing">
          <Card>
            <CardHeader>
              <CardTitle>Receipt Settings</CardTitle>
              <CardDescription>
                Configure receipts and printing options
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="receipt-header">Receipt Header</Label>
                    <Input 
                      id="receipt-header"
                      defaultValue="PVOSyncPOS Laundry Services"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="receipt-footer">Receipt Footer</Label>
                    <Input 
                      id="receipt-footer"
                      defaultValue="Thank you for your business!"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="receipt-notes">Additional Notes</Label>
                    <Input 
                      id="receipt-notes"
                      defaultValue="Please keep your receipt for pickup"
                    />
                  </div>
                </div>
                
                <Separator />
                
                <BluetoothPrinterSettings />

                <Separator />
                
                <div>
                  <h3 className="text-lg font-medium mb-4">Receipt Options</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="receipt-logo">Show Logo on Receipt</Label>
                      <Switch id="receipt-logo" defaultChecked />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <Label htmlFor="receipt-tax">Show Tax Details</Label>
                      <Switch id="receipt-tax" defaultChecked />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <Label htmlFor="receipt-date">Show Date and Time</Label>
                      <Switch id="receipt-date" defaultChecked />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <Label htmlFor="receipt-cashier">Show Cashier Name</Label>
                      <Switch id="receipt-cashier" defaultChecked />
                    </div>
                  </div>
                </div>
                
                <Separator />
                
                <div>
                  <h3 className="text-lg font-medium mb-4">Printer Settings</h3>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="printer-name">Default Printer</Label>
                      <Input 
                        id="printer-name"
                        defaultValue="GOOJPRT JP-58H"
                      />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <Label htmlFor="printer-auto">Auto-Print Receipts</Label>
                      <Switch id="printer-auto" defaultChecked />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="printer-copies">Number of Copies</Label>
                      <Input 
                        id="printer-copies"
                        type="number"
                        defaultValue="2"
                        min="1"
                        max="5"
                      />
                    </div>
                  </div>
                </div>
                
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
