
import { Skeleton } from '@/components/ui/skeleton';
import { formatCurrency } from '@/lib/formatters';

interface StatCardProps {
  title: string;
  value: number;
  isLoading: boolean;
  valueType: 'number' | 'currency';
}

export function StatCard({ title, value, isLoading, valueType }: StatCardProps) {
  // For currency values, ensure they're never negative
  const displayValue = valueType === 'currency' 
    ? Math.max(0, value)
    : value;
    
  const formattedValue = valueType === 'currency' 
    ? formatCurrency(displayValue)
    : displayValue.toString();

  return (
    <div className="bg-muted/50 rounded-lg p-4">
      <div className="text-sm text-muted-foreground">{title}</div>
      {isLoading ? (
        <Skeleton className="h-8 w-24 mt-1" />
      ) : (
        <div className="text-2xl font-bold mt-1">{formattedValue}</div>
      )}
    </div>
  );
}
