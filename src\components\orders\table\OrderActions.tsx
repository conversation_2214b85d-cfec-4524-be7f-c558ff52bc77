
import { Button } from "@/components/ui/button";
import { Eye, Printer, FileText, Trash2, Edit } from "lucide-react";
import { Order } from "@/types";
import { useToast } from "@/hooks/use-toast";
import { usePrinterContext } from "@/contexts/PrinterContext";
import { useState } from "react";
import { useAuth } from "@/contexts/auth";
import { DeleteOrderDialog } from "../DeleteOrderDialog";

interface OrderActionsProps {
  order: Order;
  onViewOrder: (order: Order) => void;
  onEditOrder?: (order: Order) => void;
  onOrderDeleted?: () => void;
}

export function OrderActions({ order, onViewOrder, onEditOrder, onOrderDeleted }: OrderActionsProps) {
  const { toast } = useToast();
  const { printerStatus, printReceipt, printJobOrder, showPrinterConnect } = usePrinterContext();
  const [isPrinting, setIsPrinting] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const { userRole } = useAuth();
  
  const isAdmin = userRole === 'admin';
  const canEdit = userRole === 'admin' || userRole === 'staff';
  const isStaff = userRole === 'staff' || userRole === 'admin';

  const handlePrintReceipt = async () => {
    const useBluetoothPrinter = localStorage.getItem('useBluetoothPrinter') === 'true';
    
    // If Bluetooth printing is enabled and the printer is connected
    if (useBluetoothPrinter && printerStatus === 'connected') {
      try {
        setIsPrinting(true);
        const success = await printReceipt(order);
        
        if (success) {
          toast({
            title: "Print Successful",
            description: `Customer receipt for ${order.id} has been sent to printer`,
          });
        } else {
          throw new Error('Printing failed');
        }
      } catch (error) {
        console.error('Print error:', error);
        toast({
          title: "Print Failed",
          description: "Could not print the receipt. Please try again.",
          variant: "destructive",
        });
        
        // Fall back to browser-based printing
        fallbackBrowserPrint('receipt');
      } finally {
        setIsPrinting(false);
      }
    }
    // If Bluetooth printing is enabled but printer is not connected
    else if (useBluetoothPrinter && printerStatus !== 'connected') {
      toast({
        title: "Printer Not Connected",
        description: "Connect your Bluetooth printer in Settings",
        variant: "destructive",
        action: (
          <Button variant="outline" size="sm" onClick={showPrinterConnect}>
            Connect
          </Button>
        )
      });
      
      // Fall back to browser-based printing
      fallbackBrowserPrint('receipt');
    }
    // Use browser-based printing
    else {
      fallbackBrowserPrint('receipt');
    }
  };

  const handlePrintJobOrder = async () => {
    const useBluetoothPrinter = localStorage.getItem('useBluetoothPrinter') === 'true';
    
    // If Bluetooth printing is enabled and the printer is connected
    if (useBluetoothPrinter && printerStatus === 'connected') {
      try {
        setIsPrinting(true);
        const success = await printJobOrder(order);
        
        if (success) {
          toast({
            title: "Print Successful",
            description: `Job order for ${order.id} has been sent to printer`,
          });
        } else {
          throw new Error('Printing failed');
        }
      } catch (error) {
        console.error('Print error:', error);
        toast({
          title: "Print Failed",
          description: "Could not print the job order. Please try again.",
          variant: "destructive",
        });
        
        // Fall back to browser-based printing
        fallbackBrowserPrint('jobOrder');
      } finally {
        setIsPrinting(false);
      }
    }
    // If Bluetooth printing is enabled but printer is not connected
    else if (useBluetoothPrinter && printerStatus !== 'connected') {
      toast({
        title: "Printer Not Connected",
        description: "Connect your Bluetooth printer in Settings",
        variant: "destructive",
        action: (
          <Button variant="outline" size="sm" onClick={showPrinterConnect}>
            Connect
          </Button>
        )
      });
      
      // Fall back to browser-based printing
      fallbackBrowserPrint('jobOrder');
    }
    // Use browser-based printing
    else {
      fallbackBrowserPrint('jobOrder');
    }
  };

  const fallbackBrowserPrint = (type: 'receipt' | 'jobOrder') => {
    try {
      // Get add-on quantities from order data
      const detergentQty = order.detergentQuantity || 1;
      const conditionerQty = order.conditionerQuantity || 1;
      
      // Format the order data for printing
      let printContent = '';
      let title = '';
      
      if (type === 'receipt') {
        title = 'Customer Receipt';
        printContent = `
CMC LAUNDRY
-----------
ORDER SLIP
----------
Order ID: ${order.id}
Date: ${order.orderDate}
Customer: ${order.customer.name}
Phone: ${order.customer.phone}

Items:
${order.lineItems?.map(item => 
  `${item.name} x${item.quantity} - ₱${item.total.toFixed(2)}`
).join('\n') || 'No items'}

Weight: ${order.weightKilos || 0} kg
Pieces: ${order.numberOfPieces || 0}

Add-ons:
${order.useDetergent ? `- Detergent x${detergentQty}` : ''}
${order.useFabricConditioner ? `- Fabric Conditioner x${conditionerQty}` : ''}
${order.useStainRemover ? '- Stain Remover' : ''}
${order.useBleach ? '- Bleach Treatment' : ''}

Subtotal: ₱${order.subtotalBeforeVAT?.toFixed(2) || '0.00'}
VAT: ₱${order.vatAmount?.toFixed(2) || '0.00'}
Total Amount: ₱${order.amount.toFixed(2)}
Paid Amount: ₱${order.paidAmount.toFixed(2)}
Balance: ₱${(order.amount - order.paidAmount).toFixed(2)}

Status: ${order.status.toUpperCase()}
        `.trim();
      } else {
        title = 'Job Order';
        printContent = `
CMC LAUNDRY
-----------
JOB ORDER
----------
Order ID: ${order.id}
Date: ${order.orderDate}
Service: ${order.serviceType?.toUpperCase() || "REGULAR SERVICE"}
Customer: ${order.customer.name}
Phone: ${order.customer.phone}

ITEMS TO PROCESS:
${order.lineItems?.map(item => 
  `- ${item.name} x${item.quantity}`
).join('\n') || 'No specific items'}

SPECIFICATIONS:
Weight: ${order.weightKilos || 0} kg
Pieces: ${order.numberOfPieces || 0}

PROCESSING INSTRUCTIONS:
${order.useDetergent ? `☐ Use Detergent: ${order.detergentType || "Standard"}\n  Quantity: ${detergentQty}` : ''}
${order.useFabricConditioner ? `☐ Use Fabric Conditioner: ${order.conditionerType || "Standard"}\n  Quantity: ${conditionerQty}` : ''}
${order.useStainRemover ? '☐ Apply Stain Remover Treatment' : ''}
${order.useBleach ? '☐ Apply Bleach Treatment' : ''}

${order.notes ? `SPECIAL NOTES:\n${order.notes}` : ''}

STAFF PROCESSING CHECKLIST:
☐ Sorting Complete
☐ Pre-treatment Applied
☐ Washing Complete
☐ Drying Complete
☐ Folding Complete
☐ Quality Check
☐ Ready for Pickup

Assigned Staff: ________________

Status: ${order.status.toUpperCase()}
        `.trim();
      }

      // Send to browser printer
      const printWindow = window.open('', '', 'width=600,height=600');
      if (!printWindow) {
        throw new Error('Could not open print window');
      }

      printWindow.document.open();
      printWindow.document.write(`
        <html>
          <head>
            <title>${title} - ${order.id}</title>
            <style>
              body {
                font-family: monospace;
                font-size: 12px;
                white-space: pre;
                margin: 0;
                padding: 20px;
              }
              @media print {
                body { margin: 0; }
              }
            </style>
          </head>
          <body>${printContent}</body>
        </html>
      `);
      printWindow.document.close();
      printWindow.print();
      printWindow.close();

      toast({
        title: "Print Initiated",
        description: `${title} for ${order.id} has been sent to printer`,
      });
    } catch (error) {
      console.error('Print error:', error);
      toast({
        title: "Print Failed",
        description: "Could not print the document. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <>
      <div className="flex justify-end gap-1">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => onViewOrder(order)}
          title="View Order"
        >
          <Eye className="h-4 w-4" />
        </Button>
        
        {/* Edit button - only for admin and staff */}
        {onEditOrder && canEdit && (
          <Button
            variant="ghost"
            size="icon"
            onClick={() => onEditOrder(order)}
            title="Edit Order"
          >
            <Edit className="h-4 w-4" />
          </Button>
        )}
        
        {/* Customer Receipt Button */}
        <Button
          variant="ghost"
          size="icon"
          onClick={handlePrintReceipt}
          title="Print Customer Receipt"
          disabled={isPrinting}
        >
          <Printer className={`h-4 w-4 ${isPrinting ? 'animate-pulse' : ''}`} />
        </Button>

        {/* Job Order Button - only for staff and admin */}
        {isStaff && (
          <Button
            variant="ghost"
            size="icon"
            onClick={handlePrintJobOrder}
            title="Print Job Order for Staff"
            disabled={isPrinting}
          >
            <FileText className={`h-4 w-4 ${isPrinting ? 'animate-pulse' : ''}`} />
          </Button>
        )}
        
        {/* Delete button - only for admin users */}
        {isAdmin && (
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsDeleteDialogOpen(true)}
            title="Delete Order"
            className="text-red-500 hover:text-red-700 hover:bg-red-100"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        )}
      </div>
      
      {/* Delete Order Dialog */}
      <DeleteOrderDialog
        orderId={order.uuid || order.id}
        orderReference={order.id}
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        onOrderDeleted={() => onOrderDeleted?.()}
      />
    </>
  );
}
