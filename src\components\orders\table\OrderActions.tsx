
import { useState } from "react";
import { Order } from "@/types";
import { useAuth } from "@/contexts/auth";
import { DeleteOrderDialog } from "../DeleteOrderDialog";
import { ActionButtons } from "./components/ActionButtons";
import { PrintButtons } from "./components/PrintButtons";

interface OrderActionsProps {
  order: Order;
  onViewOrder: (order: Order) => void;
  onEditOrder?: (order: Order) => void;
  onOrderDeleted?: () => void;
}

export function OrderActions({ 
  order, 
  onViewOrder, 
  onEditOrder, 
  onOrderDeleted 
}: OrderActionsProps) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const { userRole } = useAuth();
  
  const isAdmin = userRole === 'admin';
  const canEdit = userRole === 'admin' || userRole === 'staff';
  const isStaff = userRole === 'admin' || userRole === 'staff';

  return (
    <>
      <div className="flex justify-end gap-1">
        {/* View and Edit buttons */}
        <ActionButtons 
          order={order}
          onViewOrder={onViewOrder}
          onEditOrder={onEditOrder}
          canEdit={canEdit}
          isAdmin={isAdmin}
          onDeleteClick={() => setIsDeleteDialogOpen(true)}
        />
        
        {/* Print buttons */}
        <PrintButtons 
          order={order}
          isStaff={isStaff}
        />
      </div>
      
      {/* Delete Order Dialog - pass the UUID for database operations */}
      <DeleteOrderDialog
        orderId={order.uuid || order.id}
        orderReference={order.id}
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        onOrderDeleted={() => onOrderDeleted?.()}
      />
    </>
  );
}
