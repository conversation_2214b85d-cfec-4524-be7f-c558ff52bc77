
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { CopyIcon, CheckIcon } from "lucide-react";
import { useState } from "react";

interface ClientLoginInfoProps {
  clientName: string;
  email: string;
}

export function ClientLoginInfo({ clientName, email }: ClientLoginInfoProps) {
  const [copied, setCopied] = useState(false);
  
  const handleCopy = () => {
    const loginInfo = `
Hello ${clientName},

Your account for the CMC Laundry client portal has been created.

Login details:
- Email: ${email}
- Password: Client1234

For security reasons, please log in and change your password immediately.

You can access the client portal at: ${window.location.origin}

Thank you for your business.
`;
    
    navigator.clipboard.writeText(loginInfo);
    setCopied(true);
    
    setTimeout(() => setCopied(false), 2000);
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Client Login Information</CardTitle>
        <CardDescription>
          Share these login details with {clientName}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <h4 className="text-sm font-medium mb-1">Email</h4>
          <p className="text-sm">{email}</p>
        </div>
        <div>
          <h4 className="text-sm font-medium mb-1">Password</h4>
          <p className="text-sm">Client1234</p>
        </div>
        <div className="bg-muted p-3 rounded-md">
          <p className="text-sm">
            Please remind the client to change their password after first login.
          </p>
        </div>
      </CardContent>
      <CardFooter>
        <Button
          variant="outline"
          className="w-full"
          onClick={handleCopy}
        >
          {copied ? (
            <>
              <CheckIcon className="h-4 w-4 mr-2" />
              Copied!
            </>
          ) : (
            <>
              <CopyIcon className="h-4 w-4 mr-2" />
              Copy Login Information
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
