
import React from "react";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";

interface DetergentSectionProps {
  useDetergent: boolean;
  detergentType: 'none' | 'regular' | 'color';
  detergentQuantity: string;
  onDetergentChange: (checked: boolean) => void;
  onDetergentTypeChange: (value: 'none' | 'regular' | 'color') => void;
  onDetergentQuantityChange: (value: string) => void;
}

export function DetergentSection({
  useDetergent,
  detergentType,
  detergentQuantity,
  onDetergentChange,
  onDetergentTypeChange,
  onDetergentQuantityChange
}: DetergentSectionProps) {
  return (
    <div className="border rounded-md p-4 bg-white">
      <div className="flex items-center gap-2">
        <Checkbox 
          id="use-detergent" 
          checked={useDetergent}
          onCheckedChange={onDetergentChange}
        />
        <Label htmlFor="use-detergent" className="font-medium">Use Detergent</Label>
      </div>
      
      {useDetergent && (
        <div className="mt-3 grid grid-cols-2 gap-3">
          <div>
            <Label htmlFor="detergent-type">Detergent Type</Label>
            <Select 
              value={detergentType} 
              onValueChange={onDetergentTypeChange}
              disabled={!useDetergent}
            >
              <SelectTrigger id="detergent-type">
                <SelectValue placeholder="Select detergent type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="regular">Regular Detergent</SelectItem>
                <SelectItem value="color">Color-safe Detergent</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="detergent-qty">Quantity</Label>
            <Input
              id="detergent-qty"
              type="number"
              min="1"
              value={detergentQuantity}
              onChange={(e) => onDetergentQuantityChange(e.target.value)}
              disabled={!useDetergent}
            />
          </div>
        </div>
      )}
    </div>
  );
}
