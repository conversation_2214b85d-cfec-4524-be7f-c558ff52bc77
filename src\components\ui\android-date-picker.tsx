import React, { useState } from 'react';
import { CalendarIcon, ChevronLeft, ChevronRight } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

interface AndroidDatePickerProps {
  value?: Date;
  onChange: (date: Date) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  minDate?: Date;
}

export function AndroidDatePicker({
  value,
  onChange,
  placeholder = "Select date",
  disabled = false,
  className,
  minDate
}: AndroidDatePickerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [currentMonth, setCurrentMonth] = useState(value || new Date());

  const handleToggle = (e: React.MouseEvent | React.TouchEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!disabled) {
      console.log('AndroidDatePicker toggle:', { isOpen, value });
      setIsOpen(!isOpen);
    }
  };

  const handleDateSelect = (date: Date) => {
    console.log('AndroidDatePicker date selected:', date);
    onChange(date);
    setIsOpen(false);
  };

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      setIsOpen(false);
    }
  };

  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    const days = [];
    
    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null);
    }
    
    // Add all days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(new Date(year, month, day));
    }
    
    return days;
  };

  const isDateDisabled = (date: Date | null) => {
    if (!date) return true;
    if (minDate && date < minDate) return true;
    return false;
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newMonth = new Date(currentMonth);
    if (direction === 'prev') {
      newMonth.setMonth(newMonth.getMonth() - 1);
    } else {
      newMonth.setMonth(newMonth.getMonth() + 1);
    }
    setCurrentMonth(newMonth);
  };

  const days = getDaysInMonth(currentMonth);
  const weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  return (
    <div className="relative">
      {/* Trigger Button */}
      <button
        type="button"
        className={cn(
          "w-full h-12 px-3 text-left border border-gray-300 rounded-md",
          "bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary",
          "flex items-center justify-between touch-action-manipulation",
          "active:scale-[0.98] transition-all duration-150",
          !value && "text-gray-500",
          disabled && "opacity-50 cursor-not-allowed",
          className
        )}
        onClick={handleToggle}
        onTouchEnd={handleToggle}
        disabled={disabled}
      >
        <span className="flex items-center">
          <CalendarIcon className="h-4 w-4 mr-2 text-gray-400" />
          {value ? format(value, 'PPP') : placeholder}
        </span>
      </button>

      {/* Calendar Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 z-[9999] bg-black bg-opacity-50 flex items-center justify-center p-4"
          onClick={handleOverlayClick}
          style={{ touchAction: 'manipulation' }}
        >
          <div
            className="bg-white rounded-lg shadow-xl max-w-sm w-full p-4 touch-action-manipulation"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Month Navigation */}
            <div className="flex items-center justify-between mb-4">
              <button
                type="button"
                className="p-2 hover:bg-gray-100 rounded-md touch-action-manipulation active:scale-95"
                onClick={() => navigateMonth('prev')}
              >
                <ChevronLeft className="h-5 w-5" />
              </button>
              <h3 className="text-lg font-semibold">
                {format(currentMonth, 'MMMM yyyy')}
              </h3>
              <button
                type="button"
                className="p-2 hover:bg-gray-100 rounded-md touch-action-manipulation active:scale-95"
                onClick={() => navigateMonth('next')}
              >
                <ChevronRight className="h-5 w-5" />
              </button>
            </div>

            {/* Week Days */}
            <div className="grid grid-cols-7 gap-1 mb-2">
              {weekDays.map((day) => (
                <div key={day} className="text-center text-sm font-medium text-gray-500 py-2">
                  {day}
                </div>
              ))}
            </div>

            {/* Calendar Days */}
            <div className="grid grid-cols-7 gap-1">
              {days.map((date, index) => {
                const isDisabled = isDateDisabled(date);
                const isSelected = date && value && 
                  date.getDate() === value.getDate() &&
                  date.getMonth() === value.getMonth() &&
                  date.getFullYear() === value.getFullYear();

                return (
                  <button
                    key={index}
                    type="button"
                    className={cn(
                      "h-10 w-10 text-sm rounded-md touch-action-manipulation",
                      "flex items-center justify-center transition-all duration-150",
                      "active:scale-95",
                      date && !isDisabled
                        ? "hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary"
                        : "cursor-not-allowed",
                      isSelected && "bg-primary text-primary-foreground hover:bg-primary/90",
                      isDisabled && "text-gray-300"
                    )}
                    onClick={() => date && !isDisabled && handleDateSelect(date)}
                    disabled={!date || isDisabled}
                  >
                    {date?.getDate()}
                  </button>
                );
              })}
            </div>

            {/* Close Button */}
            <div className="mt-4 flex justify-end">
              <button
                type="button"
                className="px-4 py-2 text-sm bg-gray-100 hover:bg-gray-200 rounded-md touch-action-manipulation active:scale-95"
                onClick={() => setIsOpen(false)}
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
