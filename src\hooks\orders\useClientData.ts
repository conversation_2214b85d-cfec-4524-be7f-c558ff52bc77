
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { getCurrentUser } from '@/lib/auth';
import { useToast } from '@/hooks/use-toast';
import { getClientById } from '@/services/clients/queries/getClientById';
import { Client } from '@/services/clients/types';
import { ensureClientAssociation } from '@/services/clients/helpers/ensureClientAssociation';

export const useClientData = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [clientId, setClientId] = useState<string | null>(null);
  const [client, setClient] = useState<Client | null>(null);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    const fetchClientInfo = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        const { data: sessionData } = await supabase.auth.getSession();
        if (!sessionData.session) {
          console.log('No active session found');
          setIsLoading(false);
          return;
        }
        
        const user = await getCurrentUser();
        if (!user) {
          console.log('No user found');
          setIsLoading(false);
          return;
        }

        console.log('Checking client association for user:', user.id);
        
        // Try to ensure client association (especially for special emails)
        const associatedClientId = await ensureClientAssociation(user);
        if (associatedClientId) {
          console.log('Found/created client association:', associatedClientId);
          setClientId(associatedClientId);
          
          // Fetch the client details
          const clientData = await getClientById(associatedClientId);
          if (clientData) {
            console.log('Retrieved client data:', clientData.name);
            setClient(clientData);
          }
          setIsLoading(false);
          return;
        }
        
        // If no association was ensured, try the regular flow
        const { data: userClientData, error } = await supabase
          .from('users_clients')
          .select('client_id')
          .eq('user_id', user.id)
          .maybeSingle();
          
        if (error) {
          console.error('Error fetching client info:', error);
          
          // Special email handling as direct fallback
          if (user.email === '<EMAIL>' || user.email === '<EMAIL>') {
            console.log('Special email detected, trying direct client lookup');
            
            const { data: clientsByEmail, error: clientsError } = await supabase
              .from('clients')
              .select('id, name')
              .ilike('email', user.email)
              .maybeSingle();
              
            if (!clientsError && clientsByEmail) {
              console.log('Found client by email match:', clientsByEmail);
              setClientId(clientsByEmail.id);
              
              // Try to create association via edge function
              try {
                const { error: fnError } = await supabase.functions.invoke('associate-client-user', {
                  body: {
                    userId: user.id,
                    clientId: clientsByEmail.id
                  }
                });
                
                if (fnError) {
                  console.error("Error creating association via edge function:", fnError);
                } else {
                  console.log("Successfully created association via edge function");
                }
              } catch (associationErr) {
                console.error("Error calling association edge function:", associationErr);
              }
              
              // Fetch full client details
              const clientData = await getClientById(clientsByEmail.id);
              if (clientData) {
                setClient(clientData);
              }
              setIsLoading(false);
              return;
            } else {
              console.log('No client found by direct email match either, trying domain match');
              
              // Try domain matching as last resort
              const domainPart = user.email.split('@')[1]; // e.g., "bighotel.ph"
              const { data: domainMatches } = await supabase
                .from('clients')
                .select('id, name, email')
                .ilike('email', `%${domainPart}%`)
                .limit(1);
                
              if (domainMatches && domainMatches.length > 0) {
                console.log('Found client by domain match:', domainMatches[0]);
                setClientId(domainMatches[0].id);
                
                // Try to create association via edge function
                try {
                  const { error: fnError } = await supabase.functions.invoke('associate-client-user', {
                    body: {
                      userId: user.id,
                      clientId: domainMatches[0].id
                    }
                  });
                  
                  if (fnError) {
                    console.error("Error creating association via edge function:", fnError);
                  } else {
                    console.log("Successfully created association via edge function");
                  }
                } catch (associationErr) {
                  console.error("Error calling association edge function:", associationErr);
                }
                
                // Fetch full client details
                const clientData = await getClientById(domainMatches[0].id);
                if (clientData) {
                  setClient(clientData);
                }
              } else {
                setError('Could not find a client matching your email domain');
              }
            }
          } else {
            setError('Could not retrieve client information');
          }
          setIsLoading(false);
          return;
        }

        if (userClientData?.client_id) {
          console.log('Found client association:', userClientData.client_id);
          setClientId(userClientData.client_id);
          
          // Fetch the client details
          const clientData = await getClientById(userClientData.client_id);
          if (clientData) {
            setClient(clientData);
          }
        } else {
          console.log('No client association found in users_clients table');
          setError('No client association found for your account');
        }
      } catch (error) {
        console.error('Error in fetchClientInfo:', error);
        setError('Failed to load client information');
        toast({
          title: 'Error',
          description: 'Failed to load client information.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchClientInfo();
  }, [toast]);

  return { isLoading, clientId, client, error };
};
