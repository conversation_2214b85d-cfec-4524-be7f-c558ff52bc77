
import { useState } from "react";
import { Order } from "@/types";
import { DeleteOrderDialog } from "../DeleteOrderDialog";
import { EditOrderDialog } from "../EditOrderDialog";

interface OrderDialogsProps {
  order: Order;
  canEdit: boolean;
  onOrderUpdated: () => void;
  onOrderDeleted: () => void;
}

export function OrderDialogs({
  order,
  canEdit,
  onOrderUpdated,
  onOrderDeleted
}: OrderDialogsProps) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  return (
    <>
      {/* Delete Order Dialog */}
      <DeleteOrderDialog
        orderId={order.uuid || order.id}
        orderReference={order.id}
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        onOrderDeleted={onOrderDeleted}
      />
      
      {/* Edit Order Dialog */}
      {canEdit && (
        <EditOrderDialog
          order={order}
          open={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
          onOrderUpdated={onOrderUpdated}
        />
      )}
    </>
  );
}

export const useOrderDialogs = () => {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isEditItemsDialogOpen, setIsEditItemsDialogOpen] = useState(false);
  const [isEditAddOnsDialogOpen, setIsEditAddOnsDialogOpen] = useState(false);
  
  return {
    isDeleteDialogOpen,
    setIsDeleteDialogOpen,
    isEditDialogOpen,
    setIsEditDialogOpen,
    isEditItemsDialogOpen,
    setIsEditItemsDialogOpen,
    isEditAddOnsDialogOpen,
    setIsEditAddOnsDialogOpen
  };
};
