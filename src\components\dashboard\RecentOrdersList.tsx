
import React from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ExternalLink } from "lucide-react";

export interface RecentOrder {
  id: string;
  customerName: string;
  date: string;
  status?: string;
  amount?: number;
  customerType?: "client" | "walk-in"; // Add customer type
}

interface RecentOrdersListProps {
  orders: RecentOrder[];
  onOrderClick?: (orderId: string) => void;
  emptyMessage?: string;
  showCustomerType?: boolean;
}

export function RecentOrdersList({ 
  orders, 
  onOrderClick,
  emptyMessage = "No orders to display",
  showCustomerType = false
}: RecentOrdersListProps) {
  const getStatusColor = (status: string = 'processing') => {
    switch (status) {
      case 'pending':
        return 'bg-gray-100 text-gray-800';
      case 'processing':
        return 'bg-amber-100 text-amber-800';
      case 'ready_for_pickup':
        return 'bg-green-100 text-green-800';
      case 'fulfilled':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getCustomerTypeColor = (type: string = 'client') => {
    return type === 'client' ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800';
  };

  return (
    <div className="space-y-3">
      {orders.length > 0 ? (
        orders.map((order) => (
          <div 
            key={order.id}
            className={`flex items-center p-3 rounded-md border hover:bg-gray-50 ${onOrderClick ? 'cursor-pointer' : ''} transition-colors`}
            onClick={() => onOrderClick && onOrderClick(order.id)}
            role={onOrderClick ? "button" : undefined}
            tabIndex={onOrderClick ? 0 : undefined}
          >
            <div className="mr-3">
              <div className="bg-blue-100 p-2 rounded-md">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-500">
                  <rect width="18" height="18" x="3" y="3" rx="2" ry="2" />
                  <path d="M3 9h18" />
                  <path d="M9 21V9" />
                </svg>
              </div>
            </div>
            <div className="flex-1">
              <p className="font-medium">{order.customerName || "Unknown Customer"}</p>
              <div className="flex items-center gap-2 flex-wrap">
                <Badge variant="outline" className="text-xs">
                  {order.id}
                </Badge>
                {order.status && (
                  <Badge variant="secondary" className={`text-xs ${getStatusColor(order.status)}`}>
                    {order.status === 'fulfilled' ? 'delivery complete' : order.status.replace(/_/g, ' ')}
                  </Badge>
                )}
                {showCustomerType && order.customerType && (
                  <Badge variant="secondary" className={`text-xs ${getCustomerTypeColor(order.customerType)}`}>
                    {order.customerType === 'client' ? 'Client' : 'Walk-in'}
                  </Badge>
                )}
              </div>
            </div>
            <div className="text-right">
              <div className="text-sm text-gray-500">{order.date}</div>
              {order.amount !== undefined && (
                <div className="text-sm font-semibold text-right">₱{order.amount.toFixed(2)}</div>
              )}
              {onOrderClick && (
                <span className="text-xs text-blue-500 flex items-center justify-end mt-1">
                  <ExternalLink className="h-3 w-3 mr-1" /> View Details
                </span>
              )}
            </div>
          </div>
        ))
      ) : (
        <div className="text-center py-4 text-gray-500">
          {emptyMessage}
        </div>
      )}
    </div>
  );
}
