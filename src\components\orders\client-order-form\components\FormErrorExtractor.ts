
// This file is being deprecated in favor of the errorHandlingUtils.ts
// Keeping it for backward compatibility

import { UseFormReturn } from "react-hook-form";
import { OrderFormValues } from "../../OrderFormTypes";
import { extractFormErrors as extractErrorsFromForm } from "../utils/errorHandlingUtils";

export function extractFormErrors(form: UseFormReturn<OrderFormValues>) {
  // Use the improved utility function
  return extractErrorsFromForm(form);
}
