
import React, { useState } from "react";
import { ClientItem } from "@/services/clientItemService";
import { ClientItemWithQuantity } from "../OrderFormTypes";
import { TableRow, TableCell } from "@/components/ui/table";
import { Check } from "lucide-react";
import { calculateItemTotalWithTreatments } from "@/utils/priceCalculations";
import { MainItemRow } from "./MainItemRow";
import { ItemInstanceRow } from "./ItemInstanceRow";

interface ClientItemRowProps {
  item: ClientItem;
  selectedItems: ClientItemWithQuantity[];
  isSelected: boolean;
  onToggle: (item: ClientItem, isSelected: boolean) => void;
  onQuantityChange: (instanceId: string, quantity: number) => void;
  onTreatmentChange: (instanceId: string, treatmentUpdates: Partial<ClientItemWithQuantity['treatments']>) => void;
  onDuplicate: (instanceId: string) => void;
  onRemove: (instanceId: string) => void;
  unitPrice: number;
  isMobile: boolean;
}

export function ClientItemRow({
  item,
  selectedItems,
  isSelected,
  onToggle,
  onQuantityChange,
  onTreatmentChange,
  onDuplicate,
  onRemove,
  unitPrice,
  isMobile
}: ClientItemRowProps) {
  const [expanded, setExpanded] = useState<Record<string, boolean>>({});
  
  // Toggle selection for this item
  const handleToggle = (e: React.MouseEvent<HTMLInputElement>) => {
    e.preventDefault();
    e.stopPropagation();
    onToggle(item, !isSelected);
  };

  // Toggle expanded view for an instance
  const toggleExpand = (instanceId: string) => {
    setExpanded(prev => ({
      ...prev,
      [instanceId]: !prev[instanceId]
    }));
  };

  // Render each instance of the item
  const renderItemInstances = () => {
    if (!isSelected || selectedItems.length === 0) return null;

    return selectedItems.map(instance => (
      <ItemInstanceRow
        key={instance.instanceId}
        instance={instance}
        isExpanded={expanded[instance.instanceId] || false}
        toggleExpand={toggleExpand}
        onQuantityChange={onQuantityChange}
        onTreatmentChange={onTreatmentChange}
        onDuplicate={onDuplicate}
        onRemove={onRemove}
        isMobile={isMobile}
      />
    ));
  };

  return (
    <>
      <MainItemRow 
        item={item}
        isSelected={isSelected}
        selectedItems={selectedItems}
        handleToggle={handleToggle}
        isMobile={isMobile}
      />
      
      {/* Instance rows */}
      {renderItemInstances()}
    </>
  );
}
