
/**
 * Utility class for receipt and document formatting
 */
export class FormattingUtils {
  private LINE_WIDTH: number;

  constructor(lineWidth = 32) {
    this.LINE_WIDTH = lineWidth; // 32 characters for 58mm thermal printers
  }

  /**
   * Format a single line of text to fit within the printer width
   */
  public formatLine(text: string, width = this.LINE_WIDTH): string {
    if (text.length <= width) {
      return text.padEnd(width);
    }
    return text.substring(0, width);
  }

  /**
   * Center text on the line
   */
  public centerText(text: string): string {
    if (text.length >= this.LINE_WIDTH) return text.substring(0, this.LINE_WIDTH);
    const padding = Math.floor((this.LINE_WIDTH - text.length) / 2);
    return ' '.repeat(padding) + text;
  }
  
  /**
   * Get the line width used by formatter
   */
  public getLineWidth(): number {
    return this.LINE_WIDTH;
  }
}
