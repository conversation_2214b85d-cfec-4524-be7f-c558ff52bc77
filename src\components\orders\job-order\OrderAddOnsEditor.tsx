
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { Order } from "@/types";
import { updateOrderAddOns } from "@/services/orders";
import { useOrder } from "@/contexts/OrderContext";
import { DetergentSection } from "./add-ons/DetergentSection";
import { ConditionerSection } from "./add-ons/ConditionerSection";
import { DryCleaningIndicator } from "./add-ons/DryCleaningIndicator";
import { DryCleaningItemsSection } from "./add-ons/DryCleaningItemsSection";
import { SaveAddOnsButton } from "./add-ons/SaveAddOnsButton";
import { AddOnsCategorySection } from "./add-ons/AddOnsCategorySection";
import { useAddOnsState } from "./add-ons/useAddOnsState";
import { SERVICE_TYPES } from "../pricing/constants";
import { DryCleaningItem } from "@/types";

interface OrderAddOnsEditorProps {
  order: Order;
  onOrderUpdated: () => Promise<void>;
}

export function OrderAddOnsEditor({
  order,
  onOrderUpdated
}: OrderAddOnsEditorProps) {
  const { addOnsState, updateAddOnState, handleDetergentChange, handleConditionerChange } = useAddOnsState(order);
  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();
  const { refreshOrder } = useOrder();
  
  const isDryCleaningService = order.serviceType === SERVICE_TYPES.DRY_CLEANING;
  
  // For debugging purposes
  console.log("OrderAddOnsEditor - Order dry cleaning items:", order.dryCleaningItems);
  console.log("OrderAddOnsEditor - AddOnsState dry cleaning items:", addOnsState.dryCleaningItems);
  
  const handleDryCleaningItemsChange = (items: DryCleaningItem[]) => {
    console.log("Dry cleaning items changed:", items);
    updateAddOnState('dryCleaningItems', items);
  };
  
  const handleSaveAddOns = async () => {
    setIsSaving(true);
    try {
      await updateOrderAddOns(order.id, {
        useDetergent: addOnsState.useDetergent,
        useFabricConditioner: addOnsState.useFabricConditioner,
        useStainRemover: addOnsState.useStainRemover,
        useBleach: addOnsState.useBleach,
        detergentQuantity: parseInt(addOnsState.detergentQuantity),
        conditionerQuantity: parseInt(addOnsState.conditionerQuantity),
        // Convert string type to expected enum type
        detergentType: addOnsState.detergentType as "regular" | "color" | "none",
        conditionerType: addOnsState.conditionerType as "regular" | "fresh" | "floral" | "none",
        dryCleaningItems: isDryCleaningService ? addOnsState.dryCleaningItems : []
      });
      
      toast({
        title: "Add-ons Updated",
        description: "Order add-ons have been updated successfully"
      });
      
      await refreshOrder();
      await onOrderUpdated();
    } catch (error) {
      console.error("Failed to update add-ons:", error);
      toast({
        title: "Update Failed",
        description: "Could not update order add-ons",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };
  
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium mb-4">Job Detail</h3>
        
        <div className="space-y-4">
          {isDryCleaningService && (
            <AddOnsCategorySection title="Dry Cleaning Items">
              <DryCleaningIndicator isDryCleaning={true} />
              <DryCleaningItemsSection 
                isDryCleaning={isDryCleaningService} 
                items={order.dryCleaningItems}
                onItemsChange={handleDryCleaningItemsChange}
              />
            </AddOnsCategorySection>
          )}
          
          <AddOnsCategorySection title="Detergent Options">
            <DetergentSection 
              useDetergent={addOnsState.useDetergent} 
              detergentType={addOnsState.detergentType as "regular" | "color" | "none"} 
              detergentQuantity={addOnsState.detergentQuantity} 
              onDetergentChange={handleDetergentChange} 
              onDetergentTypeChange={value => updateAddOnState('detergentType', value)} 
              onDetergentQuantityChange={value => updateAddOnState('detergentQuantity', value)} 
            />
          </AddOnsCategorySection>
          
          <AddOnsCategorySection title="Fabric Conditioner Options">
            <ConditionerSection 
              useFabricConditioner={addOnsState.useFabricConditioner} 
              conditionerType={addOnsState.conditionerType as "regular" | "fresh" | "floral" | "none"} 
              conditionerQuantity={addOnsState.conditionerQuantity} 
              onConditionerChange={handleConditionerChange} 
              onConditionerTypeChange={value => updateAddOnState('conditionerType', value)} 
              onConditionerQuantityChange={value => updateAddOnState('conditionerQuantity', value)}
            />
          </AddOnsCategorySection>
        </div>
      </div>
      
      <div className="flex justify-end">
        <SaveAddOnsButton isSaving={isSaving} onSave={handleSaveAddOns} />
      </div>
    </div>
  );
}
