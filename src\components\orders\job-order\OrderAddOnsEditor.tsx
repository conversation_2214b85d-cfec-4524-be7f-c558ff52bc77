
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { Order } from "@/types";
import { updateOrderAddOns } from "@/services/orders";
import { useOrder } from "@/contexts/OrderContext";
import { DetergentSection } from "./add-ons/DetergentSection";
import { ConditionerSection } from "./add-ons/ConditionerSection";
import { SpecialTreatmentsSection } from "./add-ons/SpecialTreatmentsSection";
import { DryCleaningIndicator } from "./add-ons/DryCleaningIndicator";
import { DryCleaningItemsSection } from "./add-ons/DryCleaningItemsSection";
import { DryCleaningToggle } from "./add-ons/DryCleaningToggle";
import { SaveAddOnsButton } from "./add-ons/SaveAddOnsButton";
import { AddOnsCategorySection } from "./add-ons/AddOnsCategorySection";
import { useAddOnsState } from "./add-ons/useAddOnsState";

interface OrderAddOnsEditorProps {
  order: Order;
  onOrderUpdated: () => Promise<void>;
  isDryCleaning?: boolean;
  setIsDryCleaning?: (value: boolean) => void;
}

export function OrderAddOnsEditor({
  order,
  onOrderUpdated,
  isDryCleaning = false,
  setIsDryCleaning
}: OrderAddOnsEditorProps) {
  const { addOnsState, updateAddOnState, handleDetergentChange, handleConditionerChange } = useAddOnsState(order);
  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();
  const { refreshOrder, updateOrder } = useOrder();
  
  const handleDryCleaningChange = async (checked: boolean) => {
    if (!setIsDryCleaning) return;
    
    setIsDryCleaning(checked);
    setIsSaving(true);
    
    try {
      await updateOrder({
        isDryCleaning: checked
      });
      
      toast({
        title: "Order Updated",
        description: `Dry cleaning option ${checked ? "enabled" : "disabled"}`
      });
      
      await refreshOrder();
      await onOrderUpdated();
    } catch (error) {
      console.error("Failed to update dry cleaning status:", error);
      toast({
        title: "Update Failed",
        description: "Could not update dry cleaning status",
        variant: "destructive"
      });
      // Revert UI state on error
      if (setIsDryCleaning) {
        setIsDryCleaning(!checked);
      }
    } finally {
      setIsSaving(false);
    }
  };
  
  const handleDryCleaningItemsChange = (items: any[]) => {
    updateAddOnState('dryCleaningItems', items);
  };
  
  const handleSaveAddOns = async () => {
    setIsSaving(true);
    try {
      await updateOrderAddOns(order.id, {
        useDetergent: addOnsState.useDetergent,
        useFabricConditioner: addOnsState.useFabricConditioner,
        useStainRemover: addOnsState.useStainRemover,
        useBleach: addOnsState.useBleach,
        detergentQuantity: parseInt(addOnsState.detergentQuantity),
        conditionerQuantity: parseInt(addOnsState.conditionerQuantity),
        detergentType: addOnsState.detergentType,
        conditionerType: addOnsState.conditionerType,
        dryCleaningItems: isDryCleaning ? addOnsState.dryCleaningItems : [],
        isDryCleaning
      });
      
      toast({
        title: "Add-ons Updated",
        description: "Order add-ons have been updated successfully"
      });
      
      await refreshOrder();
      await onOrderUpdated();
    } catch (error) {
      console.error("Failed to update add-ons:", error);
      toast({
        title: "Update Failed",
        description: "Could not update order add-ons",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };
  
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium mb-4">Job Detail</h3>
        
        <div className="space-y-4">
          <AddOnsCategorySection title="Detergent Options">
            <DetergentSection 
              useDetergent={addOnsState.useDetergent} 
              detergentType={addOnsState.detergentType} 
              detergentQuantity={addOnsState.detergentQuantity} 
              onDetergentChange={handleDetergentChange} 
              onDetergentTypeChange={value => updateAddOnState('detergentType', value)} 
              onDetergentQuantityChange={value => updateAddOnState('detergentQuantity', value)} 
            />
          </AddOnsCategorySection>
          
          <AddOnsCategorySection title="Fabric Conditioner Options">
            <ConditionerSection 
              useFabricConditioner={addOnsState.useFabricConditioner} 
              conditionerType={addOnsState.conditionerType} 
              conditionerQuantity={addOnsState.conditionerQuantity} 
              onConditionerChange={handleConditionerChange} 
              onConditionerTypeChange={value => updateAddOnState('conditionerType', value)} 
              onConditionerQuantityChange={value => updateAddOnState('conditionerQuantity', value)}
            />
          </AddOnsCategorySection>
          
          <AddOnsCategorySection title="Special Treatments">
            <SpecialTreatmentsSection 
              useStainRemover={addOnsState.useStainRemover} 
              useBleach={addOnsState.useBleach} 
              isDryCleaning={isDryCleaning}
              onStainRemoverChange={value => updateAddOnState('useStainRemover', value)}
              onBleachChange={value => updateAddOnState('useBleach', value)}
            />
          </AddOnsCategorySection>
          
          {/* Dry Cleaning Toggle - Added below Special Treatments */}
          {setIsDryCleaning && (
            <div className="border-t pt-4 mt-6">
              <DryCleaningToggle 
                isDryCleaning={isDryCleaning}
                isSaving={isSaving}
                onChange={handleDryCleaningChange}
              />
              
              {isDryCleaning && (
                <>
                  <DryCleaningIndicator isDryCleaning={true} />
                  <DryCleaningItemsSection 
                    isDryCleaning={isDryCleaning} 
                    items={addOnsState.dryCleaningItems}
                    onItemsChange={handleDryCleaningItemsChange}
                  />
                </>
              )}
            </div>
          )}
        </div>
      </div>
      
      <div className="flex justify-end">
        <SaveAddOnsButton isSaving={isSaving} onSave={handleSaveAddOns} />
      </div>
    </div>
  );
}
