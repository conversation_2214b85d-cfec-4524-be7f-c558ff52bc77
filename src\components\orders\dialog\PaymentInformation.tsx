
import { Order } from "@/types";

interface PaymentInformationProps {
  order: Order;
}

export function PaymentInformation({ order }: PaymentInformationProps) {
  return (
    <div className="space-y-2">
      <h4 className="text-sm font-medium">Payment Details</h4>
      <div className="text-sm">
        <div>
          <strong>Subtotal:</strong> ₱{order.subtotalBeforeVAT?.toFixed(2) || '0.00'}
        </div>
        <div>
          <strong>VAT:</strong> ₱{order.vatAmount?.toFixed(2) || '0.00'}
        </div>
        <div>
          <strong>Total Amount:</strong> ₱{order.amount?.toFixed(2) || '0.00'}
        </div>
        <div>
          <strong>Paid Amount:</strong> ₱{order.paidAmount?.toFixed(2) || '0.00'}
        </div>
        <div>
          <strong>Balance:</strong> ₱{(order.amount - order.paidAmount)?.toFixed(2) || '0.00'}
        </div>
      </div>
    </div>
  );
}
