
import React, { useState } from "react";
import { Order } from "@/types";
import { Button } from "@/components/ui/button";
import { formatCurrency } from "@/lib/formatters";
import { format } from "date-fns";
import { OrderStatusBadge } from "@/components/orders/OrderStatusBadge";
import { FileText } from "lucide-react";
import { JobOrderProcessDialog } from "./JobOrderProcessDialog";
import { useOrder } from "@/contexts/OrderContext";
interface ClientOrdersListProps {
  orders: Order[];
  searchTerm: string;
  statusFilter: string;
}
export function ClientOrdersList({
  orders
}: ClientOrdersListProps) {
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [isProcessDialogOpen, setIsProcessDialogOpen] = useState(false);
  const {
    dispatch
  } = useOrder();
  const handleProcessOrder = (order: Order) => {
    setSelectedOrder(order);
    dispatch({
      type: 'SET_ORDER',
      payload: order
    });
    setIsProcessDialogOpen(true);
  };

  // Handler for when the order is updated
  const handleOrderUpdated = async (): Promise<void> => {
    console.log("Order was updated, refreshing data could be triggered here");
    return Promise.resolve();
  };
  
  // Sort orders by date (newest first)
  const sortedOrders = [...orders].sort((a, b) => {
    const dateA = new Date(a.orderDate || 0); // Fallback to 0 if orderDate is undefined
    const dateB = new Date(b.orderDate || 0); // Fallback to 0 if orderDate is undefined
    return dateB.getTime() - dateA.getTime();
  });
  
  if (orders.length === 0) {
    return <div className="text-center p-8 border rounded-md bg-white">
        <p className="text-muted-foreground">No orders found matching your criteria.</p>
      </div>;
  }
  return <div className="space-y-4">
      {sortedOrders.map(order => <div key={order.id} className="border rounded-md p-4 bg-white flex flex-col space-y-4">
          <div className="flex justify-between items-start">
            <div>
              <h3 className="font-bold text-lg">{order.reference_code || order.id}</h3>
              <p className="text-sm">Client: {order.customer?.name || 'Unknown'}</p>
            </div>
            <OrderStatusBadge status={order.status} />
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 gap-2 text-sm">
            <div>
              <p className="text-muted-foreground">Order Date:</p>
              <p>{order.orderDate ? format(new Date(order.orderDate), 'MMM d, yyyy') : 'Unknown'}</p>
            </div>
            <div>
              
              
            </div>
            <div className="md:text-right">
              <p className="text-muted-foreground">Items:</p>
              <p>{Array.isArray(order.lineItems) ? order.lineItems.length : 0}</p>
            </div>
          </div>

          <div className="flex justify-between items-center pt-2 mt-2 border-t">
            <div>
              <p className="text-muted-foreground">Amount:</p>
              <p className="font-medium">{formatCurrency(order.amount || 0)}</p>
            </div>
            <Button className="ml-auto" onClick={() => handleProcessOrder(order)}>
              <FileText className="mr-2 h-4 w-4" />
              Process Order
            </Button>
          </div>
        </div>)}

      {/* Process Order Dialog */}
      {selectedOrder && <JobOrderProcessDialog order={selectedOrder} open={isProcessDialogOpen} onOpenChange={setIsProcessDialogOpen} onOrderUpdated={handleOrderUpdated} />}
    </div>;
}
