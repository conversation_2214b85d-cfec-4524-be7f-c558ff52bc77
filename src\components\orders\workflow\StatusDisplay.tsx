
import React from "react";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { getStatusDisplayName, getStatusBadgeStyle } from "./utils/statusUtils";

interface StatusDisplayProps {
  currentStatus: string;
}

export const StatusDisplay: React.FC<StatusDisplayProps> = ({ currentStatus }) => {
  return (
    <div className="flex items-center gap-2">
      <span className="text-sm">Current Status:</span>
      <Badge className={cn("uppercase px-2 py-1", getStatusBadgeStyle(currentStatus))}>
        {getStatusDisplayName(currentStatus)}
      </Badge>
    </div>
  );
};
