
import { Skeleton } from '@/components/ui/skeleton';
import { StatCard } from './StatCard';
import { ReportSummary } from './types';

interface StatSummaryProps {
  summary: ReportSummary;
  isLoading: boolean;
}

export function StatSummary({ summary, isLoading }: StatSummaryProps) {
  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
      <StatCard 
        title="Total Orders" 
        value={summary.totalOrders} 
        isLoading={isLoading}
        valueType="number"
      />
      <StatCard 
        title="Total Amount" 
        value={summary.totalAmount} 
        isLoading={isLoading}
        valueType="currency"
      />
      <StatCard 
        title="Amount Paid" 
        value={summary.totalPaid} 
        isLoading={isLoading}
        valueType="currency"
      />
      <StatCard 
        title="Amount Payable" 
        value={summary.totalPayable} 
        isLoading={isLoading}
        valueType="currency"
      />
    </div>
  );
}
