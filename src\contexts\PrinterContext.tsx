
import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { bluetoothPrinterService, PrinterStatus } from '@/services/printer/BluetoothPrinterService';

interface PrinterContextType {
  printerStatus: PrinterStatus;
  isSupported: boolean;
  connectPrinter: () => Promise<boolean>;
  disconnectPrinter: () => Promise<void>;
  printReceipt: (order: any) => Promise<boolean>;
  printJobOrder: (order: any) => Promise<boolean>;
  showPrinterConnect: () => void;
}

const PrinterContext = createContext<PrinterContextType | undefined>(undefined);

export function PrinterProvider({ children }: { children: ReactNode }) {
  const [printerStatus, setPrinterStatus] = useState<PrinterStatus>(bluetoothPrinterService.getStatus());
  const [isSupported] = useState<boolean>(bluetoothPrinterService.isSupported());
  
  useEffect(() => {
    // Subscribe to printer status changes
    const unsubscribe = bluetoothPrinterService.onStatusChange((status) => {
      setPrinterStatus(status);
    });
    
    // Try to reconnect to last used printer on startup
    const useBluetoothPrinter = localStorage.getItem('useBluetoothPrinter') === 'true';
    if (useBluetoothPrinter && isSupported) {
      // We'll try to reconnect in the background
      bluetoothPrinterService.connectPrinter().catch(err => {
        console.log('Could not auto-reconnect printer', err);
      });
    }
    
    return () => {
      unsubscribe();
    };
  }, [isSupported]);
  
  const connectPrinter = async () => {
    return await bluetoothPrinterService.connectPrinter();
  };
  
  const disconnectPrinter = async () => {
    return await bluetoothPrinterService.disconnect();
  };
  
  const printReceipt = async (order: any) => {
    return await bluetoothPrinterService.printReceipt(order);
  };
  
  const printJobOrder = async (order: any) => {
    return await bluetoothPrinterService.printJobOrder(order);
  };
  
  // Navigate to printer settings
  const showPrinterConnect = () => {
    // Using window.location to navigate to settings with printing tab active
    window.location.href = '/settings?tab=printing';
  };
  
  return (
    <PrinterContext.Provider value={{
      printerStatus,
      isSupported,
      connectPrinter,
      disconnectPrinter,
      printReceipt,
      printJobOrder,
      showPrinterConnect
    }}>
      {children}
    </PrinterContext.Provider>
  );
}

export function usePrinterContext() {
  const context = useContext(PrinterContext);
  if (context === undefined) {
    throw new Error('usePrinterContext must be used within a PrinterProvider');
  }
  return context;
}
