
import { useState, useEffect } from "react";
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON>alogTitle 
} from "@/components/ui/dialog";
import { AddClientItemForm } from "./item-dialog/AddClientItemForm";
import { ItemFormValues } from "./item-dialog/types";
import { getClientItems } from "./ClientData";
import { ClientItem } from "@/services/clientItemService";

interface AddClientItemDialogProps {
  clientId: string;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onAddItem: (data: {name: string, unitPrice: number}) => void;
}

export function AddClientItemDialog({ 
  clientId,
  isOpen, 
  onOpenChange,
  onAddItem
}: AddClientItemDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [existingItems, setExistingItems] = useState<ClientItem[]>([]);
  
  // Fetch existing items when the dialog opens
  useEffect(() => {
    if (isOpen && clientId) {
      const fetchItems = async () => {
        try {
          const items = await getClientItems(clientId);
          setExistingItems(items);
        } catch (error) {
          console.error("Failed to fetch existing items:", error);
        }
      };
      
      fetchItems();
    }
  }, [isOpen, clientId]);
  
  const handleSubmit = (data: ItemFormValues) => {
    setIsSubmitting(true);
    try {
      const itemData = {
        name: data.name.trim(),
        unitPrice: data.unitPrice
      };
      onAddItem(itemData);
    } finally {
      setIsSubmitting(false);
      onOpenChange(false); // Close dialog after submission
    }
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Add Client Item</DialogTitle>
        </DialogHeader>
        
        <AddClientItemForm 
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isSubmitting={isSubmitting}
          existingItems={existingItems}
        />
      </DialogContent>
    </Dialog>
  );
}
