
import React from "react";
import { UseFormReturn } from "react-hook-form";
import { OrderFormValues } from "../../OrderFormTypes";
import { FormSectionWrapper } from "./FormSectionWrapper";

// This component is now deprecated for client order forms
// It's kept for backwards compatibility but will not render anything

interface AddOnsSectionProps {
  form: UseFormReturn<OrderFormValues>;
}

export function AddOnsSection({ form }: AddOnsSectionProps) {
  // Return null to effectively remove this section from the client ordering process
  return null;
}
