
import { useState, useEffect } from "react";
import { Order, DryCleaningItem } from "@/types";
import { SERVICE_TYPES } from "../../pricing/constants";

interface AddOnsState {
  useDetergent: boolean;
  detergentType: 'none' | 'regular' | 'color';
  detergentQuantity: string;
  useFabricConditioner: boolean;
  conditionerType: 'none' | 'regular' | 'fresh' | 'floral';
  conditionerQuantity: string;
  useStainRemover: boolean;
  useBleach: boolean;
  dryCleaningItems: DryCleaningItem[];
}

export function useAddOnsState(order: Order) {
  const [addOnsState, setAddOnsState] = useState<AddOnsState>({
    useDetergent: order.useDetergent || false,
    detergentType: order.useDetergent ? order.detergentType as any || 'regular' : 'none' as const,
    detergentQuantity: order.detergentQuantity?.toString() || "1",
    
    useFabricConditioner: order.useFabricConditioner || false,
    conditionerType: order.useFabricConditioner ? order.conditionerType as any || 'regular' : 'none' as const,
    conditionerQuantity: order.conditionerQuantity?.toString() || "1",
    
    useStainRemover: order.useStainRemover || false,
    useBleach: order.useBleach || false,
    
    dryCleaningItems: order.dryCleaningItems || []
  });
  
  // Update dryCleaningItems when order changes
  useEffect(() => {
    if (order.dryCleaningItems && order.dryCleaningItems.length > 0) {
      console.log("Updating dry cleaning items in useAddOnsState:", order.dryCleaningItems);
      
      // Ensure proper data structure for dry cleaning items
      const validItems = order.dryCleaningItems.map(item => ({
        ...item,
        type: item.type || "",
        quantity: item.quantity || 1,
        price: item.price || 0,
        total: item.total || (item.price * (item.quantity || 1))
      }));
      
      setAddOnsState(prev => ({
        ...prev,
        dryCleaningItems: validItems
      }));
    } else {
      setAddOnsState(prev => ({
        ...prev,
        dryCleaningItems: []
      }));
    }
  }, [order.dryCleaningItems]);
  
  const updateAddOnState = (key: keyof AddOnsState, value: any) => {
    setAddOnsState(prev => ({ ...prev, [key]: value }));
  };
  
  const handleDetergentChange = (checked: boolean) => {
    updateAddOnState('useDetergent', checked);
    if (!checked) {
      updateAddOnState('detergentType', 'none');
    } else if (addOnsState.detergentType === 'none') {
      updateAddOnState('detergentType', 'regular');
    }
  };
  
  const handleConditionerChange = (checked: boolean) => {
    updateAddOnState('useFabricConditioner', checked);
    if (!checked) {
      updateAddOnState('conditionerType', 'none');
    } else if (addOnsState.conditionerType === 'none') {
      updateAddOnState('conditionerType', 'regular');
    }
  };
  
  return {
    addOnsState,
    updateAddOnState,
    handleDetergentChange,
    handleConditionerChange
  };
}
