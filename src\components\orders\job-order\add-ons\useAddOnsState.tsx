
import { useState } from "react";
import { Order } from "@/types";

interface AddOnsState {
  useDetergent: boolean;
  detergentType: 'none' | 'regular' | 'color';
  detergentQuantity: string;
  useFabricConditioner: boolean;
  conditionerType: 'none' | 'regular' | 'fresh' | 'floral';
  conditionerQuantity: string;
  useStainRemover: boolean;
  useBleach: boolean;
  dryCleaningItems: any[];
}

export function useAddOnsState(order: Order) {
  const [addOnsState, setAddOnsState] = useState<AddOnsState>({
    useDetergent: order.useDetergent || false,
    detergentType: order.useDetergent ? order.detergentType as any || 'regular' : 'none' as const,
    detergentQuantity: order.detergentQuantity?.toString() || "1",
    
    useFabricConditioner: order.useFabricConditioner || false,
    conditionerType: order.useFabricConditioner ? order.conditionerType as any || 'regular' : 'none' as const,
    conditionerQuantity: order.conditionerQuantity?.toString() || "1",
    
    useStainRemover: order.useStainRemover || false,
    useBleach: order.useBleach || false,
    
    dryCleaningItems: []
  });
  
  const updateAddOnState = (key: keyof AddOnsState, value: any) => {
    setAddOnsState(prev => ({ ...prev, [key]: value }));
  };
  
  const handleDetergentChange = (checked: boolean) => {
    updateAddOnState('useDetergent', checked);
    if (!checked) {
      updateAddOnState('detergentType', 'none');
    } else if (addOnsState.detergentType === 'none') {
      updateAddOnState('detergentType', 'regular');
    }
  };
  
  const handleConditionerChange = (checked: boolean) => {
    updateAddOnState('useFabricConditioner', checked);
    if (!checked) {
      updateAddOnState('conditionerType', 'none');
    } else if (addOnsState.conditionerType === 'none') {
      updateAddOnState('conditionerType', 'regular');
    }
  };
  
  return {
    addOnsState,
    updateAddOnState,
    handleDetergentChange,
    handleConditionerChange
  };
}
