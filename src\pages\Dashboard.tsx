
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { StatusCard } from "@/components/dashboard/StatusCard";
import { RecentOrdersList } from "@/components/dashboard/RecentOrdersList";
import { useDashboardData } from "@/hooks/useDashboardData";
import { Skeleton } from "@/components/ui/skeleton";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { FileText, Plus } from "lucide-react";
import { AddOrderDialog } from "@/components/orders/AddOrderDialog";
import { useAuth } from "@/contexts/auth";

export default function Dashboard() {
  const { orderStats, recentOrders, isLoading, refreshDashboard } = useDashboardData();
  const navigate = useNavigate();
  const [isOrderDialogOpen, setIsOrderDialogOpen] = useState(false);
  const { userRole } = useAuth();

  // Handle order click to navigate to orders page
  const handleOrderClick = (orderId: string) => {
    navigate(`/orders?id=${orderId}`);
  };

  // Handle new order creation
  const handleNewOrder = () => {
    setIsOrderDialogOpen(true);
  };

  // Handle order added
  const handleOrderAdded = () => {
    refreshDashboard();
  };

  // Handle reports navigation
  const handleViewReports = () => {
    navigate('/reports');
  };

  // Separate orders by customer type
  const clientOrders = recentOrders.filter(order => order.customerType === 'client');
  const walkInOrders = recentOrders.filter(order => order.customerType === 'walk-in');

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mb-4">
        <h1 className="text-2xl font-bold tracking-tight">Dashboard</h1>
        <div className="flex items-center gap-2">
          <Button onClick={handleNewOrder} className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            New Order
          </Button>
          {userRole === 'admin' && (
            <Button 
              variant="outline" 
              onClick={handleViewReports} 
              className="flex items-center gap-2"
            >
              <FileText className="h-4 w-4" />
              Reports
            </Button>
          )}
        </div>
      </div>

      {isLoading ? (
        <>
          {/* Skeleton loader for status cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
                <Skeleton className="h-8 w-32 mb-4" />
                <Skeleton className="h-10 w-16" />
              </div>
            ))}
          </div>

          {/* Skeleton loader for recent orders */}
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
            <Skeleton className="h-8 w-40 mb-4" />
            <div className="space-y-3">
              <Skeleton className="h-16 w-full" />
              <Skeleton className="h-16 w-full" />
              <Skeleton className="h-16 w-full" />
            </div>
          </div>
        </>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <StatusCard 
              title="Pending Orders" 
              count={orderStats.pending} 
              type="pending"
            />
            <StatusCard 
              title="Processing Orders" 
              count={orderStats.processing} 
              type="processing"
            />
            <StatusCard 
              title="Ready to Deliver" 
              count={orderStats.ready} 
              type="ready"
            />
            <StatusCard 
              title="Delivered Orders" 
              count={orderStats.delivered} 
              type="delivered"
            />
          </div>
          
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
            <Tabs defaultValue="all">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">Recent Orders</h2>
                <TabsList>
                  <TabsTrigger value="all">All</TabsTrigger>
                  <TabsTrigger value="client">Client</TabsTrigger>
                  <TabsTrigger value="walkin">Walk-in</TabsTrigger>
                </TabsList>
              </div>
              
              <TabsContent value="all">
                <RecentOrdersList 
                  orders={recentOrders} 
                  onOrderClick={handleOrderClick}
                  emptyMessage="No recent orders found"
                  showCustomerType={true}
                />
              </TabsContent>
              
              <TabsContent value="client">
                <RecentOrdersList 
                  orders={clientOrders} 
                  onOrderClick={handleOrderClick}
                  emptyMessage="No client orders found"
                />
              </TabsContent>
              
              <TabsContent value="walkin">
                <RecentOrdersList 
                  orders={walkInOrders} 
                  onOrderClick={handleOrderClick}
                  emptyMessage="No walk-in orders found"
                />
              </TabsContent>
            </Tabs>
          </div>
        </>
      )}

      <AddOrderDialog 
        open={isOrderDialogOpen} 
        onOpenChange={setIsOrderDialogOpen}
        onOrderAdded={handleOrderAdded}
      />
    </div>
  );
}
