
import React from "react";
import { Order } from "@/types";
import { CustomerInformation } from "./CustomerInformation";
import { OrderDetails } from "./OrderDetails";
import { ItemsSection } from "./ItemsSection";
import { AddOnsSection } from "./AddOnsSection";
import { AdditionalInformation } from "./AdditionalInformation";
import { PaymentInformation } from "./PaymentInformation";
import { OrderStatusWorkflow } from "../workflow/OrderStatusWorkflow";
import { useAuth } from "@/contexts/auth";
import { DryCleaningIndicator } from "../job-order/add-ons/DryCleaningIndicator";
import { SERVICE_TYPES } from "../pricing/constants";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

interface OrderContentProps {
  order: Order;
  onStatusChange?: (orderId: string, newStatus: string) => Promise<void>;
  onOrderUpdated: () => void;
}

export function OrderContent({
  order,
  onStatusChange,
  onOrderUpdated,
}: OrderContentProps) {
  const { userRole } = useAuth();
  const isStaff = userRole === 'staff' || userRole === 'admin';
  const isDryCleaningService = order.serviceType === SERVICE_TYPES.DRY_CLEANING || order.isDryCleaning;
  
  // Format item type for display
  const formatItemType = (type: string) => {
    return type
      .replace(/_/g, ' ')
      .replace(/\w\S*/g, txt => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase());
  };
  
  // Format currency
  const formatCurrency = (amount: number) => {
    return `₱${amount.toFixed(2)}`;
  };
  
  // Count total dry cleaning items
  const totalDryCleaningItems = isDryCleaningService && order.dryCleaningItems ? 
    order.dryCleaningItems.reduce((sum, item) => sum + (item.quantity || 0), 0) : 0;
  
  // Log order dry cleaning details for debugging
  console.log("OrderContent - Dry cleaning details:", {
    orderId: order.id,
    isDryCleaningService,
    isDryCleaning: order.isDryCleaning,
    serviceType: order.serviceType,
    hasDryCleaningItems: Boolean(order.dryCleaningItems && order.dryCleaningItems.length > 0),
    dryCleaningItemsCount: order.dryCleaningItems ? order.dryCleaningItems.length : 0,
    totalDryCleaningItems,
    dryCleaningItems: order.dryCleaningItems
  });
  
  return (
    <div className="grid gap-4 py-4">
      {isDryCleaningService && (
        <DryCleaningIndicator 
          isDryCleaning={true} 
          serviceType={order.serviceType}
          dryCleaningItems={order.dryCleaningItems}
        />
      )}
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <CustomerInformation order={order} />
        <OrderDetails order={order} />
      </div>

      {/* Line Items without Edit Button */}
      <ItemsSection 
        order={order}
        isStaff={isStaff}
        onOrderUpdated={onOrderUpdated}
      />

      {/* Show dry cleaning items in a proper table if they exist */}
      {isDryCleaningService && order.dryCleaningItems && order.dryCleaningItems.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Dry Cleaning Items ({totalDryCleaningItems} pieces)</h4>
          <div className="border rounded-md p-3 bg-amber-50/40">
            <Table>
              <TableHeader className="bg-amber-100/50">
                <TableRow>
                  <TableHead className="w-1/2">Item</TableHead>
                  <TableHead className="text-center">Quantity</TableHead>
                  <TableHead className="text-right">Unit Price</TableHead>
                  <TableHead className="text-right">Total</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {order.dryCleaningItems.map((item, idx) => (
                  <TableRow key={idx} className="border-b border-amber-100/30">
                    <TableCell>{formatItemType(item.type)}</TableCell>
                    <TableCell className="text-center font-medium">{item.quantity || 1}</TableCell>
                    <TableCell className="text-right">{formatCurrency(item.price || 0)}</TableCell>
                    <TableCell className="text-right">
                      {formatCurrency((item.price || 0) * (item.quantity || 1))}
                    </TableCell>
                  </TableRow>
                ))}
                <TableRow className="bg-amber-100/30 font-medium">
                  <TableCell colSpan={3} className="text-right">Total Dry Cleaning:</TableCell>
                  <TableCell className="text-right">
                    {formatCurrency(order.dryCleaningItems.reduce((sum, item) => 
                      sum + ((item.price || 0) * (item.quantity || 1)), 0))}
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
        </div>
      )}

      {/* Add-Ons without Edit Button */}
      <AddOnsSection 
        order={order}
        isStaff={isStaff}
      />

      {/* Order Status Workflow for Staff/Admin */}
      {isStaff && onStatusChange && (
        <OrderStatusWorkflow
          order={order}
          onStatusChange={onStatusChange}
        />
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <AdditionalInformation order={order} />
        <PaymentInformation order={order} />
      </div>
    </div>
  );
}
