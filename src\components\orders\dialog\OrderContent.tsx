
import React from "react";
import { Order } from "@/types";
import { CustomerInformation } from "./CustomerInformation";
import { OrderDetails } from "./OrderDetails";
import { ItemsSection } from "./ItemsSection";
import { AddOnsSection } from "./AddOnsSection";
import { AdditionalInformation } from "./AdditionalInformation";
import { PaymentInformation } from "./PaymentInformation";
import { OrderStatusWorkflow } from "../workflow/OrderStatusWorkflow";
import { useAuth } from "@/contexts/auth";
import { DryCleaningIndicator } from "../job-order/add-ons/DryCleaningIndicator";

interface OrderContentProps {
  order: Order;
  onStatusChange?: (orderId: string, newStatus: string) => Promise<void>;
  onOrderUpdated: () => void;
}

export function OrderContent({
  order,
  onStatusChange,
  onOrderUpdated,
}: OrderContentProps) {
  const { userRole } = useAuth();
  const isStaff = userRole === 'staff' || userRole === 'admin';
  
  return (
    <div className="grid gap-4 py-4">
      {order.isDryCleaning && (
        <DryCleaningIndicator isDryCleaning={true} />
      )}
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <CustomerInformation order={order} />
        <OrderDetails order={order} />
      </div>

      {/* Line Items without Edit Button */}
      <ItemsSection 
        order={order}
        isStaff={isStaff}
        onOrderUpdated={onOrderUpdated}
      />

      {/* Add-Ons without Edit Button */}
      <AddOnsSection 
        order={order}
        isStaff={isStaff}
      />

      {/* Order Status Workflow for Staff/Admin */}
      {isStaff && onStatusChange && (
        <OrderStatusWorkflow
          order={order}
          onStatusChange={onStatusChange}
        />
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <AdditionalInformation order={order} />
        <PaymentInformation order={order} />
      </div>
    </div>
  );
}
