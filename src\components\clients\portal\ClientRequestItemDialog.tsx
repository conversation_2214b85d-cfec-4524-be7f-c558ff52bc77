
import { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>nt, 
  <PERSON><PERSON><PERSON>eader, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
  DialogDescription
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

interface ClientRequestItemDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  clientId: string | null;
  onRequestSubmitted?: () => void;
}

export function ClientRequestItemDialog({ 
  open, 
  onOpenChange,
  clientId, 
  onRequestSubmitted 
}: ClientRequestItemDialogProps) {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [existingItems, setExistingItems] = useState<string[]>([]);
  const [nameError, setNameError] = useState('');
  const { toast } = useToast();

  // Fetch existing items for validation when dialog opens
  useEffect(() => {
    if (open && clientId) {
      const fetchExistingItems = async () => {
        try {
          console.log('Fetching existing items for validation');
          
          // Fetch existing client items
          const { data, error } = await supabase
            .from('client_items')
            .select('name')
            .eq('client_id', clientId);
          
          if (error) throw error;
          
          // Also fetch pending requests to avoid duplicate requests
          const { data: requests, error: requestError } = await supabase
            .from('client_item_requests')
            .select('name')
            .eq('client_id', clientId)
            .eq('status', 'pending');
          
          if (requestError) throw requestError;
          
          // Combine both arrays of names
          const allNames = [
            ...(data || []).map(item => item.name.toLowerCase()),
            ...(requests || []).map(item => item.name.toLowerCase())
          ];
          
          console.log('Found existing items:', allNames);
          setExistingItems(allNames);
        } catch (error) {
          console.error('Error fetching existing items:', error);
        }
      };
      
      fetchExistingItems();
    }
    
    // Reset form state when dialog opens/closes
    if (!open) {
      setName('');
      setDescription('');
      setNameError('');
    }
  }, [open, clientId]);

  const validateItemName = (itemName: string) => {
    const normalizedName = itemName.trim().toLowerCase();
    
    if (!normalizedName) {
      setNameError('Please enter an item name.');
      return false;
    }
    
    if (existingItems.includes(normalizedName)) {
      setNameError('This item already exists or is pending approval.');
      return false;
    }
    setNameError('');
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!clientId) {
      toast({
        title: 'Error',
        description: 'Client information not found.',
        variant: 'destructive',
      });
      return;
    }
    
    const trimmedName = name.trim();
    if (!trimmedName) {
      toast({
        title: 'Error',
        description: 'Please provide an item name.',
        variant: 'destructive',
      });
      return;
    }

    // Validate that the item name doesn't already exist
    if (!validateItemName(trimmedName)) {
      return;
    }

    setIsSubmitting(true);
    try {
      console.log('Submitting item request:', { name: trimmedName, description });
      
      // Insert the item request into the database
      const { error } = await supabase
        .from('client_item_requests')
        .insert({
          client_id: clientId,
          name: trimmedName,
          item_type: 'default', // Use default type since we removed the field
          description,
          status: 'pending'
        });
        
      if (error) throw error;
      
      // Reset form
      setName('');
      setDescription('');
      
      toast({
        title: 'Request Submitted',
        description: 'Your service item request has been submitted for approval.',
      });
      
      // Close dialog and call callback
      onOpenChange(false);
      
      if (onRequestSubmitted) {
        onRequestSubmitted();
      }
      
    } catch (error) {
      console.error('Error submitting item request:', error);
      toast({
        title: 'Error',
        description: 'Failed to submit request. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newName = e.target.value;
    setName(newName);
    if (newName) {
      validateItemName(newName);
    } else {
      setNameError('');
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Request New Service Item</DialogTitle>
          <DialogDescription>
            Submit a request for a new service item for your account
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Item Name *</Label>
            <Input 
              id="name"
              value={name}
              onChange={handleNameChange}
              placeholder="e.g., Premium Shirt Washing"
              className={nameError ? "border-red-500" : ""}
              required
            />
            {nameError && (
              <p className="text-sm text-red-500">{nameError}</p>
            )}
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea 
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Provide any additional details for this service item"
              rows={3}
            />
          </div>
          
          <DialogFooter>
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => onOpenChange(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={isSubmitting || !!nameError}
            >
              {isSubmitting ? 'Submitting...' : 'Submit Request'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
