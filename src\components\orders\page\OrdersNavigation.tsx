
import { Link, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import { useAuth } from "@/contexts/auth";

export function OrdersNavigation() {
  const location = useLocation();
  const { userRole } = useAuth();
  const isStaff = userRole === "admin" || userRole === "staff";
  const path = location.pathname;

  // Check if we're on a specific tab
  const isAllOrders = path === "/orders";
  const isJobOrders = path === "/orders/jobs";
  const isStatus = path === "/orders/status";
  const isSettings = path === "/pricing";

  return (
    <nav className="flex flex-wrap gap-1 border-b mb-6">
      <Link
        to="/orders"
        className={cn(
          "px-4 py-2 text-sm font-medium transition-colors hover:text-primary",
          isAllOrders ? "border-b-2 border-primary text-primary" : "text-muted-foreground"
        )}
      >
        All Orders
      </Link>
      
      {isStaff && (
        <Link
          to="/orders/jobs"
          className={cn(
            "px-4 py-2 text-sm font-medium transition-colors hover:text-primary",
            isJobOrders ? "border-b-2 border-primary text-primary" : "text-muted-foreground"
          )}
        >
          Job Orders
        </Link>
      )}
      
      <Link
        to="/orders/status"
        className={cn(
          "px-4 py-2 text-sm font-medium transition-colors hover:text-primary",
          isStatus ? "border-b-2 border-primary text-primary" : "text-muted-foreground"
        )}
      >
        Order Status
      </Link>
      
      {isStaff && (
        <Link
          to="/pricing"
          className={cn(
            "px-4 py-2 text-sm font-medium transition-colors hover:text-primary",
            isSettings ? "border-b-2 border-primary text-primary" : "text-muted-foreground"
          )}
        >
          Settings
        </Link>
      )}
    </nav>
  );
}
