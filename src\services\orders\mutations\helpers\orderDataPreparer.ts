
/**
 * Prepares the order data for insertion into the database
 */
export function prepareOrderData(
  orderData: any,
  customerInfo: {
    customerNameToUse: string;
    customerPhoneToUse: string;
    contactPerson: string;
  },
  clientId: string | null,
  referenceCode: string
) {
  const { customerNameToUse, customerPhoneToUse, contactPerson } = customerInfo;

  // Ensure the delivery date is provided and valid
  let deliveryDate = orderData.deliveryDate;
  if (!deliveryDate || deliveryDate === '') {
    // Default to a date 1 day from now if not provided
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    deliveryDate = tomorrow.toISOString().split('T')[0];
    console.log("Using default delivery date:", deliveryDate);
  } else {
    // Try to format the date consistently
    try {
      const dateObj = new Date(deliveryDate);
      if (!isNaN(dateObj.getTime())) {
        deliveryDate = dateObj.toISOString().split('T')[0];
      }
    } catch (err) {
      console.error("Error formatting delivery date:", err);
      // Keep the original format if there's an error
    }
  }

  // Extract items based on where they might be stored
  let items = [];
  if (Array.isArray(orderData.lineItems) && orderData.lineItems.length > 0) {
    items = orderData.lineItems;
  } else if (Array.isArray(orderData.selectedClientItems) && orderData.selectedClientItems.length > 0) {
    items = orderData.selectedClientItems;
  } else if (orderData.items) {
    items = orderData.items;
  }

  // Convert items to JSON string if it's not already a string
  const itemsForDb = typeof items === 'string' 
    ? items 
    : JSON.stringify(items);

  // Handle potentially undefined numeric fields with defaults
  const orderAmount = parseFloat(orderData.orderAmount || orderData.amount || 0) || 0;
  const paidAmount = parseFloat(orderData.paidAmount || 0) || 0;
  const weightKilos = parseFloat(orderData.weightKilos) || 0;
  const vatAmount = parseFloat(orderData.vatAmount || 0) || 0;
  const subtotal = parseFloat(orderData.subtotal || 0) || 0;
  const numberOfPieces = parseInt(orderData.numberOfPieces) || 1;

  // Create the new order object with all required fields
  return {
    client_id: clientId,
    delivery_date: deliveryDate,  // Now properly formatted
    status: orderData.status || 'processing',
    items: itemsForDb,
    use_detergent: orderData.useDetergent || false,
    use_conditioner: orderData.useFabricConditioner || false,
    notes: orderData.notes || null,
    amount: orderAmount,
    paid_amount: paidAmount,
    reference_code: referenceCode,
    number_of_pieces: numberOfPieces,
    weight_kilos: weightKilos,
    customer_type: orderData.customerType,
    customer_name: customerNameToUse,
    phone_number: customerPhoneToUse,
    detergent_quantity: orderData.detergentQuantity || "1",
    conditioner_quantity: orderData.conditionerQuantity || "1",
    service_type: orderData.serviceType || "wash_dry_fold",
    vat_amount: vatAmount,
    subtotal_before_vat: subtotal,
    use_stain_remover: orderData.useStainRemover || false,
    use_bleach: orderData.useBleach || false,
    contact_person: contactPerson || "",
    is_dry_cleaning: orderData.isDryCleaning || false,
    dry_cleaning_items: orderData.dryCleaningItems || []
  };
}
