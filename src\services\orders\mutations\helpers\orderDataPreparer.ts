
import { toDbNumber } from "@/utils/db-converters";
import { processLineItems } from "./lineItemsProcessor";
import { CustomerType } from "@/types";

/**
 * Prepares order data for database insertion
 */
export function prepareOrderData(
  orderData: any, 
  customerData: { 
    customerNameToUse: string;
    customerPhoneToUse: string;
    contactPerson: string;
  },
  clientId: string | null,
  referenceCode: string
): any {
  // Process the line items
  const lineItems = processLineItems(orderData);
  
  // Ensure we have a valid amount value
  const orderAmount = parseFloat(orderData.orderAmount) || parseFloat(orderData.amount) || 0;
  
  // Prepare order data with correct types for Supabase
  return {
    client_id: clientId,
    status: orderData.status || 'processing',
    amount: orderAmount,
    paid_amount: parseFloat(orderData.paidAmount) || 0,
    delivery_date: orderData.deliveryDate,
    items: lineItems, // Store line items directly
    use_detergent: orderData.useDetergent || false,
    use_conditioner: orderData.useFabricConditioner || false,
    use_stain_remover: orderData.useStainRemover || false,
    use_bleach: orderData.useBleach || false,
    detergent_quantity: toDbNumber(parseInt(orderData.detergentQuantity) || 1),
    conditioner_quantity: toDbNumber(parseInt(orderData.conditionerQuantity) || 1),
    customer_name: customerData.customerNameToUse,
    phone_number: customerData.customerPhoneToUse,
    reference_code: referenceCode,
    notes: orderData.notes || null,
    customer_type: orderData.customerType || 'walk-in',
    number_of_pieces: parseInt(orderData.numberOfPieces || '1', 10),
    weight_kilos: parseFloat(orderData.weightKilos || '0'),
    vat_amount: orderData.vatAmount || null,
    subtotal_before_vat: orderData.subtotal || null,
    service_type: orderData.serviceType || null,
    contact_person: customerData.contactPerson
  };
}
