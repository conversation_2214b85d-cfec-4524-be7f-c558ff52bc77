
import { Alert, AlertDescription } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>ertTriangle, Loader2 } from "lucide-react";
import { UseFormReturn } from "react-hook-form";
import { extractFormErrors } from "./client-order-form/utils/errorHandlingUtils";

interface FormActionsProps {
  form: UseFormReturn<any>;
  isSubmitting: boolean;
}

export function FormActions({ form, isSubmitting }: FormActionsProps) {
  // Get form errors and validation state
  const { errorMessages, hasErrors } = extractFormErrors(form);
  const orderType = form.watch("orderType") || "walk-in";
  
  // Only display the error alert if we have actual error messages to show
  const showErrorAlert = hasErrors && errorMessages.length > 0;
  
  // For walk-in orders, we need to check the specific fields that are relevant
  const isFormValid = () => {
    if (orderType === "walk-in") {
      // Check if required walk-in fields have values
      const customerName = form.watch("customerName");
      const phoneNumber = form.watch("phoneNumber");
      const deliveryDate = form.watch("deliveryDate");
      
      // Basic validation for walk-in orders
      return (
        customerName && 
        customerName.trim() !== "" && 
        phoneNumber && 
        phoneNumber.trim() !== "" && 
        deliveryDate
      );
    }
    
    // For client orders, rely on the standard form validation
    return !hasErrors;
  };
  
  // Determine if the submit button should be disabled
  // For walk-in orders, only disable if there are actual shown error messages or form is invalid
  const isSubmitDisabled = isSubmitting || 
    (orderType === "walk-in" ? (showErrorAlert || !isFormValid()) : hasErrors);
  
  return (
    <div className="space-y-4">
      {showErrorAlert && (
        <Alert variant="destructive" className="text-sm">
          <AlertTriangle className="h-4 w-4 mr-2" />
          <AlertDescription>
            Please fix the errors above before submitting.
          </AlertDescription>
        </Alert>
      )}

      <div className="flex justify-end space-x-2">
        <Button
          type="reset"
          variant="outline"
          onClick={(e) => {
            e.preventDefault(); // Prevent accidental form submission
            form.reset();
          }}
          disabled={isSubmitting}
        >
          Reset
        </Button>
        
        <Button 
          type="submit" 
          disabled={isSubmitDisabled}
        >
          {isSubmitting ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" /> Submitting...
            </>
          ) : (
            'Create Order'
          )}
        </Button>
      </div>
    </div>
  );
}
