
import { useState } from 'react';
import { Label } from "@/components/ui/label";
import { v4 as uuidv4 } from 'uuid';
import { ItemTreatments } from "./ItemTreatmentOptions";
import { ClientOrderItem } from "../order-form/types";
import { ItemTypeSection } from "./ItemTypeSection";
import { SelectedItemsSummary } from "./SelectedItemsSummary";

interface ItemSelectorContainerProps {
  items: { id: string; name: string; item_type: string; unit_price: number; }[];
  onItemsChange: (items: ClientOrderItem[]) => void;
  initialItems?: ClientOrderItem[];
}

export function ItemSelectorContainer({ 
  items, 
  onItemsChange,
  initialItems = []
}: ItemSelectorContainerProps) {
  // Convert initial items to have instanceId if they don't already
  const initialItemsWithInstanceIds = initialItems.map(item => ({
    ...item,
    instanceId: item.instanceId || uuidv4()
  }));

  const [selectedItems, setSelectedItems] = useState<ClientOrderItem[]>(initialItemsWithInstanceIds);
  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>({});

  // Group items by type
  const itemsByType = items.reduce((acc: Record<string, typeof items>, item) => {
    if (!acc[item.item_type]) {
      acc[item.item_type] = [];
    }
    acc[item.item_type].push(item);
    return acc;
  }, {});

  const handleAddItem = (item: { id: string; name: string; item_type: string; unit_price: number; }) => {
    // Always create a new instance with a unique instanceId - this allows the same item to be added multiple times
    const newInstance: ClientOrderItem = { 
      id: item.id, 
      name: item.name, 
      quantity: 1, 
      instanceId: uuidv4(),
      unitPrice: item.unit_price, // Use the exact unit_price from database
      treatments: {
        useStainRemoval: false,
        useBeachTreatment: false,
        detergentType: 'none' as const,
        conditionerType: 'none' as const
      }
    };
    
    console.log('Adding item with unit_price from database:', item.unit_price);
    const newSelectedItems = [...selectedItems, newInstance];
    setSelectedItems(newSelectedItems);
    onItemsChange(newSelectedItems);

    // Auto-expand the newly added item to show treatment options
    setExpandedItems(prev => ({
      ...prev,
      [newInstance.instanceId!]: true
    }));
  };

  const handleRemoveItem = (instanceId: string) => {
    const newSelectedItems = selectedItems.filter(item => item.instanceId !== instanceId);
    setSelectedItems(newSelectedItems);
    onItemsChange(newSelectedItems);
  };

  const handleQuantityChange = (instanceId: string, newQuantity: number) => {
    if (newQuantity < 1) return; // Prevent zero or negative quantities
    
    const updatedItems = selectedItems.map(item => 
      item.instanceId === instanceId ? { ...item, quantity: newQuantity } : item
    );
    
    setSelectedItems(updatedItems);
    onItemsChange(updatedItems);
  };

  const toggleExpandItem = (instanceId: string) => {
    setExpandedItems(prev => ({
      ...prev,
      [instanceId]: !prev[instanceId]
    }));
  };

  const handleTreatmentChange = (instanceId: string, treatments: ItemTreatments) => {
    console.log('Updating treatments for item:', instanceId, treatments);
    const updatedItems = selectedItems.map(item => 
      item.instanceId === instanceId ? { ...item, treatments } : item
    );
    
    setSelectedItems(updatedItems);
    onItemsChange(updatedItems);
  };

  const duplicateItem = (instanceId: string) => {
    const itemToDuplicate = selectedItems.find(item => item.instanceId === instanceId);
    if (!itemToDuplicate) return;

    const duplicatedItem = {
      ...itemToDuplicate,
      instanceId: uuidv4(),
      treatments: { ...itemToDuplicate.treatments }
    };

    const newSelectedItems = [...selectedItems, duplicatedItem];
    setSelectedItems(newSelectedItems);
    onItemsChange(newSelectedItems);
  };

  return (
    <div className="space-y-4">
      <Label className="block font-medium">Select items:</Label>
      
      {Object.entries(itemsByType).map(([type, typeItems]) => (
        <ItemTypeSection
          key={type}
          type={type}
          typeItems={typeItems}
          selectedItems={selectedItems}
          expandedItems={expandedItems}
          onAddItem={handleAddItem}
          onRemoveItem={handleRemoveItem}
          onDuplicateItem={duplicateItem}
          onToggleExpand={toggleExpandItem}
          onQuantityChange={handleQuantityChange}
          onTreatmentChange={handleTreatmentChange}
        />
      ))}
      
      <SelectedItemsSummary selectedItems={selectedItems} />
    </div>
  );
}
