
export interface OrderItem {
  name: string;
  qty: number;
  price: number;
}

export interface Order {
  id: string;
  uuid?: string;
  customer: string;
  phone: string;
  items: OrderItem[];
  totalAmount: number;
  status: string;
  stage: string;
  timestamp: string;
  isClientOrder: boolean;
  reference_code?: string;
}

export interface Stage {
  id: string;
  name: string;
  color: string;
}

export type OrderType = "client" | "walk-in";
