
import React from "react";
import { UseFormReturn } from "react-hook-form";
import { OrderFormValues } from "../../OrderFormTypes";
import { LaundryDetails } from "../../laundry-details";
import { FormSectionWrapper } from "./FormSectionWrapper";

interface LaundryDetailsSectionProps {
  form: UseFormReturn<OrderFormValues>;
}

export function LaundryDetailsSection({ form }: LaundryDetailsSectionProps) {
  // We don't need to check for the DryCleaningIndicator here anymore
  // since it's now shown in the parent component based on the isDryCleaning state
  
  return (
    <FormSectionWrapper>
      <LaundryDetails 
        form={form}
        onChange={(e) => form.setValue("deliveryDate", e.target.value)}
      />
    </FormSectionWrapper>
  );
}
