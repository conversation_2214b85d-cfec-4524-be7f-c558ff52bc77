
import React from "react";
import { UseFormReturn } from "react-hook-form";
import { OrderFormValues } from "../../OrderFormTypes";
import { FormSectionWrapper } from "./FormSectionWrapper";
import { DeliveryDateInput } from "../../laundry-details/DeliveryDateInput";
import { PiecesInput } from "../../laundry-details/PiecesInput";
import { SERVICE_TYPES } from "../../pricing/constants";

interface LaundryDetailsSectionProps {
  form: UseFormReturn<OrderFormValues>;
}

export function LaundryDetailsSection({ form }: LaundryDetailsSectionProps) {
  const serviceType = form.watch("serviceType");
  const isDryCleaning = serviceType === SERVICE_TYPES.DRY_CLEANING;
  
  // Add empty onChange handler since we don't need it but it's required by the component
  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Not needed for this implementation
  };
  
  return (
    <FormSectionWrapper>
      <h3 className="font-semibold text-lg mb-4">Laundry Details</h3>
      
      <div className="space-y-4">
        {/* Include delivery date and pieces inputs for client orders */}
        <DeliveryDateInput form={form} onChange={handleDateChange} />
        <PiecesInput form={form} />
      </div>
    </FormSectionWrapper>
  );
}
