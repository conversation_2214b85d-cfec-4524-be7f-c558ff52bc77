
import { Badge } from "@/components/ui/badge";
import { ClientOrderItem } from "../order-form/types";
import { formatCurrency } from "@/lib/utils";

interface SelectedItemsSummaryProps {
  selectedItems: ClientOrderItem[];
}

export function SelectedItemsSummary({ selectedItems }: SelectedItemsSummaryProps) {
  if (selectedItems.length === 0) {
    return null;
  }
  
  // Calculate total price - use the exact unitPrice stored with each item
  const totalPrice = selectedItems.reduce((total, item) => {
    const itemUnitPrice = item.unitPrice || 0;
    const itemQuantity = item.quantity || 1;
    return total + (itemUnitPrice * itemQuantity);
  }, 0);
  
  return (
    <div className="pt-4">
      <div className="flex items-center justify-between font-medium">
        <span>Selected items:</span>
        <div className="flex items-center gap-2">
          <span>{selectedItems.length} {selectedItems.length === 1 ? 'item' : 'items'}</span>
          <span className="text-muted-foreground">|</span>
          <span className="font-semibold">{formatCurrency(totalPrice)}</span>
        </div>
      </div>
      
      <div className="flex flex-wrap gap-2 mt-2">
        {selectedItems.map(item => {
          const hasTreatments = item.treatments && (
            item.treatments.useStainRemoval || 
            item.treatments.detergentType !== 'none' || 
            item.treatments.conditionerType !== 'none'
          );
          
          return (
            <Badge key={item.instanceId} variant="outline" className="py-1.5 px-3">
              <span className="font-medium">{item.quantity}x</span> {item.name}
              <span className="ml-1 text-xs text-muted-foreground">
                ({formatCurrency(item.unitPrice || 0)})
              </span>
              {hasTreatments && (
                <span className="ml-1 text-xs">
                  {item.treatments?.useStainRemoval && '⚡'}
                  {item.treatments?.detergentType !== 'none' && '💧'}
                  {item.treatments?.conditionerType !== 'none' && '✨'}
                </span>
              )}
            </Badge>
          );
        })}
      </div>
    </div>
  );
}
