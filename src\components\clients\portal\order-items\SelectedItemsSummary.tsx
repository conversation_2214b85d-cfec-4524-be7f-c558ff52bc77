
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { ClientOrderItem } from "../order-form/types";
import { calculateItemTotalWithTreatments } from "@/utils/priceCalculations";
import { Badge } from "@/components/ui/badge";

interface SelectedItemsSummaryProps {
  selectedItems: ClientOrderItem[];
}

export function SelectedItemsSummary({ selectedItems }: SelectedItemsSummaryProps) {
  if (selectedItems.length === 0) {
    return null;
  }

  const getTreatmentLabel = (item: ClientOrderItem): string | null => {
    if (!item.treatments) return null;
    
    const treatments = [];
    if (item.treatments.useStainRemoval) treatments.push("Stain Removal");
    if (item.treatments.useBeachTreatment) treatments.push("Beach Treatment");
    if (item.treatments.detergentType !== 'none') treatments.push(`${item.treatments.detergentType} Detergent`);
    if (item.treatments.conditionerType !== 'none') treatments.push(`${item.treatments.conditionerType} Conditioner`);
    
    return treatments.length > 0 ? treatments.join(", ") : null;
  };

  // Group items by name and treatment
  const groupedItems: Record<string, ClientOrderItem[]> = {};
  
  selectedItems.forEach(item => {
    // Create a unique key for each combination of item name and treatments
    const treatments = item.treatments || {
      useStainRemoval: false,
      useBeachTreatment: false,
      detergentType: 'none',
      conditionerType: 'none'
    };
    
    // Each item instance is treated separately in this implementation
    const key = item.instanceId || `${item.id}-${Date.now()}`;
    
    if (!groupedItems[key]) {
      groupedItems[key] = [];
    }
    
    groupedItems[key].push(item);
  });

  const grandTotal = selectedItems.reduce((total, item) => {
    return total + calculateItemTotalWithTreatments(item);
  }, 0);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Selected Items Summary</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="space-y-2">
            {Object.entries(groupedItems).map(([key, items]) => {
              const item = items[0]; // Get the first item in the group (there's only one since we're using instanceId as key)
              const treatmentLabel = getTreatmentLabel(item);
              const itemTotal = calculateItemTotalWithTreatments(item);
              
              return (
                <div key={key} className="flex justify-between items-start">
                  <div>
                    <div className="flex items-center">
                      <span>{item.name} × {item.quantity || 1}</span>
                    </div>
                    {treatmentLabel && (
                      <Badge variant="outline" className="mt-1 text-xs font-normal">
                        {treatmentLabel}
                      </Badge>
                    )}
                  </div>
                  <div className="text-right">
                    <div>₱{itemTotal.toFixed(2)}</div>
                  </div>
                </div>
              );
            })}
          </div>
          
          <div className="pt-2 border-t border-border">
            <div className="flex justify-between font-medium">
              <span>Total</span>
              <span>₱{grandTotal.toFixed(2)}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
