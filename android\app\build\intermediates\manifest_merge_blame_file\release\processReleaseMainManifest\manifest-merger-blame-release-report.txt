1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.philvirtualoffice.pvosyncpos"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <!-- Permissions -->
12
13    <uses-permission android:name="android.permission.INTERNET" />
13-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:40:5-67
13-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:40:22-64
14    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
14-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:41:5-79
14-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:41:22-76
15    <uses-permission
15-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:42:5-95
16        android:name="android.permission.BLUETOOTH"
16-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:42:22-65
17        android:maxSdkVersion="30" />
17-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:42:66-92
18    <uses-permission
18-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:43:5-101
19        android:name="android.permission.BLUETOOTH_ADMIN"
19-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:43:22-71
20        android:maxSdkVersion="30" />
20-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:43:72-98
21    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
21-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:44:5-76
21-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:44:22-73
22    <uses-permission
22-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:45:5-120
23        android:name="android.permission.BLUETOOTH_SCAN"
23-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:45:22-70
24        android:usesPermissionFlags="neverForLocation" />
24-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:45:71-117
25    <uses-permission
25-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:46:5-107
26        android:name="android.permission.READ_EXTERNAL_STORAGE"
26-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:46:22-77
27        android:maxSdkVersion="32" />
27-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:46:78-104
28    <uses-permission
28-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:47:5-108
29        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
29-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:47:22-78
30        android:maxSdkVersion="29" />
30-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:47:79-105
31
32    <permission
32-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
33        android:name="com.philvirtualoffice.pvosyncpos.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
33-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
34        android:protectionLevel="signature" />
34-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
35
36    <uses-permission android:name="com.philvirtualoffice.pvosyncpos.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
36-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
36-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
37
38    <application
38-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:4:5-36:19
39        android:allowBackup="true"
39-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:5:9-35
40        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
40-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f51ed623ec66baebfa6a053fe8a8b2a\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
41        android:extractNativeLibs="false"
42        android:icon="@mipmap/ic_launcher"
42-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:6:9-43
43        android:label="@string/app_name"
43-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:7:9-41
44        android:roundIcon="@mipmap/ic_launcher_round"
44-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:8:9-54
45        android:supportsRtl="true"
45-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:9:9-35
46        android:theme="@style/AppTheme" >
46-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:10:9-40
47        <activity
47-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:12:9-25:20
48            android:name="com.philvirtualoffice.pvosyncpos.MainActivity"
48-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:14:13-41
49            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|smallestScreenSize|screenLayout|uiMode|navigation"
49-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:13:13-140
50            android:exported="true"
50-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:18:13-36
51            android:label="@string/title_activity_main"
51-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:15:13-56
52            android:launchMode="singleTask"
52-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:17:13-44
53            android:theme="@style/AppTheme.NoActionBarLaunch" >
53-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:16:13-62
54            <intent-filter>
54-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:20:13-23:29
55                <action android:name="android.intent.action.MAIN" />
55-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:21:17-69
55-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:21:25-66
56
57                <category android:name="android.intent.category.LAUNCHER" />
57-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:22:17-77
57-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:22:27-74
58            </intent-filter>
59        </activity>
60
61        <provider
62            android:name="androidx.core.content.FileProvider"
62-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:28:13-62
63            android:authorities="com.philvirtualoffice.pvosyncpos.fileprovider"
63-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:29:13-64
64            android:exported="false"
64-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:30:13-37
65            android:grantUriPermissions="true" >
65-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:31:13-47
66            <meta-data
66-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:32:13-34:64
67                android:name="android.support.FILE_PROVIDER_PATHS"
67-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:33:17-67
68                android:resource="@xml/file_paths" />
68-->C:\Users\<USER>\pvosyncpos-laba-edition\android\app\src\main\AndroidManifest.xml:34:17-51
69        </provider>
70        <provider
70-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
71            android:name="androidx.startup.InitializationProvider"
71-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
72            android:authorities="com.philvirtualoffice.pvosyncpos.androidx-startup"
72-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
73            android:exported="false" >
73-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
74            <meta-data
74-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
75                android:name="androidx.emoji2.text.EmojiCompatInitializer"
75-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
76                android:value="androidx.startup" />
76-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\455628e39230ce08b70f281b4ac99c3c\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
77            <meta-data
77-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
78                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
78-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
79                android:value="androidx.startup" />
79-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2086035d7e747a32c2be40e6ed7f404e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
80            <meta-data
80-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
81                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
81-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
82                android:value="androidx.startup" />
82-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
83        </provider>
84
85        <receiver
85-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
86            android:name="androidx.profileinstaller.ProfileInstallReceiver"
86-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
87            android:directBootAware="false"
87-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
88            android:enabled="true"
88-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
89            android:exported="true"
89-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
90            android:permission="android.permission.DUMP" >
90-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
91            <intent-filter>
91-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
92                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
92-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
92-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
93            </intent-filter>
94            <intent-filter>
94-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
95                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
95-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
95-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
96            </intent-filter>
97            <intent-filter>
97-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
98                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
98-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
98-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
99            </intent-filter>
100            <intent-filter>
100-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
101                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
101-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
101-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f3d68271fd414f74f11a6a9c308b287\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
102            </intent-filter>
103        </receiver>
104    </application>
105
106</manifest>
