
import { createReceiptFormatter, createJobOrderFormatter } from './formatters';
import { PrintOrderData } from './types';

/**
 * Main receipt formatter service that delegates to specialized formatters
 */
export class ReceiptFormatterService {
  /**
   * Format order data into a printable receipt
   */
  public formatReceipt(orderData: PrintOrderData): string {
    const formatter = createReceiptFormatter(orderData);
    return formatter.getReceiptContent();
  }

  /**
   * Format order data into a job order slip for staff
   */
  public formatJobOrder(orderData: PrintOrderData): string {
    const formatter = createJobOrderFormatter(orderData);
    return formatter.getJobOrderContent();
  }
}

/**
 * Create and export a singleton instance
 */
export const receiptFormatter = new ReceiptFormatterService();
