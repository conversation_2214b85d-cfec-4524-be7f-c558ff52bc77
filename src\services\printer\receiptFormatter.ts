
import { receiptFormatter as receiptFormatterInstance, jobOrderFormatter } from './formatters';
import { PrintOrderData } from './types';

/**
 * Main receipt formatter class that delegates to specialized formatters
 */
export class ReceiptFormatterService {
  /**
   * Format order data into a printable receipt
   */
  public formatReceipt(orderData: PrintOrderData): string {
    return receiptFormatterInstance.formatReceipt(orderData);
  }

  /**
   * Format order data into a job order slip for staff
   */
  public formatJobOrder(orderData: PrintOrderData): string {
    return jobOrderFormatter.formatJobOrder(orderData);
  }
}

/**
 * Create and export a singleton instance
 */
export const receiptFormatter = new ReceiptFormatterService();
