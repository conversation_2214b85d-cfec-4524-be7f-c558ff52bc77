
import { z } from "zod";

export type ItemTreatments = {
  useStainRemoval: boolean;
  detergentType: 'none' | 'regular' | 'color';
  conditionerType: 'none' | 'regular' | 'fresh' | 'floral';
};

export type ClientOrderItem = {
  id: string;
  name: string;
  quantity: number;
  unitPrice: number;
  instanceId: string;
  treatments?: ItemTreatments;
};

export const orderFormSchema = z.object({
  deliveryDate: z.date({
    required_error: "Please select a delivery date",
  }),
  selectedItems: z.array(z.any()).min(1, {
    message: "Please select at least one item",
  }),
  notes: z.string().optional(),
});

export type OrderFormValues = z.infer<typeof orderFormSchema>;
