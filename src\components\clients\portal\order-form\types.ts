
import { z } from 'zod';

export interface ItemTreatments {
  useStainRemoval: boolean;
  useBeachTreatment: boolean;
  detergentType: 'none' | 'regular' | 'color';
  conditionerType: 'none' | 'regular' | 'fresh' | 'floral';
}

export interface ClientOrderItem {
  id: string;
  instanceId?: string;
  name: string;
  quantity?: number;
  unitPrice?: number;
  treatments?: ItemTreatments;
  item_type?: string;
}

const orderFormSchema = z.object({
  deliveryDate: z.date(),
  notes: z.string().optional(),
  selectedItems: z.array(z.any()),
  dryCleaningItems: z.array(z.any()).optional(),
  serviceType: z.string(),
  selectedServiceTypes: z.array(z.string()),
  isDryCleaning: z.boolean().optional(), // Add isDryCleaning field to the schema
});

export type OrderFormValues = z.infer<typeof orderFormSchema>;

export { orderFormSchema };
