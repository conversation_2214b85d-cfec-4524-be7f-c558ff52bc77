
import { z } from 'zod';

// Define the treatments schema
const treatmentsSchema = z.object({
  useStainRemoval: z.boolean().default(false),
  detergentType: z.enum(['none', 'regular', 'color']),
  conditionerType: z.enum(['none', 'regular', 'fresh', 'floral'])
});

// Define the client order item schema with treatments
export const clientOrderItemSchema = z.object({
  id: z.string(),
  name: z.string(),
  item_type: z.string().optional(),
  unit_price: z.number().optional(),
  quantity: z.number().default(1),
  unitPrice: z.number().optional(),
  instanceId: z.string().optional(),
  treatments: treatmentsSchema.optional(),
  isDryCleaning: z.boolean().optional()
});

// Define dry cleaning item schema
export const dryCleaningItemSchema = z.object({
  id: z.string().optional(),
  type: z.string(),
  name: z.string(),
  price: z.number(),
  quantity: z.number().default(1),
  total: z.number().optional()
});

// Define the order form schema
export const orderFormSchema = z.object({
  deliveryDate: z.date().optional(),
  selectedItems: z.array(clientOrderItemSchema).optional(),
  notes: z.string().optional(),
  dryCleaningItems: z.array(dryCleaningItemSchema).optional(),
  isDryCleaning: z.boolean().optional()
});

export type OrderFormValues = z.infer<typeof orderFormSchema>;
export type ClientOrderItem = z.infer<typeof clientOrderItemSchema>;
export type DryCleaningItem = z.infer<typeof dryCleaningItemSchema>;
export type ItemTreatments = z.infer<typeof treatmentsSchema>;
