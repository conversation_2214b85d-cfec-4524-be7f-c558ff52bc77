
import { useState } from "react";
import { Order } from "@/types";
import { useToast } from "@/hooks/use-toast";
import { usePrinterContext } from "@/contexts/PrinterContext";

export function useBluetoothPrinting() {
  const [isPrinting, setIsPrinting] = useState(false);
  const { printReceipt: printReceiptToDevice, printJobOrder: printJobOrderToDevice } = usePrinterContext();
  const { toast } = useToast();

  const printToBluetoothDevice = async (order: Order, type: 'receipt' | 'jobOrder') => {
    try {
      setIsPrinting(true);
      
      // Create a deep copy of order to avoid mutation
      const orderForPrinting = JSON.parse(JSON.stringify(order));
      
      // Ensure dry cleaning items are properly processed
      if (orderForPrinting.dryCleaningItems && orderForPrinting.dryCleaningItems.length > 0) {
        orderForPrinting.dryCleaningItems = orderForPrinting.dryCleaningItems.map((item: any) => ({
          ...item,
          name: item.type.replace(/_/g, ' ').replace(/\w\S*/g, (txt: string) => 
            txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()),
          quantity: item.quantity || 1,
          price: item.price || 0,
          total: (item.price || 0) * (item.quantity || 1)
        }));
      }
      
      // Process special treatments for line items
      if (orderForPrinting.lineItems) {
        orderForPrinting.lineItems = orderForPrinting.lineItems.map((item: any) => {
          // Items with treatment descriptions need special handling
          if (item.treatmentDescription) {
            // Calculate treatment price based on description
            const treatmentPrice = calculateTreatmentPrice(item.treatmentDescription);
            
            // Return the item with treatment cost factored into the total
            return {
              ...item,
              name: item.name,
              total: (item.quantity * item.unitPrice) + (treatmentPrice * item.quantity)
            };
          }
          return item;
        });
      }
      
      const printFunction = type === 'receipt' 
        ? printReceiptToDevice
        : printJobOrderToDevice;
      
      const typeLabel = type === 'receipt' ? 'receipt' : 'job order';
      const success = await printFunction(orderForPrinting);
      
      if (success) {
        toast({
          title: "Print Successful",
          description: `${typeLabel} for ${order.id} has been sent to printer`,
        });
        return true;
      } else {
        throw new Error('Printing failed');
      }
    } catch (error) {
      console.error(`Print error (${type}):`, error);
      toast({
        title: "Print Failed",
        description: `Could not print the ${type === 'receipt' ? 'receipt' : 'job order'}. Please try again.`,
        variant: "destructive",
      });
      return false;
    } finally {
      setIsPrinting(false);
    }
  };

  // Helper function for treatment price calculation
  const calculateTreatmentPrice = (treatment: string): number => {
    switch (treatment) {
      case 'Stain removal':
        return 15.00;
      case 'Bleaching':
        return 20.00;
      case 'Deep clean':
        return 25.00;
      case 'Delicate wash':
        return 18.00;
      default:
        return 0;
    }
  };

  return {
    isPrinting,
    printToBluetoothDevice
  };
}
