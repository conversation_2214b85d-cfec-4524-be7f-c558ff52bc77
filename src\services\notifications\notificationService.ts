
import { supabase } from "@/integrations/supabase/client";

/**
 * Service for managing notifications in the application
 */
export const notificationService = {
  /**
   * Send a notification to staff members
   */
  async notifyStaff(type: string, message: string, details?: Record<string, any>): Promise<void> {
    try {
      // Get the current timestamp
      const timestamp = new Date().toISOString();
      
      // First check if user is authenticated
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        console.log("Cannot create notification: No active session");
        return;
      }
      
      // Insert notification for staff users
      const { error } = await supabase
        .from('notifications')
        .insert({
          type: 'system',
          role: 'staff',
          title: type.replace(/_/g, ' ').replace(/\b\w/g, c => c.toUpperCase()),
          message: message,
          is_read: false,
          related_id: details?.orderId,
          created_at: timestamp
        });

      if (error) {
        if (error.code === '42501') {
          console.log("Note: Staff notification not created due to RLS policy. This is expected if you don't have the right permissions.");
        } else {
          console.error("Error creating notification:", error);
        }
      }
    } catch (e) {
      console.error("Failed to send notification:", e);
    }
  },
  
  /**
   * Mark a notification as read
   */
  async markAsRead(notificationId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('id', notificationId);
        
      if (error) {
        console.error("Error marking notification as read:", error);
        return false;
      }
      return true;
    } catch (e) {
      console.error("Failed to mark notification as read:", e);
      return false;
    }
  },
  
  /**
   * Get unread notifications for the current user
   */
  async getUnreadNotifications(): Promise<any[]> {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        console.log("Cannot get notifications: No active session");
        return [];
      }
      
      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('is_read', false)
        .order('created_at', { ascending: false });
        
      if (error) {
        console.error("Error fetching notifications:", error);
        return [];
      }
      return data || [];
    } catch (e) {
      console.error("Failed to fetch notifications:", e);
      return [];
    }
  }
};
