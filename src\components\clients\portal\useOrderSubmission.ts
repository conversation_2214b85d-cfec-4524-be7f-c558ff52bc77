
import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { toast } from 'sonner';
import { LineItem } from '@/types';

export function useOrderSubmission(clientId: string | null, onOrderAdded?: () => void) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast: uiToast } = useToast();

  const handleCreateOrder = async (formData: any) => {
    if (!clientId) {
      uiToast({
        title: 'Error',
        description: 'Client information not found.',
        variant: 'destructive',
      });
      return false;
    }

    setIsSubmitting(true);
    try {
      const selectedItems: LineItem[] = formData.selectedItems.map((item: any) => ({
        id: item.id || '',
        name: item.name || '',
        quantity: Number(item.quantity) || 0,
        unitPrice: Number(item.unitPrice) || 0,
        total: Number(item.quantity * item.unitPrice) || 0,
        treatmentDescription: item.treatments ? 
          [
            item.treatments.useStainRemoval ? 'stain removal' : null,
            item.treatments.useBeachTreatment ? 'beach treatment' : null,
            item.treatments.detergentType !== 'none' ? `${item.treatments.detergentType} detergent` : null,
            item.treatments.conditionerType !== 'none' ? `${item.treatments.conditionerType} conditioner` : null
          ].filter(Boolean).join(', ') : ''
      }));

      const totalAmount = selectedItems.reduce(
        (total: number, item: LineItem) => total + (item.total || 0), 0
      );

      // Get client details to ensure we have the correct prefix
      let clientPrefix = '';
      try {
        const { data: clientData } = await supabase
          .from('clients')
          .select('prefix')
          .eq('id', clientId)
          .single();
        
        clientPrefix = clientData?.prefix || 'CLT';
        console.log("Retrieved client prefix for order:", clientPrefix);
      } catch (error) {
        console.error("Error fetching client prefix:", error);
        clientPrefix = 'CLT'; // Fallback to CLT
      }

      // Generate a reference code using client prefix - use the FULL prefix
      const timestamp = Date.now().toString().slice(-6);
      // Use the full prefix without truncation
      const referenceCode = `${clientPrefix.trim().toUpperCase()}-${timestamp}`;
      console.log("Generated order reference code with full prefix:", referenceCode);

      // Important: Convert the items array to JSON string before sending to Supabase
      // This fixes the type error as Supabase expects a JSON type for the items column
      const orderData = {
        client_id: clientId,
        delivery_date: formData.deliveryDate,
        status: 'ready_for_pickup',
        items: JSON.stringify(selectedItems), // Convert LineItem[] to JSON string
        notes: formData.notes,
        amount: totalAmount,
        paid_amount: 0,
        reference_code: referenceCode,
        customer_type: 'client' // Explicitly set customer type for client orders
      };

      console.log("Submitting order with data:", orderData);
      
      const { data, error } = await supabase
        .from('orders')
        .insert(orderData)
        .select()
        .single();

      if (error) {
        console.error('Detailed error creating order:', error);
        throw error;
      }
      
      console.log("Order created successfully:", data);
      
      toast('Order Created', {
        description: 'Your order has been submitted and is ready for pickup.',
        position: 'bottom-right',
      });
      
      if (onOrderAdded) {
        onOrderAdded();
      }
      
      return true;
    } catch (error) {
      console.error('Error creating order:', error);
      uiToast({
        title: 'Error',
        description: 'Failed to create order. Please try again.',
        variant: 'destructive',
      });
      return false;
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    handleCreateOrder,
    isSubmitting
  };
}
