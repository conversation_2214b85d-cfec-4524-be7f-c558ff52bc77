
import { useState, useMemo } from 'react';
import { Order } from '@/types';

export function useJobOrderFilters(orders: Order[]) {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [dateFilter, setDateFilter] = useState<Date | undefined>(undefined);
  const [clientFilter, setClientFilter] = useState("all");
  const [orderType, setOrderType] = useState<"walk-in" | "client" | "all">("all");
  
  // Filter orders based on search term, status, date and client
  const filteredOrders = useMemo(() => {
    return orders.filter(order => {
      const matchesSearch = searchTerm === "" || 
        order.customer.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.reference_code?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.id.toLowerCase().includes(searchTerm.toLowerCase());
        
      const matchesStatus = statusFilter === "all" || order.status === statusFilter;
      
      const matchesDate = !dateFilter || 
        (order.orderDate && new Date(order.orderDate).toDateString() === dateFilter.toDateString());
      
      const matchesClient = clientFilter === "all" || order.clientId === clientFilter;
      
      // Improved filtering for walk-in vs client orders
      const matchesOrderType = orderType === "all" || 
        (orderType === "client" && order.customerType === "client") ||
        (orderType === "walk-in" && order.customerType === "walk-in");
      
      return matchesSearch && matchesStatus && matchesDate && matchesClient && matchesOrderType;
    });
  }, [orders, searchTerm, statusFilter, dateFilter, clientFilter, orderType]);
  
  return {
    searchTerm,
    setSearchTerm,
    statusFilter,
    setStatusFilter,
    dateFilter,
    setDateFilter,
    clientFilter,
    setClientFilter,
    orderType,
    setOrderType,
    filteredOrders
  };
}
