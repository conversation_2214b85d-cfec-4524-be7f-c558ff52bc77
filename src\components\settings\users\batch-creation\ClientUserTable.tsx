
import { Table, TableBody, Table<PERSON>ell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";
import { Loader2, Check } from "lucide-react";
import { ClientWithUser } from "./types";

interface ClientUserTableProps {
  clients: ClientWithUser[];
  toggleSelectAll: () => void;
  toggleSelect: (index: number) => void;
  updateEmail: (index: number, email: string) => void;
  processing: boolean;
  selectedCount: number;
  availableClientsCount: number;
}

export function ClientUserTable({
  clients,
  toggleSelectAll,
  toggleSelect,
  updateEmail,
  processing,
  selectedCount,
  availableClientsCount
}: ClientUserTableProps) {
  return (
    <>
      <div className="mb-4 flex items-center">
        <Checkbox 
          id="select-all"
          checked={clients.some(c => c.selected)}
          onCheckedChange={toggleSelectAll}
        />
        <label htmlFor="select-all" className="ml-2 text-sm font-medium">
          Select all available clients ({availableClientsCount})
        </label>
        <div className="ml-auto">
          {selectedCount > 0 && (
            <span className="text-sm text-muted-foreground">
              {selectedCount} client{selectedCount !== 1 ? 's' : ''} selected
            </span>
          )}
        </div>
      </div>
      
      <div className="border rounded-md overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">Select</TableHead>
              <TableHead>Client Name</TableHead>
              <TableHead>Contact Person</TableHead>
              <TableHead>Email Address</TableHead>
              <TableHead>Status</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {clients.map((client, index) => (
              <TableRow key={client.client.id}>
                <TableCell>
                  <Checkbox 
                    checked={client.selected}
                    disabled={client.hasUser || !client.email}
                    onCheckedChange={() => toggleSelect(index)}
                  />
                </TableCell>
                <TableCell className="font-medium">{client.client.name}</TableCell>
                <TableCell>{client.client.contact_person || '-'}</TableCell>
                <TableCell>
                  <input
                    type="email"
                    className="w-full border-none bg-transparent focus:outline-none focus:ring-0"
                    value={client.email}
                    onChange={(e) => updateEmail(index, e.target.value)}
                    disabled={client.hasUser || processing}
                    placeholder="No email address"
                  />
                </TableCell>
                <TableCell>
                  <ClientStatusBadge client={client} />
                </TableCell>
              </TableRow>
            ))}
            
            {clients.length === 0 && (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-4">
                  No clients found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </>
  );
}

function ClientStatusBadge({ client }: { client: ClientWithUser }) {
  if (client.hasUser) {
    return (
      <span className="text-green-600 flex items-center">
        <Check className="h-4 w-4 mr-1" />
        Has account
      </span>
    );
  } else if (client.processing) {
    return (
      <span className="text-amber-600 flex items-center">
        <Loader2 className="h-4 w-4 mr-1 animate-spin" />
        Processing...
      </span>
    );
  } else if (client.success) {
    return (
      <span className="text-green-600 flex items-center">
        <Check className="h-4 w-4 mr-1" />
        Created
      </span>
    );
  } else if (client.error) {
    return (
      <span className="text-red-600" title={client.error}>Error</span>
    );
  } else if (!client.email) {
    return (
      <span className="text-gray-400">Missing email</span>
    );
  } else {
    return (
      <span className="text-gray-400">No account</span>
    );
  }
}
