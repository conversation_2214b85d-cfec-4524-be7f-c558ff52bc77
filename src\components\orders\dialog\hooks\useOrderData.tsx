
import { useState, useEffect } from 'react';
import { Order } from '@/types';
import { supabase } from '@/integrations/supabase/client';
import { mapSupabaseOrderToOrderType } from '@/services/orders/mutations/helpers/orderMapper';

export function useOrderData(initialOrder: Order) {
  const [currentOrder, setCurrentOrder] = useState<Order>(initialOrder);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const fetchOrderDetails = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      console.log("useOrderData - Fetching order details for:", initialOrder.id);
      
      // First determine if we're using a reference code or UUID
      const isUuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(initialOrder.uuid || '');
      const queryId = isUuid ? initialOrder.uuid : initialOrder.id;
      
      console.log(`useOrderData - Using ${isUuid ? 'UUID' : 'reference code'} to fetch order:`, queryId);
      
      // Query by the appropriate field
      let query = supabase.from('orders').select('*');
      
      if (isUuid) {
        query = query.eq('id', queryId);
      } else {
        query = query.eq('reference_code', queryId);
      }
      
      const { data, error: fetchError } = await query.single();
      
      if (fetchError) {
        console.error("useOrderData - Error fetching order:", fetchError);
        setError(`Error fetching order details: ${fetchError.message}`);
        return;
      }
      
      if (data) {
        console.log("useOrderData - Raw order data from database:", {
          id: data.id,
          reference_code: data.reference_code,
          is_dry_cleaning: data.is_dry_cleaning,
          service_type: data.service_type, 
          dry_cleaning_items: data.dry_cleaning_items
        });
        
        // Map the raw data to our Order type
        const mappedOrder = mapSupabaseOrderToOrderType(data);
        setCurrentOrder(mappedOrder);
        
        console.log("useOrderData - Mapped order:", {
          id: mappedOrder.id,
          isDryCleaning: mappedOrder.isDryCleaning,
          serviceType: mappedOrder.serviceType,
          dryCleaningItems: mappedOrder.dryCleaningItems
        });
      } else {
        console.error("useOrderData - No order data found");
        setError("Order not found");
      }
    } catch (err) {
      console.error("useOrderData - Unexpected error:", err);
      setError(`Unexpected error: ${err instanceof Error ? err.message : String(err)}`);
    } finally {
      setIsLoading(false);
    }
  };

  const refreshOrderData = async () => {
    await fetchOrderDetails();
  };

  // Fetch order details on mount or when initialOrder changes
  useEffect(() => {
    if (initialOrder?.id) {
      fetchOrderDetails();
    }
  }, [initialOrder.id]);

  return {
    currentOrder,
    refreshOrderData,
    isLoading,
    error
  };
}
