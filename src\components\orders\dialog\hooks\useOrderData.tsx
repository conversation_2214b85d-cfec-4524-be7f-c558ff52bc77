
import { useState, useEffect, useCallback } from "react";
import { Order } from "@/types";
import { supabase } from "@/integrations/supabase/client";

export function useOrderData(initialOrder: Order) {
  const [currentOrder, setCurrentOrder] = useState<Order>(initialOrder);
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  // Update local state when order prop changes
  useEffect(() => {
    setCurrentOrder(initialOrder);
  }, [initialOrder]);
  
  // Fetch updated order data from the database
  const refreshOrderData = useCallback(async () => {
    if (!currentOrder.uuid) {
      console.log("Cannot refresh order data - missing UUID");
      return;
    }
    
    try {
      setIsRefreshing(true);
      console.log("Refreshing order data for:", currentOrder.id, currentOrder.uuid);
      
      const { data, error } = await supabase
        .from('orders')
        .select('*')
        .eq('id', currentOrder.uuid)
        .single();
        
      if (error) {
        console.error('Error fetching updated order:', error);
        return;
      }
      
      if (data) {
        console.log("Order data fetched successfully:", data);
        
        // Parse the items if they exist
        let parsedItems = [];
        try {
          if (data.items) {
            if (typeof data.items === 'string') {
              parsedItems = JSON.parse(data.items);
            } else if (Array.isArray(data.items)) {
              parsedItems = data.items;
            }
          }
        } catch (error) {
          console.error('Error parsing items:', error);
          parsedItems = [];
        }
        
        // Transform the data to match the Order type
        const updatedOrder: Order = {
          ...currentOrder,
          lineItems: parsedItems || [],
          amount: data.amount || 0,
          useDetergent: Boolean(data.use_detergent),
          useFabricConditioner: Boolean(data.use_conditioner),
          useStainRemover: Boolean(data.use_stain_remover),
          useBleach: Boolean(data.use_bleach),
          detergentQuantity: Number(data.detergent_quantity) || 1,
          conditionerQuantity: Number(data.conditioner_quantity) || 1,
        };
        
        console.log("Updated order after refresh:", updatedOrder);
        setCurrentOrder(updatedOrder);
      }
    } catch (error) {
      console.error('Failed to refresh order data:', error);
    } finally {
      setIsRefreshing(false);
    }
  }, [currentOrder.id, currentOrder.uuid]);

  // Set up event listeners for order updates
  useEffect(() => {
    // Handle order items updates
    const handleItemsUpdated = (event: Event) => {
      const customEvent = event as CustomEvent;
      const detail = customEvent.detail;
      
      // Check if this event is relevant to our order
      if (detail && detail.orderId === currentOrder.id) {
        console.log("Order items update event detected for current order");
        
        // If we have the full items data in the event, update immediately
        if (detail.items) {
          console.log("Updating order with new items from event");
          setCurrentOrder(prev => ({
            ...prev,
            lineItems: detail.items,
            amount: detail.amount || prev.amount
          }));
        }
        
        // Also do a full refresh to ensure all data is up to date
        refreshOrderData();
      }
    };
    
    // Listen for item updates
    window.addEventListener('order-items-updated', handleItemsUpdated);
    
    return () => {
      window.removeEventListener('order-items-updated', handleItemsUpdated);
    };
  }, [currentOrder.id, refreshOrderData]);

  return {
    currentOrder,
    setCurrentOrder,
    refreshOrderData,
    isRefreshing
  };
}
