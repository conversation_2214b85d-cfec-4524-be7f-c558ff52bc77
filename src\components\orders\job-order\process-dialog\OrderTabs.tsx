
import { useState } from "react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Order } from "@/types";
import { useOrder } from "@/contexts/OrderContext";
import { OrderItemsEditor } from "../OrderItemsEditor";
import { OrderAddOnsEditor } from "../OrderAddOnsEditor";
import { OrderStatusWorkflow } from "../../workflow/OrderStatusWorkflow";
import { SERVICE_TYPES } from "../../pricing/constants";
import { PrintButtons } from "../../dialog/PrintButtons";
import { usePrintFunctions } from "../../dialog/PrintingUtils";

interface OrderTabsProps {
  order: Order;
  onOrderUpdated: () => Promise<void>;
  handleStatusChange: (orderId: string, newStatus: string) => Promise<void>;
}

export function OrderTabs({ order, onOrderUpdated, handleStatusChange }: OrderTabsProps) {
  const [activeTab, setActiveTab] = useState("items");
  const { userRole } = useOrder();
  const { isPrinting, handlePrintReceipt, handlePrintJobOrder } = usePrintFunctions();
  
  const isStaff = userRole === 'staff' || userRole === 'admin';
  const isDryCleaningService = order?.serviceType === SERVICE_TYPES.DRY_CLEANING;

  return (
    <Tabs value={activeTab} onValueChange={setActiveTab}>
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="items">Items & Treatments</TabsTrigger>
        <TabsTrigger value="add-ons">Add-ons & Options</TabsTrigger>
        <TabsTrigger value="status">Status & Print</TabsTrigger>
      </TabsList>
      
      <TabsContent value="items" className="space-y-4 py-4">
        <OrderItemsEditor 
          order={order}
          onOrderUpdated={onOrderUpdated}
          isDryCleaning={isDryCleaningService}
        />
      </TabsContent>
      
      <TabsContent value="add-ons" className="space-y-4 py-4">
        <OrderAddOnsEditor 
          order={order}
          onOrderUpdated={onOrderUpdated}
        />
      </TabsContent>
      
      <TabsContent value="status" className="space-y-4 py-4">
        <div className="space-y-6">
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Print Documents</h3>
            <div className="grid grid-cols-2 gap-4">
              <PrintButtons
                onPrintReceipt={() => handlePrintReceipt(order)}
                onPrintJobOrder={() => handlePrintJobOrder(order)}
                isPrinting={isPrinting}
                isStaff={isStaff}
              />
            </div>
          </div>
          
          <div className="border-t pt-6">
            <h3 className="text-lg font-medium mb-4">Update Status</h3>
            <OrderStatusWorkflow
              order={order}
              onStatusChange={handleStatusChange}
            />
          </div>
        </div>
      </TabsContent>
    </Tabs>
  );
}
