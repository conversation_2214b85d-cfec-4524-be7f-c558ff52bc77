
import { ClientOrderItem } from '@/components/clients/portal/order-form/types';
import { ClientItemWithQuantity } from '@/components/orders/OrderFormTypes';

// Calculate total price for an item including any treatment costs
// Works with both ClientOrderItem and ClientItemWithQuantity
export function calculateItemTotalWithTreatments(item: ClientOrderItem | ClientItemWithQuantity): number {
  // Base price calculation - ensure we handle both property names  
  const unitPrice = 'unitPrice' in item ? item.unitPrice : (item as any).unit_price || 0;
  const quantity = item.quantity || 1;
  const basePrice = unitPrice * quantity;
  
  // If no treatments or treatments object is undefined, return base price
  if (!item.treatments) {
    return basePrice;
  }

  // Apply special treatment pricing rules
  // If either stain removal or bleach treatment is selected, double the price
  if (item.treatments.useStainRemoval || item.treatments.useBeachTreatment) {
    // Double the base price according to new requirement
    const specialTreatmentPrice = basePrice * 1; // Double the base price (base + base)
    basePrice + specialTreatmentPrice; // This equals 2x basePrice
    
    // Since we're doubling the entire item price, we return this directly
    return basePrice * 2;
  }
  
  // Treatment surcharges for other treatments (detergent, conditioner)
  let treatmentCost = 0;
  
  // Premium detergent adds a fixed amount per item
  if (item.treatments.detergentType === 'regular') {
    treatmentCost += 5 * quantity;
  } else if (item.treatments.detergentType === 'color') {
    treatmentCost += 8 * quantity;
  }
  
  // Premium conditioner adds a fixed amount per item
  if (item.treatments.conditionerType === 'regular') {
    treatmentCost += 5 * quantity;
  } else if (item.treatments.conditionerType === 'fresh' || item.treatments.conditionerType === 'floral') {
    treatmentCost += 10 * quantity;
  }
  
  return basePrice + treatmentCost;
}
