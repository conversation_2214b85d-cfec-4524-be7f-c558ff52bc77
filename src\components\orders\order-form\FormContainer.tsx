
import { useEffect } from "react";
import { UseFormReturn } from "react-hook-form";
import { OrderFormValues, PriceBreakdown } from "../OrderFormTypes";
import { 
  calculateBasePrice, 
  calculateAddOnPrice, 
  calculateClientItemsPrice,
  calculateDryCleaningItemsPrice,
  calculateVAT, 
  calculateTotalPrice 
} from "../pricing/calculations";
import { SERVICE_TYPES } from "../pricing/constants";

interface FormContainerProps {
  form: UseFormReturn<OrderFormValues>;
  setPriceBreakdown: (breakdown: PriceBreakdown) => void;
}

export function FormContainer({ form, setPriceBreakdown }: FormContainerProps) {
  // Extract relevant form values for price calculation
  const watchedValues = form.watch();
  
  useEffect(() => {
    // Extract the necessary values from the form
    const weightKilos = parseFloat(watchedValues.weightKilos || "0");
    const pricingMethod = watchedValues.pricingMethod;
    const serviceType = watchedValues.serviceType || SERVICE_TYPES.WASH_DRY_FOLD;
    const detergentType = watchedValues.detergentType || "none";
    const conditionerType = watchedValues.conditionerType || "none";
    const detergentQuantity = parseInt(watchedValues.detergentQuantity || "1");
    const conditionerQuantity = parseInt(watchedValues.conditionerQuantity || "1");
    const useStainRemover = watchedValues.useStainRemover || false;
    const useBleach = watchedValues.useBleach || false;
    const isDryCleaning = watchedValues.isDryCleaning || serviceType === SERVICE_TYPES.DRY_CLEANING;
    const clientItems = watchedValues.selectedClientItems || [];
    const dryCleaningItems = watchedValues.dryCleaningItems || [];
    
    // Calculate base price based on pricing method
    let basePrice = 0;
    
    if (isDryCleaning || serviceType === SERVICE_TYPES.DRY_CLEANING) {
      // For dry cleaning, calculate price based on selected dry cleaning items
      basePrice = calculateDryCleaningItemsPrice(dryCleaningItems);
    } else if (pricingMethod === "client_item") {
      // For client item pricing, calculate price based on selected client items
      basePrice = calculateClientItemsPrice(clientItems);
    } else {
      // For weight-based pricing, calculate price based on weight and service type
      basePrice = calculateBasePrice(weightKilos, serviceType);
    }
    
    // Calculate add-on price
    const addOnPrice = calculateAddOnPrice({
      detergentType,
      detergentQuantity,
      conditionerType,
      conditionerQuantity,
      useStainRemover,
      useBleach
    });
    
    // Calculate subtotal
    const subtotal = basePrice + addOnPrice;
    
    // Calculate VAT
    const vatAmount = calculateVAT(subtotal);
    
    // Calculate total price
    const totalPrice = calculateTotalPrice(subtotal, vatAmount);
    
    // Update price breakdown state
    setPriceBreakdown({
      basePrice,
      addOnPrice,
      subtotal,
      vatAmount,
      totalPrice
    });
  }, [watchedValues, setPriceBreakdown]);
  
  // This component doesn't render anything; it just calculates prices
  return null;
}
