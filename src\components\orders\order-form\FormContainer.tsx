
import { useEffect } from "react";
import { UseFormReturn } from "react-hook-form";
import { OrderFormValues, PriceBreakdown, DryCleaningItem } from "../OrderFormTypes";
import { SERVICE_TYPES } from "../pricing/constants";
import { calculateEnhancedPrice } from "../pricing/core/total-calculations";

interface FormContainerProps {
  form: UseFormReturn<OrderFormValues>;
  setPriceBreakdown: (priceBreakdown: PriceBreakdown) => void;
}

export function FormContainer({ form, setPriceBreakdown }: FormContainerProps) {
  // Watch all relevant form fields that affect pricing
  const getPricingMethod = () => {
    // Get the value using the watch method
    return form.watch("pricingMethod") || "weight";
  };
  
  const serviceType = form.watch("serviceType");
  const selectedServiceTypes = form.watch("selectedServiceTypes") || [SERVICE_TYPES.WASH_DRY_FOLD];
  const weightKilos = parseFloat(String(form.watch("weightKilos")) || "0");
  const detergentType = form.watch("detergentType") as "none" | "regular" | "color";
  const detergentQuantity = parseInt(String(form.watch("detergentQuantity")) || "1");
  const conditionerType = form.watch("conditionerType") as "none" | "regular" | "fresh" | "floral";
  const conditionerQuantity = parseInt(String(form.watch("conditionerQuantity")) || "1");
  const useStainRemover = form.watch("useStainRemover");
  const useBleach = form.watch("useBleach");
  const clientItems = form.watch("selectedClientItems") || [];
  const rawDryCleaningItems = form.watch("dryCleaningItems") || [];
  const serviceWeights = form.watch("serviceWeights") || [];

  // Convert dryCleaningItems to the proper format if needed
  const dryCleaningItems: DryCleaningItem[] = rawDryCleaningItems.map((item: any) => {
    if (item.type && item.price && item.quantity) {
      return item as DryCleaningItem;
    }
    return {
      type: item.name || 'unknown',
      price: item.unitPrice || 0,
      quantity: item.quantity || 0,
      total: (item.unitPrice || 0) * (item.quantity || 0),
      id: item.id
    };
  });

  // Update price whenever relevant values change
  useEffect(() => {
    const pricingMethod = getPricingMethod();
    
    // Prepare add-on selection object
    const addOns = {
      detergentType,
      detergentQuantity,
      conditionerType,
      conditionerQuantity,
      useStainRemover,
      useBleach
    };
    
    // Calculate average weight for multi-service orders
    const calculateAverageWeight = () => {
      if (!serviceWeights || serviceWeights.length === 0) return weightKilos;
      
      // Filter to just weight-based services
      const relevantServices = selectedServiceTypes.filter(
        type => type !== SERVICE_TYPES.DRY_CLEANING
      );
      
      if (relevantServices.length === 0) return weightKilos;
      
      let totalWeight = 0;
      let countOfServices = 0;
      
      for (const service of relevantServices) {
        const serviceWeight = serviceWeights.find(sw => sw.serviceType === service);
        if (serviceWeight) {
          totalWeight += serviceWeight.weightKilos;
          countOfServices++;
        }
      }
      
      return countOfServices > 0 ? totalWeight / countOfServices : weightKilos;
    };

    // Calculate the price asynchronously
    const updatePrice = async () => {
      try {
        let result;
        
        // For multiple services, we calculate based on service weights
        if (pricingMethod === "weight" && selectedServiceTypes.length > 1) {
          // Use average weight for overall calculation
          const avgWeight = calculateAverageWeight();
          
          result = await calculateEnhancedPrice({
            weightKilos: avgWeight,
            pricingMethod,
            serviceType,
            addOns,
            clientItems,
            dryCleaningItems,
          });
        } else {
          result = await calculateEnhancedPrice({
            weightKilos,
            pricingMethod,
            serviceType,
            addOns,
            clientItems,
            dryCleaningItems,
          });
        }
        
        // Update price breakdown
        setPriceBreakdown(result);
      } catch (error) {
        console.error("Error calculating price:", error);
      }
    };

    updatePrice();
  }, [
    getPricingMethod(),
    serviceType,
    selectedServiceTypes,
    weightKilos,
    detergentType,
    detergentQuantity,
    conditionerType,
    conditionerQuantity,
    useStainRemover,
    useBleach,
    clientItems,
    rawDryCleaningItems,
    serviceWeights,
    setPriceBreakdown
  ]);

  return null;
}
