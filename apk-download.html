<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CMC Laundry POS - APK Download</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            text-align: center;
        }
        h1 {
            color: #333;
        }
        .download-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #f9f9f9;
        }
        .download-button {
            display: inline-block;
            background-color: #4CAF50;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 4px;
            font-weight: bold;
            margin: 20px 0;
        }
        .instructions {
            text-align: left;
            margin-top: 30px;
            padding: 15px;
            border-left: 4px solid #4CAF50;
            background-color: #f1f1f1;
        }
        .qr-code {
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>CMC Laundry POS</h1>
    <p>Android APK Installation</p>
    
    <div class="download-section">
        <h2>Download APK</h2>
        <p>Click the button below to download the CMC Laundry POS APK:</p>
        <a href="./cmc-laundry-pos.apk" class="download-button" download>Download APK</a>
        
        <div class="qr-code">
            <p>Or scan this QR code with your device:</p>
            <!-- You'll need to generate a QR code that points to your APK and replace the src below -->
            <img id="qrcode" alt="QR Code for APK download" width="200" height="200">
        </div>
    </div>
    
    <div class="instructions">
        <h3>Installation Instructions:</h3>
        <ol>
            <li>Download the APK file to your Android device</li>
            <li>Tap on the downloaded file to begin installation</li>
            <li>If prompted, enable installation from unknown sources in your device settings</li>
            <li>Follow the on-screen instructions to complete installation</li>
            <li>Once installed, you can find the app in your app drawer</li>
        </ol>
    </div>

    <script>
        // Generate QR code for the current page URL
        window.onload = function() {
            const currentUrl = window.location.href;
            const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(currentUrl)}`;
            document.getElementById('qrcode').src = qrCodeUrl;
        };
    </script>
</body>
</html>
