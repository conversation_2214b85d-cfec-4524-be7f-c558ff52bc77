
import React from "react";
import { Row } from "@tanstack/react-table";
import { TableRow, TableCell } from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";
import { ChevronDown, ChevronRight } from "lucide-react";
import { OrderStatusBadge } from "../OrderStatusBadge";
import { Button } from "@/components/ui/button";
import { OrderActions } from "./OrderActions";
import { Order } from "@/types";
import { ViewOrderDialog } from "../ViewOrderDialog";
import { EditOrderDialog } from "../EditOrderDialog";
import { useAuth } from "@/contexts/auth";

interface OrderTableRowProps {
  row: Row<any>;
  onStatusChange: (orderId: string, newStatus: string) => Promise<void>;
  onPrintOrder: (order: Order) => void;
  onOrderDeleted?: () => void;
}

export function OrderTableRow({
  row,
  onStatusChange,
  onPrintOrder,
  onOrderDeleted
}: OrderTableRowProps) {
  const [isViewDialogOpen, setIsViewDialogOpen] = React.useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = React.useState(false);
  const { userRole } = useAuth();
  
  const order = row.original as Order;
  
  // Determine if the user can edit orders
  const canEditOrders = userRole === 'admin' || userRole === 'staff';
  
  const handleViewOrder = () => {
    setIsViewDialogOpen(true);
  };
  
  const handleEditOrder = () => {
    setIsEditDialogOpen(true);
  };

  return (
    <>
      <TableRow data-state={row.getIsSelected() ? "selected" : ""}>
        <TableCell className="p-0 pl-2 w-[40px]">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => row.toggleExpanded()}
            className="h-8 w-8"
          >
            {row.getIsExpanded() ? (
              <ChevronDown className="h-4 w-4" />
            ) : (
              <ChevronRight className="h-4 w-4" />
            )}
          </Button>
        </TableCell>
        <TableCell className="p-0 w-[40px]">
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="Select row"
          />
        </TableCell>
        <TableCell>{order.id}</TableCell>
        <TableCell>
          {order.customer?.name || "Unknown"}
        </TableCell>
        <TableCell>
          {order.customerType === "walk-in" ? "Walk-in" : "Client"}
        </TableCell>
        <TableCell>
          <OrderStatusBadge status={order.status} />
        </TableCell>
        <TableCell className="text-right">
          ₱ {order.amount.toFixed(2)}
        </TableCell>
        <TableCell>
          <OrderActions 
            order={order} 
            onViewOrder={handleViewOrder} 
            onEditOrder={canEditOrders ? handleEditOrder : undefined}
            onOrderDeleted={onOrderDeleted}
          />
        </TableCell>
      </TableRow>

      {/* View Order Dialog */}
      <ViewOrderDialog
        order={order}
        open={isViewDialogOpen}
        onOpenChange={setIsViewDialogOpen}
        onStatusChange={onStatusChange}
      />
      
      {/* Edit Order Dialog - Only for admin/staff */}
      {canEditOrders && (
        <EditOrderDialog
          order={order}
          open={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
          onOrderUpdated={onOrderDeleted}
        />
      )}
    </>
  );
}
