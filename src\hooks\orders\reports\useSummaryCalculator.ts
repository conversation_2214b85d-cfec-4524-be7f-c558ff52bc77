
import { useCallback } from 'react';
import { OrderSummary, OrdersSummary } from './types';

export function useSummaryCalculator() {
  const calculateSummary = useCallback((orders: OrderSummary[]): OrdersSummary => {
    // Filter orders into paid, unpaid, and overdue
    const paidOrders = orders.filter(order => order.paidAmount >= order.amount);
    const unpaidOrders = orders.filter(order => order.paidAmount < order.amount);
    const overdueOrders = orders.filter(order => order.isOverdue);

    // Calculate totals
    const totalAmount = orders.reduce((sum, order) => sum + order.amount, 0);
    const totalPaid = orders.reduce((sum, order) => sum + order.paidAmount, 0);
    const totalPayable = totalAmount - totalPaid;

    return {
      totalOrders: orders.length,
      totalAmount,
      totalPaid,
      totalPayable,
      paidOrdersCount: paidOrders.length,
      unpaidOrdersCount: unpaidOrders.length,
      overdueOrdersCount: overdueOrders.length,
      paidOrders,
      unpaidOrders,
      overdueOrders
    };
  }, []);

  return { calculateSummary };
}
