
import React from "react";
import { <PERSON><PERSON>, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { UseFormReturn } from "react-hook-form";
import { OrderFormValues } from "../../OrderFormTypes";
import { extractFormErrors } from "../utils/errorHandlingUtils";

interface ClientFormErrorDisplayProps {
  errorMessages: { field: string; message: string }[];
  hasErrors: boolean;
}

// Component that accepts pre-extracted errors
export function ClientFormErrorDisplay({ errorMessages, hasErrors }: ClientFormErrorDisplayProps) {
  console.log("ClientFormErrorDisplay - Rendering with:", { hasErrors, errorMessages });
  
  if (!hasErrors || errorMessages.length === 0) {
    return null;
  }
  
  return (
    <Alert variant="destructive" className="mb-4">
      <AlertCircle className="h-4 w-4" />
      <AlertTitle>Error</AlertTitle>
      <AlertDescription>
        <ul className="list-disc pl-4">
          {errorMessages.map((err, idx) => (
            <li key={idx}>{err.message}</li>
          ))}
        </ul>
      </AlertDescription>
    </Alert>
  );
}

// Convenience component that extracts errors from the form
export function FormErrorDisplay({ form }: { form: UseFormReturn<OrderFormValues> }) {
  const { errorMessages, hasErrors } = extractFormErrors(form);
  console.log("FormErrorDisplay - Form errors:", { errorMessages, hasErrors, orderType: form.watch("orderType") });
  
  return <ClientFormErrorDisplay errorMessages={errorMessages} hasErrors={hasErrors} />;
}
