
import { FormField, FormItem, FormLabel, FormControl, FormDescription, FormMessage } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { UseFormReturn } from "react-hook-form";
import { OrderFormValues } from "./types";
import { useState } from "react";

interface DeliveryDateFieldProps {
  form: UseFormReturn<OrderFormValues>;
}

export function DeliveryDateField({ form }: DeliveryDateFieldProps) {
  const [open, setOpen] = useState(false);
  
  return (
    <FormField
      control={form.control}
      name="deliveryDate"
      render={({ field }) => (
        <FormItem className="flex flex-col">
          <FormLabel>Delivery Date *</FormLabel>
          <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
              <FormControl>
                <Button
                  variant="outline"
                  className={cn(
                    'w-full h-12 pl-3 text-left font-normal flex justify-between items-center',
                    !field.value && 'text-muted-foreground'
                  )}
                >
                  {field.value ? (
                    format(field.value, 'PPP')
                  ) : (
                    <span>Pick a date</span>
                  )}
                  <CalendarIcon className="h-5 w-5 ml-auto mr-2 opacity-70" />
                </Button>
              </FormControl>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0 pointer-events-auto shadow-lg rounded-md" align="start">
              <Calendar
                mode="single"
                selected={field.value}
                onSelect={(date) => {
                  if (date) {
                    field.onChange(date);
                    setOpen(false);
                  }
                }}
                initialFocus
                disabled={(date) => {
                  const today = new Date();
                  today.setHours(0, 0, 0, 0);
                  return date < today;
                }}
                className="border rounded-md shadow-sm"
              />
            </PopoverContent>
          </Popover>
          <FormDescription>
            When would you like your laundry to be delivered?
          </FormDescription>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
