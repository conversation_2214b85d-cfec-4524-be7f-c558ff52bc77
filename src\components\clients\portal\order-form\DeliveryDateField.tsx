import { FormField, FormItem, FormLabel, FormControl, FormDescription, FormMessage } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { UseFormReturn } from "react-hook-form";
import { OrderFormValues } from "./types";
import { useState } from "react";
import { useIsMobile } from "@/hooks/use-mobile";

interface DeliveryDateFieldProps {
  form: UseFormReturn<OrderFormValues>;
}

export function DeliveryDateField({ form }: DeliveryDateFieldProps) {
  const [open, setOpen] = useState(false);
  const isMobile = useIsMobile();
  
  return (
    <FormField
      control={form.control}
      name="deliveryDate"
      render={({ field }) => (
        <FormItem className="flex flex-col">
          <FormLabel>Delivery Date *</FormLabel>
          <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
              <FormControl>
                <Button
                  type="button"
                  variant="outline"
                  className={cn(
                    'w-full pl-3 text-left font-normal touch-button flex justify-between items-center',
                    !field.value && 'text-muted-foreground'
                  )}
                >
                  {field.value ? (
                    format(field.value, 'PPP')
                  ) : (
                    <span>Pick a date</span>
                  )}
                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                </Button>
              </FormControl>
            </PopoverTrigger>
            <PopoverContent
              className={cn(
                "w-auto p-0 z-50",
                isMobile ? "touch-action-manipulation" : ""
              )}
              align="start"
              sideOffset={5}
            >
              <div className="touch-action-manipulation">
                <Calendar
                  mode="single"
                  selected={field.value}
                  onSelect={(date) => {
                    if (date) {
                      field.onChange(date);
                      setOpen(false);
                    }
                  }}
                  disabled={(date) => date < new Date()}
                  className={cn(
                    "pointer-events-auto",
                    isMobile ? "touch-action-manipulation" : ""
                  )}
                />
              </div>
            </PopoverContent>
          </Popover>
          <FormDescription>
            When would you like your laundry to be delivered?
          </FormDescription>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
