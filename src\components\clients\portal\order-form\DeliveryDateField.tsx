import { FormField, FormItem, FormLabel, FormControl, FormDescription, FormMessage } from "@/components/ui/form";
import { UseFormReturn } from "react-hook-form";
import { OrderFormValues } from "./types";
import { AndroidDatePicker } from "@/components/ui/android-date-picker";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";

interface DeliveryDateFieldProps {
  form: UseFormReturn<OrderFormValues>;
}

export function DeliveryDateField({ form }: DeliveryDateFieldProps) {
  // Function to handle date selection - same as walk-in order form
  const handleDateSelect = (date: Date) => {
    const dateStr = format(date, "yyyy-MM-dd");
    console.log("DeliveryDateField: Date selected:", { date, dateStr });
    form.setValue("deliveryDate", dateStr);
  };

  return (
    <FormField
      control={form.control}
      name="deliveryDate"
      render={({ field }) => (
        <FormItem className="flex flex-col">
          <FormLabel className="text-base flex items-center">
            <CalendarIcon className="mr-2 h-4 w-4" />
            Delivery Date *
          </FormLabel>
          <FormControl>
            <AndroidDatePicker
              value={field.value ? new Date(field.value) : undefined}
              onChange={handleDateSelect}
              placeholder="Select date"
              minDate={new Date()}
              className="w-full"
            />
          </FormControl>
          <FormDescription>
            When would you like your laundry to be delivered?
          </FormDescription>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
