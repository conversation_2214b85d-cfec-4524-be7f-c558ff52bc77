import { FormField, FormItem, FormLabel, FormControl, FormDescription, FormMessage } from "@/components/ui/form";
import { UseFormReturn } from "react-hook-form";
import { OrderFormValues } from "./types";
import { AndroidDatePicker } from "@/components/ui/android-date-picker";

interface DeliveryDateFieldProps {
  form: UseFormReturn<OrderFormValues>;
}

export function DeliveryDateField({ form }: DeliveryDateFieldProps) {
  const handleDateSelect = (date: Date) => {
    console.log("DeliveryDateField: Date selected:", date);
    form.setValue("deliveryDate", date);
  };

  return (
    <FormField
      control={form.control}
      name="deliveryDate"
      render={({ field }) => (
        <FormItem className="flex flex-col">
          <FormLabel>Delivery Date *</FormLabel>
          <FormControl>
            <AndroidDatePicker
              value={field.value}
              onChange={handleDateSelect}
              placeholder="Pick a date"
              minDate={new Date()}
              className="w-full"
            />
          </FormControl>
          <FormDescription>
            When would you like your laundry to be delivered?
          </FormDescription>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
