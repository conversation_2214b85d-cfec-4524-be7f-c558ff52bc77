
import React, { useState } from "react";
import { FormField, FormItem, FormLabel, FormControl, FormDescription, FormMessage } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { UseFormReturn } from "react-hook-form";
import { OrderFormValues } from "./types";

interface DeliveryDateFieldProps {
  form: UseFormReturn<OrderFormValues>;
}

export function DeliveryDateField({ form }: DeliveryDateFieldProps) {
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);

  // Set a default date (tomorrow) if no date is selected - same as walk-in form
  React.useEffect(() => {
    const currentValue = form.watch("deliveryDate");
    if (!currentValue) {
      // Set default delivery date to tomorrow
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      form.setValue("deliveryDate", tomorrow);
    }
  }, [form]);

  // Format date for display - same as walk-in form
  const formatDate = (date: Date | undefined) => {
    if (!date) return "Pick a date";
    return format(date, "PPP");
  };

  return (
    <FormField
      control={form.control}
      name="deliveryDate"
      render={({ field }) => (
        <FormItem className="flex flex-col">
          <FormLabel className="text-base flex items-center">
            <CalendarIcon className="mr-2 h-4 w-4" />
            Delivery Date *
          </FormLabel>
          <Popover open={isPopoverOpen} onOpenChange={setIsPopoverOpen}>
            <PopoverTrigger asChild>
              <FormControl>
                <Button
                  variant="outline"
                  className={cn(
                    'w-full h-12 px-3 py-2 flex justify-between items-center text-left',
                    !field.value && 'text-muted-foreground'
                  )}
                >
                  <span className="truncate">{formatDate(field.value as Date)}</span>
                  <CalendarIcon className="h-4 w-4 ml-auto opacity-50" />
                </Button>
              </FormControl>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0 pointer-events-auto" align="start">
              <Calendar
                mode="single"
                selected={field.value as Date}
                onSelect={(date) => {
                  field.onChange(date);
                  setIsPopoverOpen(false);
                }}
                disabled={(date) => {
                  const today = new Date();
                  today.setHours(0, 0, 0, 0);
                  return date < today;
                }}
                initialFocus
                className="border rounded-md shadow-sm"
              />
            </PopoverContent>
          </Popover>
          <FormDescription>
            When would you like your laundry to be delivered?
          </FormDescription>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
