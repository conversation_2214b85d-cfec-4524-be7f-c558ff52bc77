
import { useEffect, useCallback } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useAuth } from "@/contexts/auth";
import { getDefaultRouteForRole } from "@/config/routes";
import { toast } from "sonner";

const Index = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { isAuthenticated, isLoading, userRole, refreshSession, signOut } = useAuth();
  
  // Handle session refreshing for edge cases
  useEffect(() => {
    // If loading takes too long, try to refresh the session
    if (isLoading) {
      const timeoutId = setTimeout(() => {
        console.log("Index: Auth loading taking too long, refreshing session...");
        refreshSession();
      }, 2000);
      
      return () => clearTimeout(timeoutId);
    }
  }, [isLoading, refreshSession]);
  
  // Handle role determination retries
  const handleRoleRetry = useCallback(async () => {
    // Get current retry count
    let retryCount = parseInt(sessionStorage.getItem('roleRetryCount') || '0');
    const maxRetries = 3;
    
    if (retryCount < maxRetries) {
      // Increment retry count and store it
      retryCount++;
      sessionStorage.setItem('roleRetryCount', retryCount.toString());
      
      console.log(`Role determination retry ${retryCount}/${maxRetries}...`);
      await refreshSession();
      return false; // Not max retries yet
    } else {
      // Max retries reached
      console.log("Max role determination retries reached, redirecting to login");
      sessionStorage.removeItem('roleRetryCount');
      toast.error("Unable to determine your user role. Please contact support.");
      await signOut();
      navigate("/login", { replace: true });
      return true; // Max retries reached
    }
  }, [refreshSession, signOut, navigate]);
  
  // Handle navigation after authentication state is determined
  useEffect(() => {
    if (isLoading) return;
    
    console.log("Index routing check:", { 
      isAuthenticated, 
      userRole 
    });
    
    if (isAuthenticated && userRole) {
      // Direct user to their appropriate landing page based on role
      const defaultRoute = getDefaultRouteForRole(userRole);
      console.log(`User is authenticated with role ${userRole}, navigating to ${defaultRoute}`);
      navigate(defaultRoute, { replace: true, state: { suppressToast: true } });
    } else if (isAuthenticated && !userRole) {
      console.log("User is authenticated but role is not determined yet");
      
      // Try role determination retry
      handleRoleRetry();
    } else {
      // User is not logged in, redirect to login
      console.log("User is not authenticated, navigating to login");
      sessionStorage.removeItem('roleRetryCount'); // Reset retry count
      navigate("/login", { replace: true });
    }
  }, [isLoading, isAuthenticated, userRole, navigate, handleRoleRetry]);
  
  // Simple loading display while figuring out where to navigate
  return (
    <div className="flex items-center justify-center h-screen">
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-2">Loading...</h1>
        <p>Please wait while we check your authentication status.</p>
      </div>
    </div>
  );
};

export default Index;
