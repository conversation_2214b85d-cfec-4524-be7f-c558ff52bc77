
import React from "react";
import { UseFormReturn } from "react-hook-form";
import { OrderFormValues } from "../OrderFormTypes";
import { SERVICE_TYPES } from "../pricing/constants";
import { 
  FormControl, 
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import { Info } from "lucide-react";
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { TouchCheckbox } from "@/components/ui/touch-checkbox";

interface ServiceSelectionProps {
  form: UseFormReturn<OrderFormValues>;
}

export function ServiceSelection({ form }: ServiceSelectionProps) {
  const handleServiceTypeToggle = (serviceType: string, checked: boolean) => {
    // Get current selected service types
    let currentSelectedTypes = form.watch("selectedServiceTypes") || [SERVICE_TYPES.WASH_DRY_FOLD];
    
    if (checked && !currentSelectedTypes.includes(serviceType)) {
      // Add the service type
      currentSelectedTypes = [...currentSelectedTypes, serviceType];
    } else if (!checked && currentSelectedTypes.includes(serviceType)) {
      // Remove the service type
      currentSelectedTypes = currentSelectedTypes.filter(type => type !== serviceType);
    }
    
    // Ensure we always have at least one service type selected
    if (currentSelectedTypes.length === 0) {
      currentSelectedTypes = [SERVICE_TYPES.WASH_DRY_FOLD];
    }
    
    // Update form values
    form.setValue("selectedServiceTypes", currentSelectedTypes);
    
    // Set primary service type for backward compatibility
    if (currentSelectedTypes.includes(SERVICE_TYPES.DRY_CLEANING)) {
      form.setValue("serviceType", SERVICE_TYPES.DRY_CLEANING);
    } else {
      form.setValue("serviceType", currentSelectedTypes[0]);
    }
    
    // Trigger validation
    form.trigger("serviceType");
    form.trigger("selectedServiceTypes");
  };

  const selectedServiceTypes = form.watch("selectedServiceTypes") || [SERVICE_TYPES.WASH_DRY_FOLD];

  return (
    <FormField
      control={form.control}
      name="selectedServiceTypes"
      render={({ field }) => (
        <FormItem className="w-full">
          <div className="flex items-center justify-between mb-1">
            <FormLabel className="text-base font-medium">Select Service</FormLabel>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Info className="h-4 w-4 text-muted-foreground cursor-help" />
                </TooltipTrigger>
                <TooltipContent className="max-w-[300px] p-4">
                  <div className="space-y-2">
                    <p className="font-medium">Service Types:</p>
                    <ul className="list-disc pl-4 space-y-1 text-sm">
                      <li><span className="font-medium">Wash & Fold:</span> Regular items at ₱28/kg (min 3kg)</li>
                      <li><span className="font-medium">Wash & Press:</span> Regular items with pressing at ₱28/kg (min 3kg)</li>
                      <li><span className="font-medium">Special Items:</span> Special fabrics at ₱28/kg (min 3kg)</li>
                      <li><span className="font-medium">Comforters:</span> ₱85/kg (min 2kg)</li>
                      <li><span className="font-medium">Towels/Curtains/Linens:</span> ₱65/kg (min 2kg)</li>
                      <li><span className="font-medium">Dry Cleaning:</span> Per piece pricing</li>
                    </ul>
                  </div>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>

          <div className="space-y-3">
            <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
              <div className="flex items-center space-x-2 border p-3 rounded-md">
                <TouchCheckbox 
                  id="wash-fold"
                  checked={selectedServiceTypes.includes(SERVICE_TYPES.WASH_DRY_FOLD)} 
                  onCheckedChange={(checked) => handleServiceTypeToggle(SERVICE_TYPES.WASH_DRY_FOLD, checked)}
                />
                <label 
                  htmlFor="wash-fold"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                >
                  Wash & Fold
                </label>
              </div>
              <div className="flex items-center space-x-2 border p-3 rounded-md">
                <TouchCheckbox 
                  id="wash-press"
                  checked={selectedServiceTypes.includes(SERVICE_TYPES.WASH_DRY_PRESS)} 
                  onCheckedChange={(checked) => handleServiceTypeToggle(SERVICE_TYPES.WASH_DRY_PRESS, checked)}
                />
                <label 
                  htmlFor="wash-press"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                >
                  Wash & Press
                </label>
              </div>
              <div className="flex items-center space-x-2 border p-3 rounded-md">
                <TouchCheckbox 
                  id="special-items"
                  checked={selectedServiceTypes.includes(SERVICE_TYPES.WASH_DRY_FOLD_SPECIAL)}
                  onCheckedChange={(checked) => handleServiceTypeToggle(SERVICE_TYPES.WASH_DRY_FOLD_SPECIAL, checked)}
                />
                <label 
                  htmlFor="special-items"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                >
                  Special Items
                </label>
              </div>
            </div>
            
            <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
              <div className="flex items-center space-x-2 border p-3 rounded-md">
                <TouchCheckbox 
                  id="comforters"
                  checked={selectedServiceTypes.includes(SERVICE_TYPES.COMFORTERS)}
                  onCheckedChange={(checked) => handleServiceTypeToggle(SERVICE_TYPES.COMFORTERS, checked)} 
                />
                <label 
                  htmlFor="comforters"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                >
                  Comforters
                </label>
              </div>
              <div className="flex items-center space-x-2 border p-3 rounded-md">
                <TouchCheckbox 
                  id="towels"
                  checked={selectedServiceTypes.includes(SERVICE_TYPES.TOWELS_CURTAINS_LINENS)}
                  onCheckedChange={(checked) => handleServiceTypeToggle(SERVICE_TYPES.TOWELS_CURTAINS_LINENS, checked)}
                />
                <label 
                  htmlFor="towels"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                >
                  Towels/Curtains
                </label>
              </div>
              <div className="flex items-center space-x-2 border p-3 rounded-md">
                <TouchCheckbox 
                  id="dry-cleaning"
                  checked={selectedServiceTypes.includes(SERVICE_TYPES.DRY_CLEANING)}
                  onCheckedChange={(checked) => handleServiceTypeToggle(SERVICE_TYPES.DRY_CLEANING, checked)}
                />
                <label 
                  htmlFor="dry-cleaning"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                >
                  Dry Cleaning
                </label>
              </div>
            </div>
          </div>
          
          <div className="mt-2 text-sm">
            {selectedServiceTypes.map((type, index) => (
              <div key={type} className="text-muted-foreground">
                {type === SERVICE_TYPES.WASH_DRY_FOLD && (
                  <p>• Regular laundry items at ₱28/kg (minimum 3kg)</p>
                )}
                {type === SERVICE_TYPES.WASH_DRY_PRESS && (
                  <p>• Regular laundry with pressing at ₱28/kg (minimum 3kg)</p>
                )}
                {type === SERVICE_TYPES.WASH_DRY_FOLD_SPECIAL && (
                  <p>• Special fabric items at ₱28/kg (minimum 3kg)</p>
                )}
                {type === SERVICE_TYPES.COMFORTERS && (
                  <p>• Comforters at ₱85/kg (minimum 2kg)</p>
                )}
                {type === SERVICE_TYPES.TOWELS_CURTAINS_LINENS && (
                  <p>• Towels, curtains and linens at ₱65/kg (minimum 2kg)</p>
                )}
                {type === SERVICE_TYPES.DRY_CLEANING && (
                  <p>• Dry cleaning with per-piece pricing</p>
                )}
              </div>
            ))}
          </div>
          
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
