
import { z } from "zod";
import { OrderFormValues, PriceBreakdown } from "../OrderFormTypes";

export interface OrderFormProps {
  onSubmit: (data: OrderFormValues & { 
    orderAmount: number;
    vatAmount: number;
    subtotal: number;
    reference_code?: string;
    isDryCleaning: boolean; // Make sure isDryCleaning is required, not optional
  }) => void;
  initialOrderType?: "walk-in" | "client";
  initialValues?: Record<string, any>;
  isSubmitting?: boolean;
}

// Re-export the PriceBreakdown type
export type { PriceBreakdown, OrderFormValues } from "../OrderFormTypes";
