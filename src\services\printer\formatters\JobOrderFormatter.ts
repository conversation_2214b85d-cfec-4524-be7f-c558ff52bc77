import { PrintOrderData } from "../types";
import { formatDateToLocaleString, formatCurrency } from "@/lib/utils";

export class JobOrderFormatter {
  private data: PrintOrderData;
  
  constructor(data: PrintOrderData) {
    this.data = data;
    // Log the data being passed to the job order formatter to verify it's complete
    console.log('Job Order formatter data:', data);
  }
  
  public getJobOrderContent(): string {
    const { id, orderDate, customer, amount, status, lineItems = [] } = this.data;
    
    let lineItemsHtml = '<div class="section"><h2>Items</h2>';
    
    // Process regular line items (with treatments)
    if (lineItems && lineItems.length > 0) {
      lineItemsHtml += `
        <div class="item-row">
          <span class="item-name"><strong>Item</strong></span>
          <span class="item-qty"><strong>Qty</strong></span>
          <span class="item-total"><strong>Total</strong></span>
        </div>
      `;
      
      lineItems.forEach(item => {
        // Include treatment info if present - check if property exists before accessing
        const treatmentInfo = item.treatmentDescription 
          ? ` (${item.treatmentDescription})` 
          : '';
        
        lineItemsHtml += `
          <div class="item-row">
            <span class="item-name">${item.name}${treatmentInfo}</span>
            <span class="item-qty">${item.quantity}</span>
            <span class="item-total">${formatCurrency(item.total || 0)}</span>
          </div>
        `;
      });
    } else {
      lineItemsHtml += "<p>No items specified</p>";
    }
    
    lineItemsHtml += '</div>';
    
    const isDryCleaning = this.data.isDryCleaning || this.data.serviceType === 'dry_cleaning';
    const isClient = this.data.customerType === 'client';
    
    // Generate instructions section
    let instructionsHtml = '';
    if (this.data.notes) {
      instructionsHtml = `
        <div class="section">
          <h2>Special Instructions</h2>
          <p class="notes">${this.data.notes}</p>
        </div>
      `;
    }
    
    // Generate service details section based on order type
    const serviceDetails = this.renderServiceDetails(isClient, isDryCleaning);
    
    // Generate washing supplements section if applicable
    const hasSupplements = this.data.useDetergent || 
                          this.data.useFabricConditioner || 
                          this.data.useStainRemover || 
                          this.data.useBleach;
    
    const supplementsSection = hasSupplements && !isDryCleaning
      ? this.renderSupplements()
      : '';
    
    // Generate dry cleaning items section if applicable
    const dryCleaningItemsSection = isDryCleaning && this.data.dryCleaningItems?.length
      ? this.renderDryCleaningItems()
      : '';
    
    // Update status name for "fulfilled" status
    const displayStatus = status === "fulfilled" ? "Complete Delivery" : status.replace(/_/g, ' ');
    
    return `
      <div class="job-order">
        <div class="header">
          <h1>CMC Express Laundry</h1>
          <div class="job-order-title">JOB ORDER</div>
          <p class="job-order-number">Order #: ${id}</p>
        </div>
        
        <div class="section">
          <div class="customer-details">
            <p><strong>Date:</strong> ${formatDateToLocaleString(orderDate)}</p>
            <p><strong>Customer:</strong> ${customer.name}</p>
            <p><strong>Phone:</strong> ${customer.phone}</p>
            ${customer.email ? `<p><strong>Email:</strong> ${customer.email}</p>` : ''}
            <p><strong>Status:</strong> ${displayStatus.toUpperCase()}</p>
          </div>
        </div>
        
        <div class="section">
          <h2>Service Details</h2>
          ${serviceDetails}
        </div>
        
        ${supplementsSection}
        
        ${dryCleaningItemsSection}
        
        ${lineItemsHtml}
        
        ${instructionsHtml}
        
        <div class="section total-section">
          <p class="total"><strong>Total Amount:</strong> ${formatCurrency(amount)}</p>
        </div>
        
        <div class="section signature-section">
          <div class="signature">
            <div class="signature-line"></div>
            <p>Staff Signature</p>
          </div>
          <div class="signature">
            <div class="signature-line"></div>
            <p>Customer Signature</p>
          </div>
        </div>
      </div>
      
      <style>
        .job-order {
          font-family: Arial, sans-serif;
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        .header {
          text-align: center;
          margin-bottom: 20px;
        }
        .header h1 {
          margin: 0;
          font-size: 24px;
        }
        .job-order-title {
          font-size: 18px;
          font-weight: bold;
          margin: 10px 0;
        }
        .job-order-number {
          font-size: 14px;
        }
        .section {
          margin-bottom: 20px;
        }
        .section h2 {
          font-size: 16px;
          margin-bottom: 10px;
          padding-bottom: 5px;
          border-bottom: 1px solid #ddd;
        }
        .customer-details p {
          margin: 5px 0;
          font-size: 14px;
        }
        .service-detail {
          display: flex;
          justify-content: space-between;
          margin-bottom: 5px;
          font-size: 14px;
        }
        .supplement-detail {
          margin-bottom: 5px;
          font-size: 14px;
        }
        .items-table {
          width: 100%;
          border-collapse: collapse;
        }
        .items-table th, .items-table td {
          border: 1px solid #ddd;
          padding: 8px;
          text-align: left;
        }
        .items-table th {
          background-color: #f2f2f2;
        }
        .notes {
          font-style: italic;
          padding: 10px;
          background-color: #f9f9f9;
          border-left: 3px solid #ddd;
        }
        .total-section {
          text-align: right;
          font-size: 16px;
        }
        .signature-section {
          display: flex;
          justify-content: space-between;
        }
        .signature {
          width: 45%;
          text-align: center;
        }
        .signature-line {
          height: 1px;
          background-color: #000;
          margin-bottom: 5px;
        }
        .item-row {
          display: flex;
          justify-content: space-between;
          font-size: 14px;
          margin: 5px 0;
        }
        .item-name {
          flex: 3;
        }
        .item-qty {
          flex: 1;
          text-align: center;
        }
        .item-price {
          flex: 1;
          text-align: right;
        }
        .item-total {
          flex: 1;
          text-align: right;
        }
      </style>
    `;
  }
  
  private renderServiceDetails(isClient: boolean, isDryCleaning: boolean): string {
    if (isDryCleaning) {
      return `
        <div class="service-detail">
          <span>Service:</span>
          <span>Dry Cleaning</span>
        </div>
        <div class="service-detail">
          <span>Delivery Date:</span>
          <span>${this.data.deliveryDate ? formatDateToLocaleString(this.data.deliveryDate) : 'Not specified'}</span>
        </div>
      `;
    }
    
    if (isClient) {
      // Client-specific service details without weight
      return `
        <div class="service-detail">
          <span>Service Type:</span>
          <span>${this.data.serviceType || 'Standard Service'}</span>
        </div>
        <div class="service-detail">
          <span>Pieces:</span>
          <span>${this.data.numberOfPieces || 0}</span>
        </div>
        <div class="service-detail">
          <span>Delivery Date:</span>
          <span>${this.data.deliveryDate ? formatDateToLocaleString(this.data.deliveryDate) : 'Not specified'}</span>
        </div>
      `;
    } else {
      // Walk-in service details with weight
      return `
        <div class="service-detail">
          <span>Service Type:</span>
          <span>${this.data.serviceType || 'Standard Service'}</span>
        </div>
        <div class="service-detail">
          <span>Weight:</span>
          <span>${this.data.weightKilos || 0} kg</span>
        </div>
        <div class="service-detail">
          <span>Pieces:</span>
          <span>${this.data.numberOfPieces || 0}</span>
        </div>
        <div class="service-detail">
          <span>Delivery Date:</span>
          <span>${this.data.deliveryDate ? formatDateToLocaleString(this.data.deliveryDate) : 'Not specified'}</span>
        </div>
      `;
    }
  }
  
  private renderSupplements(): string {
    const { useDetergent, useFabricConditioner, useStainRemover, useBleach, 
            detergentType, conditionerType, detergentQuantity, conditionerQuantity } = this.data;
    
    if (!useDetergent && !useFabricConditioner && !useStainRemover && !useBleach) {
      return '';
    }
    
    let supplementsHtml = '<div class="section"><h2>Washing Supplements</h2>';
    
    if (useDetergent && detergentType) {
      supplementsHtml += `
        <div class="supplement-detail">
          <strong>Detergent:</strong> ${detergentType} (${detergentQuantity || 1} ${Number(detergentQuantity) > 1 ? 'units' : 'unit'})
        </div>`;
    }
    
    if (useFabricConditioner && conditionerType) {
      supplementsHtml += `
        <div class="supplement-detail">
          <strong>Fabric Conditioner:</strong> ${conditionerType} (${conditionerQuantity || 1} ${Number(conditionerQuantity) > 1 ? 'units' : 'unit'})
        </div>`;
    }
    
    if (useStainRemover) {
      supplementsHtml += `
        <div class="supplement-detail">
          <strong>Stain Remover:</strong> Yes
        </div>`;
    }
    
    if (useBleach) {
      supplementsHtml += `
        <div class="supplement-detail">
          <strong>Bleach:</strong> Yes
        </div>`;
    }
    
    supplementsHtml += '</div>';
    return supplementsHtml;
  }
  
  private renderDryCleaningItems(): string {
    if (!this.data.dryCleaningItems || this.data.dryCleaningItems.length === 0) {
      return '';
    }
    
    let itemsHtml = '<div class="section"><h2>Dry Cleaning Items</h2>';
    
    // Table header
    itemsHtml += `
      <div class="item-row">
        <span class="item-name"><strong>Item</strong></span>
        <span class="item-qty"><strong>Qty</strong></span>
        <span class="item-price"><strong>Price</strong></span>
        <span class="item-total"><strong>Total</strong></span>
      </div>
    `;
    
    // Table rows
    let totalAmount = 0;
    this.data.dryCleaningItems.forEach(item => {
      const formattedItemName = item.name || this.formatItemType(item.type);
      const quantity = item.quantity || 1;
      const price = item.price || 0;
      const total = (price * quantity) || 0;
      totalAmount += total;
      
      itemsHtml += `
        <div class="item-row">
          <span class="item-name">${formattedItemName}</span>
          <span class="item-qty">${quantity}</span>
          <span class="item-price">${formatCurrency(price)}</span>
          <span class="item-total">${formatCurrency(total)}</span>
        </div>
      `;
    });
    
    // Total
    itemsHtml += `
      <div class="item-row" style="margin-top: 10px; font-weight: bold;">
        <span class="item-name"></span>
        <span class="item-qty"></span>
        <span class="item-price">Total:</span>
        <span class="item-total">${formatCurrency(totalAmount)}</span>
      </div>
    `;
    
    itemsHtml += '</div>';
    return itemsHtml;
  }
  
  // Helper method to format item type
  private formatItemType(type: string): string {
    return type
      .replace(/_/g, ' ')
      .replace(/\w\S*/g, txt => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase());
  }
  
  private renderLineItems(lineItems: Array<{name: string, quantity: number, total: number, treatmentDescription?: string}>): string {
    if (lineItems.length === 0) return "<p>No items specified</p>";
    
    let itemsHtml = `
      <div class="item-row">
        <span class="item-name"><strong>Item</strong></span>
        <span class="item-qty"><strong>Qty</strong></span>
        <span class="item-total"><strong>Total</strong></span>
      </div>
    `;
    
    lineItems.forEach(item => {
      // Add treatment description if available
      const treatmentInfo = item.treatmentDescription ? ` (${item.treatmentDescription})` : '';
      
      itemsHtml += `
        <div class="item-row">
          <span class="item-name">${item.name}${treatmentInfo}</span>
          <span class="item-qty">${item.quantity}</span>
          <span class="item-total">${formatCurrency(item.total)}</span>
        </div>
      `;
    });
    
    return itemsHtml;
  }
}
