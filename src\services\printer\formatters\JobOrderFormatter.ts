
import { COMMANDS } from '../constants';
import { PrintOrderData } from '../types';
import { FormattingUtils } from './FormattingUtils';

/**
 * Formats job orders for staff/internal use
 */
export class JobOrderFormatter {
  private formatting: FormattingUtils;
  
  constructor() {
    this.formatting = new FormattingUtils();
  }

  /**
   * Format order data into a job order slip for staff
   */
  public formatJobOrder(orderData: PrintOrderData): string {
    try {
      // Build job order content with ESC/POS commands
      let jobOrderContent = '';
      
      // Header
      jobOrderContent += COMMANDS.ALIGN_CENTER;
      jobOrderContent += COMMANDS.BOLD_ON;
      jobOrderContent += "CMC LAUNDRY\n";
      jobOrderContent += COMMANDS.BOLD_OFF;
      jobOrderContent += "-".repeat(this.formatting.getLineWidth()) + "\n";
      jobOrderContent += COMMANDS.BOLD_ON;
      jobOrderContent += "JOB ORDER\n";
      jobOrderContent += COMMANDS.BOLD_OFF;
      jobOrderContent += "-".repeat(this.formatting.getLineWidth()) + "\n";
      
      // Order info
      jobOrderContent += COMMANDS.ALIGN_LEFT;
      jobOrderContent += `Order ID: ${orderData.id}\n`;
      jobOrderContent += `Date: ${orderData.orderDate}\n`;
      
      // Service Type
      const serviceType = orderData.serviceType || "Regular Service";
      jobOrderContent += COMMANDS.BOLD_ON;
      jobOrderContent += `Service: ${serviceType.toUpperCase()}\n`;
      jobOrderContent += COMMANDS.BOLD_OFF;
      
      // Customer info
      jobOrderContent += `Customer: ${orderData.customer.name}\n`;
      jobOrderContent += `Phone: ${orderData.customer.phone}\n\n`;
      
      // Items with processing instructions
      jobOrderContent += COMMANDS.BOLD_ON;
      jobOrderContent += "ITEMS TO PROCESS:\n";
      jobOrderContent += COMMANDS.BOLD_OFF;
      
      if (orderData.lineItems && orderData.lineItems.length) {
        orderData.lineItems.forEach((item) => {
          jobOrderContent += `- ${item.name} x${item.quantity}\n`;
        });
      } else {
        jobOrderContent += "No specific items\n";
      }
      
      // Laundry specifications
      jobOrderContent += "\n";
      jobOrderContent += COMMANDS.BOLD_ON;
      jobOrderContent += "SPECIFICATIONS:\n";
      jobOrderContent += COMMANDS.BOLD_OFF;
      
      if (orderData.weightKilos) {
        jobOrderContent += `Weight: ${orderData.weightKilos} kg\n`;
      }
      
      if (orderData.numberOfPieces) {
        jobOrderContent += `Pieces: ${orderData.numberOfPieces}\n`;
      }
      
      // Processing instructions
      jobOrderContent += "\n";
      jobOrderContent += COMMANDS.BOLD_ON;
      jobOrderContent += "PROCESSING INSTRUCTIONS:\n";
      jobOrderContent += COMMANDS.BOLD_OFF;
      
      // Add-ons as processing instructions
      if (orderData.useDetergent) {
        jobOrderContent += `☐ Use Detergent: ${orderData.detergentType || "Standard"}\n`;
        jobOrderContent += `  Quantity: ${orderData.detergentQuantity || 1}\n`;
      }
      
      if (orderData.useFabricConditioner) {
        jobOrderContent += `☐ Use Fabric Conditioner: ${orderData.conditionerType || "Standard"}\n`;
        jobOrderContent += `  Quantity: ${orderData.conditionerQuantity || 1}\n`;
      }
      
      if (orderData.useStainRemover) {
        jobOrderContent += `☐ Apply Stain Remover Treatment\n`;
      }
      
      if (orderData.useBleach) {
        jobOrderContent += `☐ Apply Bleach Treatment\n`;
      }
      
      // Special notes section
      if (orderData.notes) {
        jobOrderContent += "\n";
        jobOrderContent += COMMANDS.BOLD_ON;
        jobOrderContent += "SPECIAL NOTES:\n";
        jobOrderContent += COMMANDS.BOLD_OFF;
        jobOrderContent += `${orderData.notes}\n`;
      }
      
      // Processing checklist
      jobOrderContent += "\n";
      jobOrderContent += COMMANDS.BOLD_ON;
      jobOrderContent += "STAFF PROCESSING CHECKLIST:\n";
      jobOrderContent += COMMANDS.BOLD_OFF;
      jobOrderContent += "☐ Sorting Complete\n";
      jobOrderContent += "☐ Pre-treatment Applied\n";
      jobOrderContent += "☐ Washing Complete\n";
      jobOrderContent += "☐ Drying Complete\n";
      jobOrderContent += "☐ Folding Complete\n";
      jobOrderContent += "☐ Quality Check\n";
      jobOrderContent += "☐ Ready for Pickup\n\n";
      
      // Staff signature
      jobOrderContent += "Assigned Staff: ________________\n\n";
      
      // Footer
      jobOrderContent += COMMANDS.ALIGN_CENTER;
      jobOrderContent += "-".repeat(this.formatting.getLineWidth()) + "\n";
      jobOrderContent += `Status: ${orderData.status.toUpperCase()}\n`;
      jobOrderContent += "-".repeat(this.formatting.getLineWidth()) + "\n";
      
      return jobOrderContent;
    } catch (error) {
      console.error('Error formatting job order:', error);
      return "ERROR FORMATTING JOB ORDER";
    }
  }
}
