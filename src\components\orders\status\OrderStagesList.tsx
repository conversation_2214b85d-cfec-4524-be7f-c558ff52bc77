
import { useIsMobile } from "@/hooks/use-mobile";
import { ScrollArea } from "@/components/ui/scroll-area";
import { TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Stage } from "./types";
import { memo } from "react";

interface OrderStagesListProps {
  stages: Stage[];
  selectedStage: string;
  onStageChange: (stageId: string) => void;
}

export const OrderStagesList = memo(function OrderStagesList({ 
  stages, 
  selectedStage,
  onStageChange
}: OrderStagesListProps) {
  const isMobile = useIsMobile();

  return (
    <ScrollArea className="w-full">
      <TabsList className="bg-white p-2 rounded-md mb-4 border flex flex-nowrap w-auto min-w-full gap-1 overflow-x-auto no-scrollbar">
        <TabsTrigger 
          value="all" 
          onClick={() => onStageChange("all")}
          className={`px-3 py-2 text-xs sm:text-sm whitespace-nowrap font-medium flex-shrink-0 ${selectedStage === "all" ? "bg-gray-100" : ""}`}
        >
          All
        </TabsTrigger>
        {stages.map((stage) => (
          <TabsTrigger 
            key={stage.id} 
            value={stage.id}
            onClick={() => onStageChange(stage.id)}
            className={`px-3 py-2 text-xs sm:text-sm whitespace-nowrap font-medium flex-shrink-0 ${
              selectedStage === stage.id ? `bg-gray-100 ${stage.color}` : ""
            }`}
          >
            {isMobile ? (
              <span className="truncate max-w-[60px] block">
                {stage.name}
              </span>
            ) : (
              stage.name
            )}
          </TabsTrigger>
        ))}
      </TabsList>
    </ScrollArea>
  );
});
