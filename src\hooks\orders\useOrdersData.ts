
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Order, CustomerType } from "@/types";
import { useToast } from "@/hooks/use-toast";
import { getOrdersStore, updateOrdersStore } from "@/services/orders/store";
import { formatSupabaseOrders } from "./helpers/formatSupabaseOrders";

export function useOrdersData() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const fetchOrders = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      console.log("Fetching orders from Supabase...");
      
      // Check Supabase session
      const { data: sessionData } = await supabase.auth.getSession();
      console.log('Supabase Session:', sessionData.session ? 'Active' : 'No active session');

      // Fetch all orders including client information
      const { data: ordersData, error: ordersError } = await supabase
        .from('orders')
        .select(`
          *,
          clients (
            name,
            phone,
            prefix
          )
        `)
        .order('created_at', { ascending: false });

      if (ordersError) {
        console.error("Supabase query error:", ordersError);
        throw ordersError;
      }

      console.log("Raw orders data:", ordersData ? ordersData.length : 0, "orders found");

      if (ordersData && ordersData.length > 0) {
        const formattedOrders = await formatSupabaseOrders(ordersData);
        console.log("Formatted orders:", formattedOrders.length);
        
        // Ensure all orders have proper customerType
        const validatedOrders = formattedOrders.map(order => {
          // If order doesn't have a customerType or it's invalid, determine based on other properties
          if (!order.customerType || !['client', 'walk-in'].includes(order.customerType)) {
            // If customer_name exists but no clientId, it's likely a walk-in order
            if (order.customer?.name && !order.clientId) {
              order.customerType = 'walk-in';
            } else if (order.clientId) {
              order.customerType = 'client';
            } else {
              // Default to walk-in if we can't determine
              order.customerType = 'walk-in';
            }
          }
          return order;
        });
        
        // Log order type breakdown
        const clientOrders = validatedOrders.filter(o => o.customerType === 'client');
        const walkInOrders = validatedOrders.filter(o => o.customerType === 'walk-in');
        console.log(`Order type breakdown: ${clientOrders.length} client orders, ${walkInOrders.length} walk-in orders`);
        
        setOrders(validatedOrders);
        updateOrdersStore(validatedOrders);
      } else {
        console.log("No orders found in database");
        setOrders([]);
        updateOrdersStore([]);
      }
    } catch (err) {
      console.error('Error fetching orders:', err);
      setError('Failed to load orders');
      toast({
        title: 'Error',
        description: 'Failed to load orders. Please try again.',
        variant: 'destructive',
      });
      setOrders([]);
      updateOrdersStore([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    console.log("useOrdersData hook initializing...");
    fetchOrders();
    
    // Set up subscription for real-time updates
    const channel = supabase
      .channel('orders-changes')
      .on(
        'postgres_changes',
        {
          event: '*', 
          schema: 'public',
          table: 'orders'
        },
        (payload) => {
          console.log("Real-time update received for orders", payload);
          fetchOrders();
        }
      )
      .subscribe((status) => {
        console.log("Supabase realtime subscription status:", status);
      });
    
    // Also listen for custom order-added events from the client side
    const handleOrderAdded = () => {
      console.log("Detected order-added event in useOrdersData");
      fetchOrders();
    };
    
    // Listen for status update events
    const handleStatusUpdated = () => {
      console.log("Detected order-status-updated event in useOrdersData");
      fetchOrders();
    };
    
    window.addEventListener('order-added', handleOrderAdded);
    window.addEventListener('order-status-updated', handleStatusUpdated);
    
    return () => {
      console.log("Cleaning up orders subscription");
      supabase.removeChannel(channel);
      window.removeEventListener('order-added', handleOrderAdded);
      window.removeEventListener('order-status-updated', handleStatusUpdated);
    };
  }, []);

  return {
    orders,
    isLoading,
    error,
    refreshOrders: fetchOrders
  };
}
