
import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>Title, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";

interface FormErrorProps {
  errorMessages: Array<{
    field: string;
    message: string;
  }>;
}

export function FormError({ errorMessages }: FormErrorProps) {
  if (!errorMessages || errorMessages.length === 0) {
    return null;
  }
  
  return (
    <Alert variant="destructive" className="mb-4">
      <AlertCircle className="h-4 w-4" />
      <AlertTitle>Error</AlertTitle>
      <AlertDescription>
        <ul className="list-disc pl-4">
          {errorMessages.map((err, idx) => <li key={idx}>{err.message}</li>)}
        </ul>
      </AlertDescription>
    </Alert>
  );
}
