
import { useState, useEffect } from "react";
import { ClientsPageContent } from "@/components/clients/page/ClientsPageContent";
import { ClientHeader } from "@/components/clients/header/ClientHeader";
import { AddClientDialog } from "@/components/clients/dialog/AddClientDialog";
import { ImportDialog } from "@/components/clients/import/ImportDialog";
import { ViewClientDialog } from "@/components/clients/ViewClientDialog";
import { Client, getClients } from "@/components/clients/ClientData";
import { ClientFormValues } from "@/components/clients/schemas/clientFormSchema";
import { toast } from "sonner";
import { DownloadTemplates } from "@/components/clients/import/DownloadTemplates";
import { AuthenticationNotice } from "@/components/clients/page/AuthenticationNotice";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { UserPlus } from "lucide-react";
import { Link } from "react-router-dom";
import { useAuth } from "@/contexts/auth";

export default function Clients() {
  const [isAddClientOpen, setIsAddClientOpen] = useState(false);
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false);
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [isViewClientOpen, setIsViewClientOpen] = useState(false);
  const { userRole } = useAuth();
  const [clientList, setClientList] = useState<Client[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast: uiToast } = useToast();
  
  // Fetch clients on initial load
  useEffect(() => {
    fetchClients();
  }, []);

  const fetchClients = async () => {
    setIsLoading(true);
    try {
      console.log("Fetching clients...");
      const clients = await getClients();
      console.log("Fetched clients:", clients.length);
      setClientList(clients);
      if (clients.length > 0) {
        toast(`Loaded ${clients.length} clients`);
      }
    } catch (error) {
      console.error("Failed to fetch clients:", error);
      toast("Failed to load clients", {
        description: "Please check your network connection",
        style: { backgroundColor: 'red', color: 'white' }
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddClient = async (clientData: ClientFormValues) => {
    console.log("handleAddClient called with data:", clientData);
    
    if (!clientData.createUserAccount) {
      const checkAuth = async () => {
        try {
          const { data } = await supabase.auth.getSession();
          if (!data.session) {
            console.log("User not authenticated, showing warning");
            uiToast({
              title: "Authentication Notice",
              description: "You are not logged in. Client data will be stored locally until you log in.",
              variant: "default",
            });
          }
        } catch (error) {
          console.error("Error checking authentication:", error);
        }
      };
      
      await checkAuth();
    }
    
    try {
      console.log("Calling addClient service function");
      const { addClient } = await import('@/components/clients/ClientData');
      const newClient = await addClient(clientData);
      
      if (newClient) {
        console.log("Client added successfully:", newClient);
        await fetchClients();
        setIsAddClientOpen(false);
        uiToast({
          title: "Client Added",
          description: `${newClient.name} has been added to your client list.`
        });
      } else {
        console.error("Failed to add client: returned null");
        uiToast({
          title: "Failed to Add Client",
          description: "The operation returned null. Please check the console for details.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Exception in handleAddClient:", error);
      uiToast({
        title: "Error Adding Client",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="space-y-6">
      <ClientHeader 
        onAddClick={() => setIsAddClientOpen(true)} 
        onImportClick={() => setIsImportDialogOpen(true)}
      />
      
      {userRole === "admin" && (
        <div className="flex items-center justify-end">
          <Button variant="outline" asChild>
            <Link to="/client-user-setup">
              <UserPlus className="mr-2 h-4 w-4" />
              Create User Accounts for Clients
            </Link>
          </Button>
        </div>
      )}
      
      <AuthenticationNotice />
      
      <div className="mb-4">
        <DownloadTemplates />
      </div>
      
      <ClientsPageContent 
        clientList={clientList}
        isLoading={isLoading}
        onViewClient={(client) => {
          setSelectedClient(client);
          setIsViewClientOpen(true);
        }}
      />
      
      <AddClientDialog 
        isOpen={isAddClientOpen} 
        onOpenChange={setIsAddClientOpen}
        onAddClient={handleAddClient}
      />
      
      <ViewClientDialog
        client={selectedClient}
        open={isViewClientOpen}
        onOpenChange={setIsViewClientOpen}
        onClientDeleted={fetchClients}
      />
      
      <ImportDialog 
        open={isImportDialogOpen}
        onOpenChange={setIsImportDialogOpen}
        onImportComplete={fetchClients}
      />
    </div>
  );
}
