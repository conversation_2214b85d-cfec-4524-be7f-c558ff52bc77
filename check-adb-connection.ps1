# Script to help troubleshoot ADB connection issues
Write-Host "ADB Connection Troubleshooter" -ForegroundColor Cyan
Write-Host "============================" -ForegroundColor Cyan
Write-Host ""

# Check if ADB is available
try {
    $adbVersion = adb version
    Write-Host "ADB is installed and available:" -ForegroundColor Green
    Write-Host $adbVersion -ForegroundColor Gray
} catch {
    Write-Host "Error: ADB is not available in the PATH" -ForegroundColor Red
    Write-Host "Make sure Android SDK Platform Tools are installed and added to your PATH" -ForegroundColor Yellow
    exit
}

# Restart ADB server
Write-Host "`nRestarting ADB server..." -ForegroundColor Yellow
adb kill-server
Start-Sleep -Seconds 2
adb start-server

# Check for connected devices
Write-Host "`nChecking for connected devices..." -ForegroundColor Yellow
$devices = adb devices
Write-Host $devices -ForegroundColor Gray

if ($devices -match "device$") {
    Write-Host "Device found!" -ForegroundColor Green
} else {
    Write-Host "No devices found. Let's troubleshoot:" -ForegroundColor Red
    
    # USB Debugging check
    Write-Host "`nUSB Debugging Checklist:" -ForegroundColor Yellow
    Write-Host "1. On your Android device, go to Settings > About phone" -ForegroundColor White
    Write-Host "2. Tap 'Build number' 7 times to enable Developer options" -ForegroundColor White
    Write-Host "3. Go to Settings > System > Developer options" -ForegroundColor White
    Write-Host "4. Enable 'USB debugging'" -ForegroundColor White
    Write-Host "5. Connect your device with a USB cable" -ForegroundColor White
    Write-Host "6. When prompted on your device, tap 'Allow' for USB debugging" -ForegroundColor White
    
    # USB connection check
    Write-Host "`nUSB Connection Checklist:" -ForegroundColor Yellow
    Write-Host "1. Try a different USB cable" -ForegroundColor White
    Write-Host "2. Try a different USB port on your computer" -ForegroundColor White
    Write-Host "3. Make sure your device is not in 'Charge only' mode" -ForegroundColor White
    Write-Host "   - Check the notification on your device when connected" -ForegroundColor White
    Write-Host "   - Select 'File Transfer' or 'MTP' mode" -ForegroundColor White
    
    # Driver check
    Write-Host "`nUSB Driver Checklist:" -ForegroundColor Yellow
    Write-Host "1. Install the appropriate USB drivers for your device:" -ForegroundColor White
    Write-Host "   - Samsung: https://developer.samsung.com/android-usb-driver" -ForegroundColor White
    Write-Host "   - Google: https://developer.android.com/studio/run/win-usb" -ForegroundColor White
    Write-Host "   - Other: Check the manufacturer's website" -ForegroundColor White
    
    # Alternative installation methods
    Write-Host "`nAlternative Installation Methods:" -ForegroundColor Yellow
    Write-Host "If you can't get ADB working, try these alternatives:" -ForegroundColor White
    Write-Host "1. Transfer the APK directly to your device using:" -ForegroundColor White
    Write-Host "   - USB file transfer" -ForegroundColor White
    Write-Host "   - Email attachment" -ForegroundColor White
    Write-Host "   - Cloud storage (Google Drive, Dropbox, etc.)" -ForegroundColor White
    Write-Host "2. Use the provided apk-download.html file:" -ForegroundColor White
    Write-Host "   - Host it on a local or public web server" -ForegroundColor White
    Write-Host "   - Access it from your device's browser" -ForegroundColor White
    Write-Host "   - Download and install the APK" -ForegroundColor White
}

Write-Host "`nPress any key to check for devices again, or Ctrl+C to exit..." -ForegroundColor Cyan
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# Check again after user has made changes
Write-Host "`nChecking for connected devices again..." -ForegroundColor Yellow
$devices = adb devices
Write-Host $devices -ForegroundColor Gray

if ($devices -match "device$") {
    Write-Host "Device found! You can now install the APK using:" -ForegroundColor Green
    Write-Host "adb install cmc-laundry-pos.apk" -ForegroundColor White
} else {
    Write-Host "Still no devices found. Please review the troubleshooting steps above." -ForegroundColor Red
}
