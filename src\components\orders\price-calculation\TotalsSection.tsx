
import { PriceBreakdown } from "../OrderFormTypes";
import { formatCurrency } from "./utils";

interface TotalsSectionProps {
  priceBreakdown: PriceBreakdown;
}

export function TotalsSection({ priceBreakdown }: TotalsSectionProps) {
  return (
    <div className="p-4">
      <div className="flex justify-between mb-2">
        <span className="text-base">Subtotal:</span>
        <span className="text-base">{formatCurrency(priceBreakdown.subtotal)}</span>
      </div>
      
      <div className="flex justify-between mb-2">
        <span className="text-base">VAT (12%):</span>
        <span className="text-base">{formatCurrency(priceBreakdown.vatAmount)}</span>
      </div>
      
      <div className="flex justify-between text-lg font-bold pt-2">
        <span>Total:</span>
        <span className="text-laundry-blue">{formatCurrency(priceBreakdown.totalPrice)}</span>
      </div>
    </div>
  );
}
