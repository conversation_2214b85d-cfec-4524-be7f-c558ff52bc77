
import { SERVICE_TYPES } from "../pricing/constants";

/**
 * Formats a number as a currency string
 * @param amount Amount to format
 * @returns Formatted currency string
 */
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'PHP',
    minimumFractionDigits: 2,
  }).format(amount);
}

/**
 * Returns a formatted label for a service type
 * @param serviceType The service type identifier
 * @returns Human-readable service label
 */
export function getServiceLabel(serviceType: string): string {
  switch (serviceType) {
    case SERVICE_TYPES.WASH_DRY_FOLD:
      return "Wash, Dry & Fold";
    case SERVICE_TYPES.WASH_DRY_PRESS:
      return "Wash, Dry & Press";
    case SERVICE_TYPES.DRY_CLEANING:
      return "Dry Cleaning";
    case SERVICE_TYPES.COMFORTERS:
      return "Comforters";
    case SERVICE_TYPES.TOWELS_CURTAINS_LINENS:
      return "Towels, Curtains & Linens";
    case SERVICE_TYPES.WASH_DRY_FOLD_SPECIAL:
      return "Special Wash & Fold";
    default:
      return `Service: ${serviceType}`;
  }
}
