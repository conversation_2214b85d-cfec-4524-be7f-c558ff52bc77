
import { useOrdersPage } from "@/hooks/orders/useOrdersPage";
import { OrdersHeader } from "@/components/orders/page/OrdersHeader";
import { OrdersNavigation } from "@/components/orders/page/OrdersNavigation";
import { OrdersTabsContent } from "@/components/orders/page/OrdersTabsContent";
import { OrdersDialogs } from "@/components/orders/page/OrdersDialogs";
import { useState } from "react";

export default function Orders() {
  const [orderType, setOrderType] = useState<"walk-in" | "client">("walk-in");
  
  const {
    isRefreshing,
    isLoading,
    error,
    isAddOrderOpen,
    setIsAddOrderOpen,
    activeTab,
    setActiveTab,
    selectedOrder,
    isViewOrderOpen,
    setIsViewOrderOpen,
    refreshOrders,
    handleManualRefresh,
    handleViewOrder,
    handleStatusChange,
    searchTerm,
    setSearchTerm,
    statusFilter,
    setStatusFilter,
    dateFilter,
    setDateFilter,
    clientFilter,
    setClientFilter,
    clientOrders,
    walkInOrders,
    getClientOptions,
    orders
  } = useOrdersPage();

  // Handler for opening the dialog with specific order type
  const handleAddOrder = (type: "walk-in" | "client") => {
    setOrderType(type);
    setIsAddOrderOpen(true);
  };

  return (
    <div className="space-y-6">
      <OrdersHeader 
        onRefresh={handleManualRefresh} 
        onAddOrder={handleAddOrder}
        isLoading={isLoading} 
        isRefreshing={isRefreshing} 
      />

      <OrdersNavigation />
      
      <OrdersTabsContent
        isLoading={isLoading}
        error={error}
        refreshOrders={refreshOrders}
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        clientOrders={clientOrders}
        walkInOrders={walkInOrders}
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        statusFilter={statusFilter}
        setStatusFilter={setStatusFilter}
        dateFilter={dateFilter}
        setDateFilter={setDateFilter}
        clientFilter={clientFilter}
        setClientFilter={setClientFilter}
        clientOptions={getClientOptions(orders)}
        onViewOrder={handleViewOrder}
        onOrderDeleted={refreshOrders}
      />

      <OrdersDialogs
        isAddOrderOpen={isAddOrderOpen}
        setIsAddOrderOpen={setIsAddOrderOpen}
        refreshOrders={refreshOrders}
        activeTab={activeTab}
        selectedOrder={selectedOrder}
        isViewOrderOpen={isViewOrderOpen}
        setIsViewOrderOpen={setIsViewOrderOpen}
        onStatusChange={handleStatusChange}
        initialOrderType={orderType}
      />
    </div>
  );
}
