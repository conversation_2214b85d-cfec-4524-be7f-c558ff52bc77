
import React from "react";
import { Loader2 } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { getStatusDisplayName } from "./utils/statusUtils";

interface StatusSelectorProps {
  currentStatus: string;
  availableStatuses: string[];
  isUpdating: boolean;
  onStatusChange: (status: string) => void;
}

export const StatusSelector: React.FC<StatusSelectorProps> = ({
  currentStatus,
  availableStatuses,
  isUpdating,
  onStatusChange
}) => {
  return (
    <div className="space-y-2">
      <label className="text-sm font-medium">Change Status:</label>
      <div className="flex items-center gap-2">
        <Select
          onValueChange={onStatusChange}
          disabled={isUpdating}
          value={currentStatus}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select new status" />
          </SelectTrigger>
          <SelectContent>
            {availableStatuses.map((status) => (
              <SelectItem key={status} value={status}>
                {getStatusDisplayName(status)}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        
        {isUpdating && (
          <div className="flex items-center gap-1 text-sm text-muted-foreground">
            <Loader2 className="h-3 w-3 animate-spin" />
            <span>Updating...</span>
          </div>
        )}
      </div>
    </div>
  );
};
