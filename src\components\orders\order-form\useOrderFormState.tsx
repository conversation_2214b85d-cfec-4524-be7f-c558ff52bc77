
import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { orderFormSchema, PriceBreakdown, ServiceWeight } from "../OrderFormTypes";
import { useOrderFormSubmit } from "@/hooks/useOrderFormSubmit";
import { SERVICE_TYPES } from "../pricing/constants";

export function useOrderFormState(
  onSubmit: (data: any) => void,
  initialOrderType: "walk-in" | "client" = "walk-in",
  isSubmitting = false
) {
  const [deliveryDate, setDeliveryDate] = useState("");
  const [priceBreakdown, setPriceBreakdown] = useState<PriceBreakdown>({
    basePrice: 0,
    addOnPrice: 0,
    subtotal: 0,
    vatAmount: 0,
    totalPrice: 0
  });

  // Set initial form values based on order type
  const defaultPricingMethod = initialOrderType === "client" ? "client_item" : "weight";
  const form = useForm<typeof orderFormSchema._type>({
    resolver: zodResolver(orderFormSchema),
    defaultValues: {
      customerName: "",
      phoneNumber: "",
      deliveryDate: "",
      weightKilos: "3.0",
      // Default to 3kg (minimum for regular service)
      numberOfPieces: "1",
      pricingMethod: defaultPricingMethod,
      detergentType: "none" as "none" | "regular" | "color",
      detergentQuantity: "1",
      conditionerType: "none" as "none" | "regular" | "fresh" | "floral",
      conditionerQuantity: "1",
      useStainRemover: false,
      useBleach: false,
      useDetergent: false,
      useFabricConditioner: false,
      paidAmount: "0",
      clientId: "",
      orderType: initialOrderType as "walk-in" | "client",
      serviceType: SERVICE_TYPES.WASH_DRY_FOLD,
      selectedClientItems: [],
      dryCleaningItems: [],
      // Track multiple service types
      selectedServiceTypes: [SERVICE_TYPES.WASH_DRY_FOLD],
      // Track service weights with properly defined types
      serviceWeights: [
        { 
          serviceType: SERVICE_TYPES.WASH_DRY_FOLD, 
          weightKilos: 3.0 
        }
      ]
    },
    mode: "onChange" // Enable validation on change for better user experience
  });

  // Initialize form with order type
  useEffect(() => {
    if (initialOrderType) {
      form.setValue("orderType", initialOrderType as "walk-in" | "client");
      if (initialOrderType === "client") {
        form.setValue("pricingMethod", "client_item");
        // Clear customer name and phone validation for client orders
        form.clearErrors("customerName");
        form.clearErrors("phoneNumber");
      }
    }
  }, [initialOrderType, form]);

  const {
    handleSubmit,
    isSubmitting: formSubmitting
  } = useOrderFormSubmit(form, priceBreakdown, onSubmit);

  // Combine external and internal submission states
  const submissionInProgress = isSubmitting || formSubmitting;

  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setDeliveryDate(e.target.value);
    form.setValue("deliveryDate", e.target.value);
  };

  return {
    form,
    priceBreakdown,
    setPriceBreakdown,
    deliveryDate,
    handleDateChange,
    handleSubmit,
    isSubmitting: submissionInProgress
  };
}
