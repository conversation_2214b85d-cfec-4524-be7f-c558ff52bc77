
import { supabase } from "@/integrations/supabase/client";
import { auditService } from "@/services/audit/auditService";
import { notificationService } from "@/services/notifications/notificationService";
import { getAuthSession } from "@/services/auth/utils";
import { getOrderUuidFromShortId } from "./helpers";

/**
 * Update order status in database
 */
export async function updateOrderStatus(
  orderId: string,
  newStatus: string
): Promise<boolean> {
  try {
    console.log(`Updating order status: ${orderId} -> ${newStatus}`);
    
    // Get full UUID if needed
    const orderIdOrUuid = orderId.length < 36 ? await getOrderUuidFromShortId(orderId) : orderId;
    if (!orderIdOrUuid) {
      console.error("Could not find UUID for order ID:", orderId);
      return false;
    }

    // Get the current status before updating
    const { data: currentOrderData, error: fetchError } = await supabase
      .from("orders")
      .select("status")
      .eq("id", orderIdOrUuid)
      .single();
      
    if (fetchError) {
      console.error("Error fetching current order status:", fetchError);
    }
    
    const oldStatus = currentOrderData?.status || "unknown";

    // Update in Supabase
    const { error } = await supabase
      .from("orders")
      .update({ status: newStatus, updated_at: new Date().toISOString() })
      .eq("id", orderIdOrUuid);

    if (error) {
      console.error("Error updating order status:", error);
      return false;
    }

    // Log the action
    await auditService.logOrderAction("update_status", orderId, {
      oldStatus,
      newStatus,
    });

    // Send notification to staff
    const session = await getAuthSession();
    const userEmail = session?.user?.email || "Unknown user";
    await notificationService.notifyStaff(
      "order_status_changed",
      `Order ${orderId} status changed to ${newStatus} by ${userEmail}`,
      { orderId, newStatus }
    );

    return true;
  } catch (error) {
    console.error("Error in updateOrderStatus:", error);
    return false;
  }
}
