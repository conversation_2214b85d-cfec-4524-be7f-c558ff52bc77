
import { Alert<PERSON>ir<PERSON>, <PERSON>rk<PERSON> } from "lucide-react";

interface DryCleaningIndicatorProps {
  isDryCleaning: boolean;
}

export function DryCleaningIndicator({ isDryCleaning }: DryCleaningIndicatorProps) {
  if (!isDryCleaning) return null;
  
  return (
    <div className="bg-amber-50 border border-amber-200 p-2 rounded-md flex items-center gap-2 mb-4">
      <Sparkles className="h-4 w-4 text-amber-600" />
      <span className="text-sm text-amber-800">
        This order is marked as a Dry Cleaning service
      </span>
    </div>
  );
}
