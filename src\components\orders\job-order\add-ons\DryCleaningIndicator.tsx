
import React from "react";
import { AlertTriangle } from "lucide-react";
import { DryCleaningItem } from "@/types";
import { SERVICE_TYPES } from "../../pricing/constants";

interface DryCleaningIndicatorProps {
  isDryCleaning: boolean;
  serviceType?: string;
  dryCleaningItems?: DryCleaningItem[];
}

export function DryCleaningIndicator({
  isDryCleaning,
  serviceType,
  dryCleaningItems
}: DryCleaningIndicatorProps) {
  // Check if this is a dry cleaning service by either isDryCleaning flag OR serviceType
  const isDryCleaningService = isDryCleaning || serviceType === SERVICE_TYPES.DRY_CLEANING;
  
  // Skip rendering if not dry cleaning
  if (!isDryCleaningService) {
    return null;
  }

  // Make sure dryCleaningItems is an array
  const items = Array.isArray(dryCleaningItems) ? dryCleaningItems : [];

  // Count total items if available
  const totalItems = items.reduce((sum, item) => sum + (item.quantity || 0), 0);

  // Return the actual component UI
  return (
    <div className="flex items-center gap-2 p-2 mb-4 bg-amber-50 border border-amber-200 rounded-md">
      <AlertTriangle className="h-5 w-5 text-amber-500" />
      <div className="text-sm font-medium text-amber-800">
        Dry Cleaning Service 
        {totalItems > 0 ? ` (${totalItems} items)` : ''}
      </div>
    </div>
  );
}
