@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 210 100% 50%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 210 100% 50%;
    --sidebar-foreground: 0 0% 100%;
    --sidebar-primary: 0 0% 100%;
    --sidebar-primary-foreground: 210 100% 50%;
    --sidebar-accent: 210 90% 45%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 210 90% 45%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-gray-50 text-foreground font-sans;
  }
  
  /* Enhanced touch targets for better tablet usability */
  input, select, button, .touch-target {
    @apply min-h-[44px];
  }
  
  /* Improve form controls for touch */
  input[type="radio"],
  input[type="checkbox"],
  [data-radix-checkbox-root] {
    @apply min-h-[24px] min-w-[24px] touch-action-manipulation;
  }
}

/* Touch action manipulation for better mobile experience */
.touch-action-manipulation {
  touch-action: manipulation;
}

/* Custom touch checkbox styles */
.touch-checkbox {
  @apply h-6 w-6 rounded-md border flex items-center justify-center touch-action-manipulation;
}

.touch-checkbox-checked {
  @apply bg-primary text-primary-foreground;
}

@layer utilities {
  /* Hide scrollbar but maintain functionality */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  
  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
}

/* Order Status Badge Colors */
.status-processing {
  @apply bg-laundry-orange text-white;
}

.status-ready {
  @apply bg-laundry-green text-white;
}

.status-delivered {
  @apply bg-blue-500 text-white;
}

.status-returned {
  @apply bg-laundry-red text-white;
}

.status-pending {
  @apply bg-laundry-gray text-white;
}

/* Touch-friendly styles for tap targets */
@layer components {
  .touch-input {
    @apply h-12 text-base;
  }
  
  .touch-select {
    @apply h-12;
  }
  
  .touch-button {
    @apply h-12 text-base px-5;
  }
  
  .touch-checkbox {
    @apply h-6 w-6;
  }
  
  .touch-card {
    @apply p-4 rounded-lg shadow-sm;
  }
  
  /* Calendar component styles for better mobile experience */
  .rdp {
    @apply w-full max-w-full touch-action-manipulation;
  }
  
  .rdp-month {
    @apply w-full;
  }
  
  .rdp-day {
    @apply min-h-[40px] min-w-[40px] h-10 w-10;
  }
  
  .rdp-button {
    @apply touch-action-manipulation;
  }
  
  /* Responsive button groups */
  .button-group {
    @apply flex flex-wrap gap-2;
  }
  
  /* Card adaptations for mobile */
  .mobile-friendly-card {
    @apply p-3 md:p-6;
  }
  
  /* Responsive text sizing */
  .responsive-text {
    @apply text-sm md:text-base;
  }
  
  .responsive-heading {
    @apply text-lg md:text-xl lg:text-2xl;
  }
}

/* Add some extra space to all form inputs on mobile */
@media (max-width: 640px) {
  input, select, button {
    @apply mb-1;
  }
  
  label {
    @apply mb-1;
  }
  
  .card-compact {
    @apply p-3;
  }
  
  .mobile-stretch {
    @apply w-full;
  }
  
  /* Improve tab appearance on mobile */
  .tabs-container [role="tab"] {
    @apply py-2 px-3 text-sm;
  }
  
  /* Make sure content doesn't overflow on small screens */
  .table-container {
    @apply overflow-x-auto -mx-4 px-4;
  }
  
  /* Make date picker elements more touch-friendly */
  .rdp-day, .rdp-button {
    @apply min-h-[44px] min-w-[44px];
  }
  
  /* Ensure calendar has enough space in mobile view */
  .rdp-months {
    @apply justify-center;
  }
}

/* Add tap highlight effect for better touch feedback */
.tap-highlight {
  @apply active:bg-blue-50 transition-colors duration-100;
}

/* Visually indicate active/focused elements */
.touch-active:active {
  @apply ring-2 ring-blue-400;
}

/* Allow tables to be scrollable on mobile */
.table-responsive {
  @apply overflow-x-auto whitespace-nowrap;
}

/* Ensure proper spacing on mobile cards */
.card-section {
  @apply px-3 py-2 md:px-6 md:py-4;
}
