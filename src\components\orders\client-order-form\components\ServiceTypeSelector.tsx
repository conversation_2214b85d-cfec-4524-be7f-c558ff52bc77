
import React from "react";
import { UseFormReturn } from "react-hook-form";
import { OrderFormValues } from "../../OrderFormTypes";
import { Card, CardContent } from "@/components/ui/card";
import { FormItem, FormLabel } from "@/components/ui/form";
import { Checkbox } from "@/components/ui/checkbox";
import { SERVICE_TYPES } from "../../pricing/constants";

interface ServiceTypeSelectorProps {
  form: UseFormReturn<OrderFormValues>;
  selectedServiceTypes: string[];
  handleServiceTypeToggle: (serviceType: string, checked: boolean) => void;
}

export function ServiceTypeSelector({ 
  form, 
  selectedServiceTypes, 
  handleServiceTypeToggle 
}: ServiceTypeSelectorProps) {
  return (
    <Card className="border-border">
      <CardContent className="pt-4">
        <FormItem className="space-y-3">
          <FormLabel className="text-base font-medium">Service Type</FormLabel>
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center space-x-3 rounded-md border p-3 cursor-pointer hover:bg-accent">
              <Checkbox 
                id="wash-fold-checkbox"
                checked={selectedServiceTypes.includes(SERVICE_TYPES.WASH_DRY_FOLD)}
                onCheckedChange={(checked) => {
                  handleServiceTypeToggle(SERVICE_TYPES.WASH_DRY_FOLD, checked === true);
                }}
              />
              <FormLabel 
                htmlFor="wash-fold-checkbox"
                className="font-normal cursor-pointer flex-1"
                onClick={() => {
                  const isCurrentlySelected = selectedServiceTypes.includes(SERVICE_TYPES.WASH_DRY_FOLD);
                  handleServiceTypeToggle(SERVICE_TYPES.WASH_DRY_FOLD, !isCurrentlySelected);
                }}
              >
                Wash, Dry & Fold
              </FormLabel>
            </div>
            <div className="flex items-center space-x-3 rounded-md border p-3 cursor-pointer hover:bg-accent">
              <Checkbox 
                id="dry-cleaning-checkbox"
                checked={selectedServiceTypes.includes(SERVICE_TYPES.DRY_CLEANING)}
                onCheckedChange={(checked) => {
                  handleServiceTypeToggle(SERVICE_TYPES.DRY_CLEANING, checked === true);
                }}
              />
              <FormLabel 
                htmlFor="dry-cleaning-checkbox"
                className="font-normal cursor-pointer flex-1"
                onClick={() => {
                  const isCurrentlySelected = selectedServiceTypes.includes(SERVICE_TYPES.DRY_CLEANING);
                  handleServiceTypeToggle(SERVICE_TYPES.DRY_CLEANING, !isCurrentlySelected);
                }}
              >
                Dry Cleaning Service
              </FormLabel>
            </div>
          </div>
        </FormItem>
      </CardContent>
    </Card>
  );
}
