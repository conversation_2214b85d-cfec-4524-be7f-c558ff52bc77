
import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { X } from "lucide-react";
import { Order } from "@/types";
import { OrderStatusWorkflow } from "./workflow/OrderStatusWorkflow";
import { OrderDetailsForm } from "./edit-form/OrderDetailsForm";
import { FormActions } from "./edit-form/FormActions";
import { useOrderForm } from "./edit-form/useOrderForm";
import { useAuth } from "@/contexts/auth";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

interface EditOrderDialogProps {
  order: Order | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onOrderUpdated?: () => void;
}

export function EditOrderDialog({
  order,
  open,
  onOpenChange,
  onOrderUpdated,
}: <PERSON><PERSON>rder<PERSON>ialogProps) {
  const { userRole } = useAuth();
  const { toast } = useToast();
  
  // Only allow admin or staff to edit orders
  const canEdit = userRole === 'admin' || userRole === 'staff';
  
  // Form handling with custom hook
  const {
    formData,
    isSubmitting,
    handleInputChange,
    handleNumberChange,
    handleCustomerTypeChange,
    handleDryCleaningChange,
    handleSubmit
  } = useOrderForm(order, onOrderUpdated);

  // Handle status change
  const handleStatusChange = async (orderId: string, newStatus: string) => {
    try {
      if (!order) return;
      
      const { error } = await supabase
        .from('orders')
        .update({ status: newStatus })
        .eq('id', order.uuid);
        
      if (error) {
        console.error('Error updating order status:', error);
        toast({
          title: "Status Update Failed",
          description: error.message,
          variant: "destructive",
        });
        return;
      }
      
      toast({
        title: "Status Updated",
        description: `Order status changed to ${newStatus}`,
      });
      
      if (onOrderUpdated) onOrderUpdated();
    } catch (error) {
      console.error('Error in status change:', error);
      toast({
        title: "Status Update Failed",
        description: "An error occurred while updating the status",
        variant: "destructive",
      });
    }
  };

  const handleFormSubmit = (e: React.FormEvent) => {
    handleSubmit(e);
    // Close dialog after form submission
    if (!isSubmitting) {
      onOpenChange(false);
    }
  };

  if (!order) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <div className="flex items-center justify-between">
          <DialogHeader className="flex-1">
            <DialogTitle>Edit Order: {order.id}</DialogTitle>
          </DialogHeader>
          
          <Button 
            variant="outline" 
            size="icon" 
            className="rounded-full"
            onClick={() => onOpenChange(false)}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
        
        {!canEdit ? (
          <div className="text-amber-600 bg-amber-50 p-4 rounded-md">
            You don't have permission to edit orders. Only admin and staff can modify orders.
          </div>
        ) : (
          <form onSubmit={handleFormSubmit}>
            <OrderDetailsForm
              formData={formData}
              handleInputChange={handleInputChange}
              handleNumberChange={handleNumberChange}
              handleCustomerTypeChange={handleCustomerTypeChange}
              handleDryCleaningChange={handleDryCleaningChange}
            />
            
            <div className="pt-4 border-t">
              <OrderStatusWorkflow 
                order={order} 
                onStatusChange={handleStatusChange}
              />
            </div>
            
            <DialogFooter className="mt-4">
              <FormActions 
                isSubmitting={isSubmitting} 
                onCancel={() => onOpenChange(false)} 
              />
            </DialogFooter>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
}
