
import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Order } from "@/types";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Loader2, Save, X } from "lucide-react";
import { OrderStatusWorkflow } from "./workflow/OrderStatusWorkflow";
import { useAuth } from "@/contexts/auth";
import { Textarea } from "../ui/textarea";

interface EditOrderDialogProps {
  order: Order | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onOrderUpdated?: () => void;
}

export function EditOrderDialog({
  order,
  open,
  onOpenChange,
  onOrderUpdated,
}: EditOrderDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const { userRole } = useAuth();
  
  // Only allow admin or staff to edit orders
  const canEdit = userRole === 'admin' || userRole === 'staff';
  
  // Form state
  const [formData, setFormData] = useState<{
    customerName: string;
    customerPhone: string;
    notes: string;
    weightKilos: number;
    numberOfPieces: number;
    customerType: "client" | "walk-in";
  }>({
    customerName: "",
    customerPhone: "",
    notes: "",
    weightKilos: 0,
    numberOfPieces: 1,
    customerType: "walk-in",
  });

  // Populate form when order changes
  useEffect(() => {
    if (order) {
      setFormData({
        customerName: order.customer?.name || "",
        customerPhone: order.customer?.phone || "",
        notes: order.notes || "",
        weightKilos: order.weightKilos || 0,
        numberOfPieces: order.numberOfPieces || 1,
        customerType: order.customerType || "walk-in",
      });
    }
  }, [order]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleNumberChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: parseFloat(value) || 0 }));
  };

  const handleCustomerTypeChange = (value: "client" | "walk-in") => {
    setFormData((prev) => ({ ...prev, customerType: value }));
  };

  const handleStatusChange = async (orderId: string, newStatus: string) => {
    try {
      if (!order) return;
      
      const { error } = await supabase
        .from('orders')
        .update({ status: newStatus })
        .eq('id', order.uuid);
        
      if (error) {
        console.error('Error updating order status:', error);
        toast({
          title: "Status Update Failed",
          description: error.message,
          variant: "destructive",
        });
        return;
      }
      
      toast({
        title: "Status Updated",
        description: `Order status changed to ${newStatus}`,
      });
      
      if (onOrderUpdated) onOrderUpdated();
    } catch (error) {
      console.error('Error in status change:', error);
      toast({
        title: "Status Update Failed",
        description: "An error occurred while updating the status",
        variant: "destructive",
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!order || !canEdit) return;
    
    setIsSubmitting(true);
    
    try {
      // Prepare update data
      const updateData = {
        customer_name: formData.customerName,
        phone_number: formData.customerPhone,
        notes: formData.notes,
        weight_kilos: formData.weightKilos,
        number_of_pieces: formData.numberOfPieces,
        customer_type: formData.customerType,
        updated_at: new Date().toISOString(),
      };
      
      // Update in database
      const { error } = await supabase
        .from('orders')
        .update(updateData)
        .eq('id', order.uuid);
        
      if (error) {
        throw error;
      }
      
      toast({
        title: "Order Updated",
        description: `Successfully updated order ${order.id}`,
      });
      
      // Trigger refresh
      if (onOrderUpdated) onOrderUpdated();
      
      // Close dialog
      onOpenChange(false);
      
    } catch (error: any) {
      console.error('Error updating order:', error);
      toast({
        title: "Update Failed",
        description: error.message || "Failed to update the order",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!order) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Edit Order: {order.id}</DialogTitle>
        </DialogHeader>
        
        {!canEdit ? (
          <div className="text-amber-600 bg-amber-50 p-4 rounded-md">
            You don't have permission to edit orders. Only admin and staff can modify orders.
          </div>
        ) : (
          <form onSubmit={handleSubmit}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="customerName">Customer Name</Label>
                  <Input
                    id="customerName"
                    name="customerName"
                    value={formData.customerName}
                    onChange={handleInputChange}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="customerPhone">Phone Number</Label>
                  <Input
                    id="customerPhone"
                    name="customerPhone"
                    value={formData.customerPhone}
                    onChange={handleInputChange}
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="customerType">Customer Type</Label>
                <Select
                  value={formData.customerType}
                  onValueChange={handleCustomerTypeChange as any}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select customer type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="client">Client</SelectItem>
                    <SelectItem value="walk-in">Walk-in</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="weightKilos">Weight (kg)</Label>
                  <Input
                    id="weightKilos"
                    name="weightKilos"
                    type="number"
                    step="0.1"
                    value={formData.weightKilos}
                    onChange={handleNumberChange}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="numberOfPieces">Number of Pieces</Label>
                  <Input
                    id="numberOfPieces"
                    name="numberOfPieces"
                    type="number"
                    value={formData.numberOfPieces}
                    onChange={handleNumberChange}
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="notes">Notes</Label>
                <Textarea
                  id="notes"
                  name="notes"
                  value={formData.notes}
                  onChange={handleInputChange}
                  rows={3}
                />
              </div>
              
              <div className="pt-4 border-t">
                <OrderStatusWorkflow 
                  order={order} 
                  onStatusChange={handleStatusChange}
                />
              </div>
            </div>
            
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                <X className="w-4 h-4 mr-1" /> Cancel
              </Button>
              <Button 
                type="submit"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-1 animate-spin" /> Saving...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-1" /> Save Changes
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
}
