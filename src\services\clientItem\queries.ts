
import { supabase } from "@/integrations/supabase/client";
import { ClientItem } from "./types";
import { getClientItemsStore } from "./localStorage";

// Function to get client items
export const getClientItems = async (clientId: string): Promise<ClientItem[]> => {
  try {
    // Check authentication status
    const { data: sessionData } = await supabase.auth.getSession();
    const isAuthenticated = !!sessionData.session;
    
    // Get local items for this client
    const localItems = getClientItemsStore().filter(
      item => item.client_id === clientId
    );
    
    if (!isAuthenticated) {
      console.log(`Not authenticated, using local store for client ${clientId} items`);
      return localItems;
    }
    
    // If client ID is local, only return local items
    if (clientId.startsWith('local-')) {
      return localItems;
    }
    
    // Try to fetch from Supabase
    const { data, error } = await supabase
      .from('client_items')
      .select('id, client_id, name, created_at, updated_at, unit_price, item_type')
      .eq('client_id', clientId)
      .order('name');
    
    if (error) {
      console.error("Error fetching client items:", error);
      return localItems;
    }
    
    // Ensure all items have item_type property
    const itemsWithType = data ? data.map(item => ({
      ...item,
      item_type: item.item_type || 'default'
    })) : [];
    
    // Combine Supabase data with any local items for this client
    const remoteItemIds = itemsWithType.map(item => item.id);
    const localOnlyItems = localItems.filter(
      item => item.id.startsWith('local-') || !remoteItemIds.includes(item.id)
    );
    
    return [...itemsWithType, ...localOnlyItems];
  } catch (err) {
    console.error("Error in getClientItems:", err);
    return getClientItemsStore().filter(item => item.client_id === clientId);
  }
};
