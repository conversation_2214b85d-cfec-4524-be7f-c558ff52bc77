
import React from "react";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";

interface StatusSelectProps {
  value: string;
  onChange: (value: string) => void;
}

export const StatusSelect: React.FC<StatusSelectProps> = ({ value, onChange }) => {
  // Get status display name for UI
  const getStatusDisplayName = (status: string) => {
    switch (status) {
      case 'all': return 'All Statuses';
      case 'for_pickup': return 'For Pickup'; // Client orders first status
      case 'pickup_complete': return 'Pickup Complete';
      case 'processing': return 'Order Processing';
      case 'for_treatment': return 'For Treatment';
      case 'hard_stain': return 'With Hard Stain';
      case 'partial_delivery': return 'Partial Delivery';
      case 'fulfilled': return 'Delivery Complete';
      case 'cancelled': return 'Cancelled';
      case 'pending': return 'Pending';
      default: return status.charAt(0).toUpperCase() + status.slice(1).replace(/_/g, ' ');
    }
  };

  return (
    <div className="space-y-2">
      <label className="text-sm">Status</label>
      <Select
        value={value}
        onValueChange={onChange}
      >
        <SelectTrigger>
          <SelectValue placeholder="Filter by status">
            {getStatusDisplayName(value)}
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">{getStatusDisplayName('all')}</SelectItem>
          <SelectItem value="pending">{getStatusDisplayName('pending')}</SelectItem>
          <SelectItem value="for_pickup">{getStatusDisplayName('for_pickup')}</SelectItem>
          <SelectItem value="pickup_complete">{getStatusDisplayName('pickup_complete')}</SelectItem>
          <SelectItem value="processing">{getStatusDisplayName('processing')}</SelectItem>
          <SelectItem value="for_treatment">{getStatusDisplayName('for_treatment')}</SelectItem>
          <SelectItem value="hard_stain">{getStatusDisplayName('hard_stain')}</SelectItem>
          <SelectItem value="partial_delivery">{getStatusDisplayName('partial_delivery')}</SelectItem>
          <SelectItem value="fulfilled">{getStatusDisplayName('fulfilled')}</SelectItem>
          <SelectItem value="cancelled">{getStatusDisplayName('cancelled')}</SelectItem>
        </SelectContent>
      </Select>
    </div>
  );
};
