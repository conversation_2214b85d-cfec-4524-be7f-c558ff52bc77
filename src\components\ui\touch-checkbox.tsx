
import * as React from "react"
import { Check } from "lucide-react"
import { cn } from "@/lib/utils"

interface TouchCheckboxProps extends React.InputHTMLAttributes<HTMLInputElement> {
  checked?: boolean;
  onCheckedChange?: (checked: boolean) => void;
  className?: string;
}

const TouchCheckbox = React.forwardRef<HTMLInputElement, TouchCheckboxProps>(
  ({ className, checked, onCheckedChange, ...props }, ref) => {
    return (
      <div
        className={cn(
          "flex h-6 w-6 items-center justify-center rounded-md border border-primary bg-background",
          checked && "bg-primary text-primary-foreground",
          "cursor-pointer touch-action-manipulation",
          className
        )}
        onClick={() => onCheckedChange?.(!checked)}
      >
        <input
          type="checkbox"
          className="sr-only"
          checked={checked}
          onChange={(e) => onCheckedChange?.(e.target.checked)}
          ref={ref}
          {...props}
        />
        {checked && <Check className="h-4 w-4 text-current" />}
      </div>
    )
  }
)

TouchCheckbox.displayName = "TouchCheckbox"

export { TouchCheckbox }
