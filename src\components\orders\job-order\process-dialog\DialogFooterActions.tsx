
import { DialogFooter } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Loader2 } from "lucide-react";

interface DialogFooterActionsProps {
  isUpdating: boolean;
  onClose: () => void;
}

export function DialogFooterActions({ isUpdating, onClose }: DialogFooterActionsProps) {
  return (
    <DialogFooter className="flex justify-between">
      <Button
        onClick={onClose}
        disabled={isUpdating}
        variant="outline"
      >
        Cancel
      </Button>
      <Button
        onClick={onClose}
        disabled={isUpdating}
        variant="default"
      >
        {isUpdating ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Processing...
          </>
        ) : (
          "Done"
        )}
      </Button>
    </DialogFooter>
  );
}
