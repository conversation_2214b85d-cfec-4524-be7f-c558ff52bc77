
import { supabase } from '@/integrations/supabase/client';
import { User } from '@supabase/supabase-js';

/**
 * Ensures that a user is associated with a client
 * This is particularly useful for special case emails that should always
 * be associated with specific clients
 */
export const ensureClientAssociation = async (user: User): Promise<string | null> => {
  try {
    if (!user || !user.email) return null;
    
    console.log("Ensuring client association for user:", user.email);
    
    // First, check if association already exists
    const { data: existingAssoc, error: assocError } = await supabase
      .from('users_clients')
      .select('client_id')
      .eq('user_id', user.id)
      .maybeSingle();
    
    if (!assocError && existingAssoc?.client_id) {
      console.log("User already associated with client:", existingAssoc.client_id);
      return existingAssoc.client_id;
    }
    
    // Handle special case emails
    const specialEmails = ['<EMAIL>', '<EMAIL>'];
    if (specialEmails.includes(user.email)) {
      // Find the client by email
      const { data: client, error: clientError } = await supabase
        .from('clients')
        .select('id')
        .ilike('email', user.email)
        .maybeSingle();
        
      if (clientError) {
        console.error("Error finding client by email:", clientError);
        return null;
      }
      
      if (!client?.id) {
        console.log("No client found with email:", user.email);
        
        // Try a case-insensitive search as a fallback
        const { data: clientsByEmail } = await supabase
          .from('clients')
          .select('id')
          .ilike('email', `%${user.email.split('@')[1]}%`)
          .limit(1);
          
        if (!clientsByEmail || clientsByEmail.length === 0) {
          console.log("No client found by domain match either");
          return null;
        }
        
        // Use the first client with matching domain
        client.id = clientsByEmail[0].id;
        console.log("Found client by domain match:", client.id);
      }
      
      // Create the association using the edge function instead of direct insertion
      const { error: edgeFunctionError } = await supabase.functions.invoke('associate-client-user', {
        body: {
          userId: user.id,
          clientId: client.id
        }
      });
      
      if (edgeFunctionError) {
        console.error("Error invoking associate-client-user function:", edgeFunctionError);
        return null;
      }
      
      console.log("Created user-client association between", user.id, "and", client.id);
      return client.id;
    }
    
    return null;
  } catch (error) {
    console.error("Error in ensureClientAssociation:", error);
    return null;
  }
};
