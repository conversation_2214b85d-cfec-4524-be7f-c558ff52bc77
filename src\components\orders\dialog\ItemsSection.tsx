
import { Order } from "@/types";
import { OrderLineItems } from "./OrderLineItems";
import { useOrder } from "@/contexts/OrderContext";

interface ItemsSectionProps {
  order: Order;
  isStaff: boolean;
  onOrderUpdated: () => void;
}

export function ItemsSection({
  order,
  isStaff,
  onOrderUpdated
}: ItemsSectionProps) {
  const {
    order: contextOrder,
    refreshOrder
  } = useOrder();

  // Use the order from context if available, otherwise fall back to props
  const currentOrder = contextOrder || order;

  // Function to handle both context refresh and parent notification
  const handleOrderUpdated = async () => {
    await refreshOrder();
    onOrderUpdated();
  };

  return (
    <div className="space-y-2">
      <OrderLineItems order={currentOrder} onOrderUpdated={handleOrderUpdated} />
    </div>
  );
}
