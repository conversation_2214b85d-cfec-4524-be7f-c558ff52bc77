
import React from "react";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { UseFormReturn } from "react-hook-form";
import { OrderFormValues } from "../OrderFormTypes";
import { Weight } from "lucide-react";

interface WeightInputProps {
  form: UseFormReturn<OrderFormValues>;
}

export function WeightInput({ form }: WeightInputProps) {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    form.setValue("weightKilos", value);
    
    // Update weight field for backward compatibility
    const numValue = parseFloat(value);
    if (!isNaN(numValue)) {
      // Ensure weight is set as a number
      form.setValue("weight", numValue);
    }
  };

  return (
    <FormField
      control={form.control}
      name="weightKilos"
      render={({ field }) => (
        <FormItem>
          <FormLabel className="text-base flex items-center">
            <Weight className="mr-2 h-4 w-4" />
            Weight (kg)
          </FormLabel>
          <FormControl>
            <Input 
              type="number" 
              inputMode="decimal" 
              step="0.1" 
              min="0" 
              placeholder="0.0" 
              className="h-12 text-center text-base"
              {...field}
              value={field.value || ""}
              onChange={handleChange}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
