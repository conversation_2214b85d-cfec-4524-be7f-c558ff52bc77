import { Routes, Route } from "react-router-dom";
import Dashboard from "@/pages/Dashboard";
import Orders from "@/pages/Orders";
import OrderStatus from "@/pages/OrderStatus";
import Clients from "@/pages/Clients";
import Customers from "@/pages/Customers";
import Inventory from "@/pages/Inventory";
import Index from "@/pages/Index";
import NotFound from "@/pages/NotFound";
import Login from "@/pages/Login";
import Reports from "@/pages/Reports";
import Settings from "@/pages/Settings";
import Profile from "@/pages/Profile";
import Accounting from "@/pages/Accounting";
import ClientTransactions from "@/pages/ClientTransactions";
import ClientItems from "@/pages/client/ClientItems";
import ClientOrders from "@/pages/client/ClientOrders";
import ClientReports from "@/pages/client/ClientReports";
import AuditLogs from "@/pages/AuditLogs";
import Services from "@/pages/Services";
import PricingSettings from "@/components/orders/pricing-settings";
import Expenses from "@/pages/Expenses";
import ClientUserSetup from "@/pages/ClientUserSetup";
import JobOrders from "@/pages/JobOrders";

import { AuthGuard } from "./guards/AuthGuard";
import { RoleGuard } from "./guards/RoleGuard";
import { useAuth } from "@/contexts/auth";
import { ClientPortalLayout } from "@/components/layout/ClientPortalLayout";
import { MainLayout } from "@/components/layout/MainLayout";
import { Navigate } from "react-router-dom";

export function AppRoutes() {
  const { isClientUser } = useAuth();
  
  return (
    <Routes>
      <Route path="/" element={<Index />} />
      <Route path="/login" element={<Login />} />

      {/* Protected routes - require authentication */}
      <Route element={<AuthGuard />}>
        {/* Client portal routes (for client users) - RESTRICTED */}
        {isClientUser && (
          <Route element={<ClientPortalLayout />}>
            {/* Only allow these specific routes for client users */}
            <Route path="/orders" element={<ClientOrders />} />
            <Route path="/items" element={<ClientItems />} />
            <Route path="/reports" element={<ClientReports />} />
            <Route path="/profile" element={<Profile />} />
            
            {/* Redirect any other client portal routes to /orders */}
            <Route path="/dashboard" element={<Navigate to="/orders" replace />} />
            <Route path="/settings" element={<Navigate to="/orders" replace />} />
            <Route path="/transactions" element={<Navigate to="/orders" replace />} />
          </Route>
        )}

        {/* Staff/Admin routes */}
        {!isClientUser && (
          <Route element={<MainLayout />}>
            <Route path="/dashboard" element={<Dashboard />} />
            <Route path="/orders" element={<Orders />} />
            <Route path="/orders/status" element={<OrderStatus />} />
            {/* Add the new Job Orders route - only for admin and staff */}
            <Route path="/orders/jobs" element={<JobOrders />} />
            <Route path="/clients" element={<Clients />} />
            <Route path="/customers" element={<Customers />} />
            <Route path="/inventory" element={<Inventory />} />
            <Route path="/services" element={<Services />} />
            <Route path="/expenses" element={<Expenses />} />
            <Route path="/transactions/:clientId" element={<ClientTransactions />} />
            
            {/* Admin-only routes */}
            <Route element={<RoleGuard allowedRoles={['admin']} />}>
              <Route path="/accounting" element={<Accounting />} />
              <Route path="/reports" element={<Reports />} />
              <Route path="/audit-logs" element={<AuditLogs />} />
              <Route path="/pricing" element={<PricingSettings />} />
              <Route path="/settings" element={<Settings />} />
              <Route path="/client-user-setup" element={<ClientUserSetup />} />
            </Route>
            
            <Route path="/profile" element={<Profile />} />
          </Route>
        )}
      </Route>

      {/* 404 route */}
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
}
