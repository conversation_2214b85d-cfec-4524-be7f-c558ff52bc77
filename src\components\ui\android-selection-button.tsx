import React from 'react';
import { Check } from 'lucide-react';
import { cn } from '@/lib/utils';

interface AndroidSelectionButtonProps {
  selected: boolean;
  onToggle: () => void;
  label?: string;
  className?: string;
  disabled?: boolean;
}

export function AndroidSelectionButton({
  selected,
  onToggle,
  label,
  className,
  disabled = false
}: AndroidSelectionButtonProps) {
  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!disabled) {
      console.log('AndroidSelectionButton clicked:', { selected, label });
      onToggle();
    }
  };

  const handleTouchStart = (e: React.TouchEvent) => {
    e.preventDefault();
    if (!disabled) {
      console.log('AndroidSelectionButton touch start:', { selected, label });
    }
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    e.preventDefault();
    if (!disabled) {
      console.log('AndroidSelectionButton touch end:', { selected, label });
      onToggle();
    }
  };

  return (
    <button
      type="button"
      className={cn(
        // Base styles
        "relative flex items-center justify-center",
        "w-12 h-12 min-w-[48px] min-h-[48px]",
        "rounded-md border-2 transition-all duration-200",
        "focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2",
        "touch-action-manipulation",
        "active:scale-95",
        // Selected state
        selected
          ? "border-primary bg-primary text-primary-foreground"
          : "border-gray-300 bg-white text-gray-600 hover:border-gray-400 hover:bg-gray-50",
        // Disabled state
        disabled && "opacity-50 cursor-not-allowed",
        className
      )}
      onClick={handleClick}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
      disabled={disabled}
      aria-label={label}
      aria-pressed={selected}
      role="checkbox"
      aria-checked={selected}
    >
      {selected && <Check className="h-6 w-6" />}
    </button>
  );
}
