
import { v4 as uuidv4 } from 'uuid';
import { mapSupabaseOrderToOrderType } from './orderMapper';

/**
 * Creates and stores an order in local storage for offline mode
 */
export function createLocalOrder(orderData: any): any {
  const localOrderData = {
    id: uuidv4(),
    ...orderData,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };
  
  // Map the local data to the Order type
  const createdOrder = mapSupabaseOrderToOrderType(localOrderData);
  
  // Save to local storage
  const localOrders = JSON.parse(localStorage.getItem('orders') || '[]');
  localOrders.push(localOrderData);
  localStorage.setItem('orders', JSON.stringify(localOrders));
  
  return createdOrder;
}
