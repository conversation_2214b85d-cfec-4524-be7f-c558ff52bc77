
import React from 'react';
import { ItemSelectorContainer } from './order-items/ItemSelectorContainer';
import { ClientOrderItem } from './order-form/types';

interface ClientOrderItemSelectorProps {
  items: { id: string; name: string; item_type: string; unit_price: number; }[];
  onItemsChange: (items: ClientOrderItem[]) => void;
  initialItems?: ClientOrderItem[];
}

export function ClientOrderItemSelector({ 
  items, 
  onItemsChange,
  initialItems = [] 
}: ClientOrderItemSelectorProps) {
  // Log the items with their prices for debugging
  console.log('ClientOrderItemSelector items with database prices:', items);
  
  return (
    <ItemSelectorContainer 
      items={items} 
      onItemsChange={onItemsChange}
      initialItems={initialItems}
    />
  );
}
