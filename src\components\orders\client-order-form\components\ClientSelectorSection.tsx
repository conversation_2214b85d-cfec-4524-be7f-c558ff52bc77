
import React from "react";
import { UseFormReturn } from "react-hook-form";
import { OrderFormValues } from "../../OrderFormTypes";
import { ClientSelector } from "../../client-selector";
import { FormSectionWrapper } from "./FormSectionWrapper";

interface ClientSelectorSectionProps {
  form: UseFormReturn<OrderFormValues>;
}

export function ClientSelectorSection({ form }: ClientSelectorSectionProps) {
  return (
    <FormSectionWrapper>
      <div className="mb-2">
        <h3 className="text-lg font-medium">Create Client Order</h3>
        <p className="text-sm text-muted-foreground">Create a new order for an existing client</p>
      </div>
      <ClientSelector form={form} initialOrderType="client" />
    </FormSectionWrapper>
  );
}
