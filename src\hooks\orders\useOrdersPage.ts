
import { useState, useEffect } from "react";
import { useToast } from "@/hooks/use-toast";
import { useOrdersData } from "@/hooks/orders/useOrdersData";
import { useOrderFilters } from "@/hooks/orders/useOrderFilters";
import { Order } from "@/types";
import { supabase } from "@/integrations/supabase/client";

export function useOrdersPage() {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isAddOrderOpen, setIsAddOrderOpen] = useState(false);
  const [activeTab, setActiveTab] = useState("clients");
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [isViewOrderOpen, setIsViewOrderOpen] = useState(false);
  const { toast } = useToast();
  
  // Use the orders data hook
  const {
    orders,
    isLoading,
    error,
    refreshOrders
  } = useOrdersData();
  
  // Use the order filters hook
  const {
    searchTerm,
    setSearchTerm,
    statusFilter,
    setStatusFilter,
    dateFilter,
    setDateFilter,
    clientFilter,
    setClientFilter,
    filteredOrders,
    getClientOptions
  } = useOrderFilters(orders);

  useEffect(() => {
    // Log orders data when it changes
    console.log("Orders page - orders data updated:", orders.length, "orders");
    if (orders.length > 0) {
      const clientOrders = orders.filter(o => o.customerType === "client");
      const walkInOrders = orders.filter(o => o.customerType === "walk-in");
      console.log("Order types breakdown:", {
        clientOrders: clientOrders.length,
        walkInOrders: walkInOrders.length
      });
    }
    
    // Listen for status update and deletion events to refresh the UI
    const handleStatusUpdated = () => {
      console.log("Status update detected, refreshing orders");
      refreshOrders();
    };
    
    const handleOrderDeleted = () => {
      console.log("Order deletion detected, refreshing orders");
      refreshOrders();
    };
    
    window.addEventListener('order-status-updated', handleStatusUpdated);
    window.addEventListener('order-deleted', handleOrderDeleted);
    
    return () => {
      window.removeEventListener('order-status-updated', handleStatusUpdated);
      window.removeEventListener('order-deleted', handleOrderDeleted);
    };
  }, [orders, refreshOrders]);

  // Handle refreshing orders
  const handleManualRefresh = async () => {
    console.log("Manual refresh triggered");
    setIsRefreshing(true);
    try {
      await refreshOrders();
      toast({
        title: "Orders Refreshed",
        description: "Latest orders have been loaded"
      });
    } catch (error) {
      console.error("Error refreshing orders:", error);
      toast({
        title: "Refresh Failed",
        description: "Could not refresh order data",
        variant: "destructive"
      });
    } finally {
      setTimeout(() => setIsRefreshing(false), 500);
    }
  };
  
  const handleViewOrder = (order: Order) => {
    console.log("Viewing order:", order);
    setSelectedOrder(order);
    setIsViewOrderOpen(true);
  };
  
  const handleStatusChange = async (orderId: string, newStatus: string) => {
    console.log("Changing status of order", orderId, "to", newStatus);
    
    try {
      const { error } = await supabase
        .from('orders')
        .update({ status: newStatus })
        .eq('reference_code', orderId);
        
      if (error) {
        console.error('Error updating order in database:', error);
        toast({
          title: "Update Failed",
          description: "Could not update order status",
          variant: "destructive",
        });
        return;
      }
      
      // Update the local selected order if it matches
      if (selectedOrder?.id === orderId) {
        setSelectedOrder({
          ...selectedOrder,
          status: newStatus
        });
      }
      
      toast({
        title: "Order Updated",
        description: `Order ${orderId} status updated to ${newStatus}`,
      });
      
      // Refresh orders to update the UI
      await refreshOrders();
    } catch (error) {
      console.error('Error in status update:', error);
      toast({
        title: "Update Failed",
        description: "Could not update order status",
        variant: "destructive",
      });
    }
  };

  // Filter orders based on the active tab - using proper customer type filtering
  const clientOrders = filteredOrders.filter(order => order.customerType === "client");
  const walkInOrders = filteredOrders.filter(order => order.customerType === "walk-in");
  
  return {
    isRefreshing,
    isLoading,
    error,
    isAddOrderOpen,
    setIsAddOrderOpen,
    activeTab,
    setActiveTab,
    selectedOrder,
    isViewOrderOpen,
    setIsViewOrderOpen,
    orders,
    refreshOrders,
    handleManualRefresh,
    handleViewOrder,
    handleStatusChange,
    searchTerm,
    setSearchTerm,
    statusFilter,
    setStatusFilter,
    dateFilter,
    setDateFilter,
    clientFilter,
    setClientFilter,
    clientOrders,
    walkInOrders,
    getClientOptions
  };
}
