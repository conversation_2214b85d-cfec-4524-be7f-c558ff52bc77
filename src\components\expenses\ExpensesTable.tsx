
import { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { format } from "date-fns";
import { ExpenseItem } from "@/types/expense";
import { ExpenseCategoryBadge } from "./ExpenseCategoryBadge";
import { ExpenseViewDialog } from "./ExpenseViewDialog";
import { useAuth } from "@/contexts/auth";
import { Eye } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile"; 

interface ExpensesTableProps {
  expenses: ExpenseItem[];
  isLoading?: boolean;
}

export function ExpensesTable({ expenses, isLoading = false }: ExpensesTableProps) {
  const [viewExpense, setViewExpense] = useState<ExpenseItem | null>(null);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const { userRole } = useAuth();
  const isMobile = useIsMobile();

  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "MMM dd, yyyy");
    } catch (e) {
      return dateString;
    }
  };

  const handleViewExpense = (expense: ExpenseItem) => {
    setViewExpense(expense);
    setViewDialogOpen(true);
  };

  if (isLoading) {
    return <div className="flex justify-center p-8">Loading expenses...</div>;
  }

  if (expenses.length === 0) {
    return (
      <div className="text-center p-8">
        <p className="text-muted-foreground">No expenses found</p>
      </div>
    );
  }

  // For mobile, show a card-based layout
  if (isMobile) {
    return (
      <>
        <div className="space-y-4 px-4">
          {expenses.map((expense) => (
            <div 
              key={expense.id} 
              className="border rounded-md p-4 bg-white shadow-sm"
            >
              <div className="flex justify-between items-start">
                <div>
                  <p className="font-medium">{expense.description}</p>
                  <p className="text-sm text-muted-foreground">{formatDate(expense.date)}</p>
                </div>
                <div className="text-right">
                  <p className="font-semibold">{formatAmount(expense.amount)}</p>
                  <div className="mt-1">
                    <ExpenseCategoryBadge category={expense.category} />
                  </div>
                </div>
              </div>
              <div className="mt-4 flex justify-end">
                <Button 
                  variant="ghost" 
                  size="sm"
                  onClick={() => handleViewExpense(expense)}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  View Details
                </Button>
              </div>
            </div>
          ))}
        </div>

        <ExpenseViewDialog 
          open={viewDialogOpen} 
          onOpenChange={setViewDialogOpen} 
          expense={viewExpense} 
        />
      </>
    );
  }

  // For tablet and desktop, show a table layout
  return (
    <>
      <div className="rounded-md border overflow-hidden">
        <div className="table-responsive overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Date</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Category</TableHead>
                <TableHead className="text-right">Amount</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {expenses.map((expense) => (
                <TableRow key={expense.id} className="touch-target">
                  <TableCell>{formatDate(expense.date)}</TableCell>
                  <TableCell className="font-medium">{expense.description}</TableCell>
                  <TableCell>
                    <ExpenseCategoryBadge category={expense.category} />
                  </TableCell>
                  <TableCell className="text-right">{formatAmount(expense.amount)}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end">
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        onClick={() => handleViewExpense(expense)}
                        title="View details"
                        className="h-10 w-10" // Increased touch target
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>

      <ExpenseViewDialog 
        open={viewDialogOpen} 
        onOpenChange={setViewDialogOpen} 
        expense={viewExpense} 
      />
    </>
  );
}
