import { useState, useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Form } from "@/components/ui/form";
import { CustomerDetails } from "../CustomerDetails";
import { LaundryDetails } from "../laundry-details";
import { PriceCalculation } from "../PriceCalculation";
import { AddOns } from "../add-ons";
import { PaymentInput } from "../PaymentInput";
import { orderFormSchema } from "../OrderFormTypes";
import { FormActions } from "./FormActions";
import { FormContainer } from "./FormContainer";
import { OrderFormProps, PriceBreakdown } from "./OrderFormTypes";
import { useOrderFormSubmit } from "@/hooks/useOrderFormSubmit";
import { ClientItemWithQuantity, DryCleaningItem } from "../OrderFormTypes";
import { useIsMobile } from "@/hooks/use-mobile";
import { <PERSON><PERSON>, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { SERVICE_TYPES } from "../pricing/constants";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { DryCleaningIndicator } from "../job-order/add-ons/DryCleaningIndicator";

export function OrderForm({
  onSubmit,
  initialOrderType = "walk-in",
  isSubmitting = false
}: OrderFormProps) {
  const [deliveryDate, setDeliveryDate] = useState("");
  const [isDryCleaning, setIsDryCleaning] = useState(false);
  const [priceBreakdown, setPriceBreakdown] = useState<PriceBreakdown>({
    basePrice: 0,
    addOnPrice: 0,
    subtotal: 0,
    vatAmount: 0,
    totalPrice: 0
  });
  const isMobile = useIsMobile();

  // Set initial form values based on order type
  const defaultPricingMethod = initialOrderType === "client" ? "client_item" : "weight";
  const form = useForm<typeof orderFormSchema._type>({
    resolver: zodResolver(orderFormSchema),
    defaultValues: {
      customerName: "",
      phoneNumber: "",
      deliveryDate: "",
      weightKilos: "3.0",
      // Default to 3kg (minimum for regular service)
      numberOfPieces: "1",
      pricingMethod: defaultPricingMethod,
      detergentType: "none" as "none" | "regular" | "color",
      detergentQuantity: "1",
      conditionerType: "none" as "none" | "regular" | "fresh" | "floral",
      conditionerQuantity: "1",
      useStainRemover: false,
      useBleach: false,
      useDetergent: false,
      useFabricConditioner: false,
      isDryCleaning: false,
      paidAmount: "0",
      clientId: "",
      orderType: initialOrderType,
      serviceType: SERVICE_TYPES.WASH_DRY_FOLD,
      selectedClientItems: [] as ClientItemWithQuantity[],
      dryCleaningItems: [] as DryCleaningItem[]
    },
    mode: "onChange" // Enable validation on change for better user experience
  });

  // Initialize form with order type
  useEffect(() => {
    if (initialOrderType) {
      form.setValue("orderType", initialOrderType);
      if (initialOrderType === "client") {
        form.setValue("pricingMethod", "client_item");
        // Clear customer name and phone validation for client orders
        form.clearErrors("customerName");
        form.clearErrors("phoneNumber");
      }
    }
  }, [initialOrderType, form]);
  const {
    handleSubmit,
    isSubmitting: formSubmitting
  } = useOrderFormSubmit(form, priceBreakdown, onSubmit);

  // Combine external and internal submission states
  const submissionInProgress = isSubmitting || formSubmitting;
  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setDeliveryDate(e.target.value);
    form.setValue("deliveryDate", e.target.value);
  };

  // Handle dry cleaning toggle
  const handleDryCleaningToggle = (checked: boolean) => {
    setIsDryCleaning(checked);
    form.setValue("isDryCleaning", checked);
  };
  const orderType = form.watch("orderType");
  const pricingMethod = form.watch("pricingMethod") as "weight" | "client_item" | "dry_cleaning";
  const serviceType = form.watch("serviceType");
  const clientId = form.watch("clientId");
  const formErrors = form.formState.errors;
  const hasErrors = Object.keys(formErrors).length > 0;

  // Extract error messages
  const errorMessages = Object.entries(formErrors).map(([field, error]) => ({
    field,
    message: error?.message as string || `Please check ${field}`
  }));
  const detergentType = form.watch("detergentType") as "none" | "regular" | "color";
  const conditionerType = form.watch("conditionerType") as "none" | "regular" | "fresh" | "floral";
  const detergentQuantity = parseInt(form.watch("detergentQuantity") || "1");
  const conditionerQuantity = parseInt(form.watch("conditionerQuantity") || "1");
  const useStainRemover = form.watch("useStainRemover") || false;
  const useBleach = form.watch("useBleach") || false;
  const weightKilos = parseFloat(form.watch("weightKilos") || "0");
  const numberOfPieces = parseInt(form.watch("numberOfPieces") || "0");
  const clientItems = form.watch("selectedClientItems") as ClientItemWithQuantity[];
  const dryCleaningItems = form.watch("dryCleaningItems") as DryCleaningItem[];
  const sectionClasses = isMobile ? "bg-gray-50 p-3 rounded-lg shadow-inner mb-3" : "bg-gray-50 p-4 rounded-lg shadow-inner";
  return <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className={`${isMobile ? 'space-y-3' : 'space-y-6'}`}>
        {/* Form container for price calculation logic */}
        <FormContainer form={form} setPriceBreakdown={setPriceBreakdown} />
        
        {/* Display validation errors */}
        {hasErrors && errorMessages.length > 0 && <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              <ul className="list-disc pl-4">
                {errorMessages.map((err, idx) => <li key={idx}>{err.message}</li>)}
              </ul>
            </AlertDescription>
          </Alert>}
        
        {/* Only show customer details for walk-in orders */}
        {orderType === "walk-in" && <div className={sectionClasses}>
            <CustomerDetails form={form} />
          </div>}
        
        <div className={sectionClasses}>
          
          
          {isDryCleaning && <DryCleaningIndicator isDryCleaning={isDryCleaning} />}
          
          <LaundryDetails form={form} onChange={handleDateChange} />
        </div>
        
        <div className={sectionClasses}>
          <PriceCalculation priceBreakdown={priceBreakdown} detergentType={detergentType} detergentQuantity={detergentQuantity} conditionerType={conditionerType} conditionerQuantity={conditionerQuantity} useStainRemover={useStainRemover} useBleach={useBleach} pricingMethod={pricingMethod} serviceType={serviceType} weightKilos={weightKilos} numberOfPieces={numberOfPieces} clientItems={clientItems} dryCleaningItems={dryCleaningItems} />
        </div>

        <div className={sectionClasses}>
          <AddOns form={form} />
        </div>

        <div className={sectionClasses}>
          <PaymentInput form={form} priceBreakdown={priceBreakdown} />
        </div>

        <FormActions form={form} isSubmitting={submissionInProgress} />
      </form>
    </Form>;
}
