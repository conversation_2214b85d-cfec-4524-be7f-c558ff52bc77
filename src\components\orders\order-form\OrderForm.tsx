
import { useOrderFormState } from "./useOrderFormState";
import { OrderFormContent } from "./OrderFormContent";
import { FormContainer } from "./FormContainer";
import { OrderFormProps } from "../OrderFormTypes";
import { useIsMobile } from "@/hooks/use-mobile";

export function OrderForm({
  onSubmit,
  initialOrderType = "walk-in",
  isSubmitting = false
}: OrderFormProps) {
  const isMobile = useIsMobile();
  
  const {
    form,
    priceBreakdown,
    setPriceBreakdown,
    handleDateChange,
    handleSubmit,
    isSubmitting: submissionInProgress
  } = useOrderFormState(onSubmit, initialOrderType, isSubmitting);

  return (
    <>
      {/* Form container for price calculation logic */}
      <FormContainer form={form} setPriceBreakdown={setPriceBreakdown} />
      
      {/* Form UI */}
      <OrderFormContent 
        form={form}
        priceBreakdown={priceBreakdown}
        handleSubmit={handleSubmit}
        isSubmitting={submissionInProgress}
        isMobile={isMobile}
        handleDateChange={handleDateChange}
      />
    </>
  );
}
