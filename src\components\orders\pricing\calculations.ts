import { getPricingSettings } from "@/services/pricing/pricingService";
import { DryCleaningItem, ClientItemWithQuantity } from "../OrderFormTypes";
import { calculateItemTotalWithTreatments } from "@/utils/priceCalculations";

// Calculate base price based on weight and service type
export const calculateBasePrice = async (
  weightKilos: number,
  serviceType: string
): Promise<number> => {
  const settings = await getPricingSettings();
  let pricePerKilo = settings.BASE_PRICE_PER_KILO;
  
  // Apply minimum weight for various service types
  let effectiveWeight = weightKilos;
  
  if (serviceType === "wash_dry_fold" || serviceType === "wash_dry_press") {
    // Minimum 3kg for regular services
    effectiveWeight = Math.max(3, weightKilos);
  } else if (serviceType === "comforters") {
    // Minimum 3kg for comforters (if SPECIAL_SERVICES is configured)
    const minWeight = settings.SPECIAL_SERVICES?.minimumWeightComforters || 3;
    effectiveWeight = Math.max(minWeight, weightKilos);
    
    // Adjust price per kilo for comforters
    pricePerKilo = settings.SPECIAL_SERVICES?.comfortersPrice || 150;
  } else if (serviceType === "towels_curtains_linens") {
    // Minimum 2kg for special items
    const minWeight = settings.SPECIAL_SERVICES?.minimumWeightSpecial || 2;
    effectiveWeight = Math.max(minWeight, weightKilos);
    
    // Adjust price per kilo for special items
    pricePerKilo = settings.SPECIAL_SERVICES?.towelsCurtainsLinensPrice || 120;
  }
  
  return effectiveWeight * pricePerKilo;
};

// Calculate add-on prices
export const calculateAddOnPrice = async (addOns: {
  detergentType: "none" | "regular" | "color";
  detergentQuantity: number;
  conditionerType: "none" | "regular" | "fresh" | "floral";
  conditionerQuantity: number;
  useStainRemover: boolean;
  useBleach: boolean;
}): Promise<number> => {
  const settings = await getPricingSettings();
  let totalAddOnPrice = 0;
  
  // Add detergent price
  if (addOns.detergentType !== "none") {
    const detergentPrice = addOns.detergentType === "regular" 
      ? settings.ADDON_PRICES.detergent.regular 
      : settings.ADDON_PRICES.detergent.color;
    totalAddOnPrice += detergentPrice * addOns.detergentQuantity;
  }
  
  // Add conditioner price
  if (addOns.conditionerType !== "none") {
    let conditionerPrice = 0;
    switch (addOns.conditionerType) {
      case "regular":
        conditionerPrice = settings.ADDON_PRICES.fabricConditioner.regular;
        break;
      case "fresh":
        conditionerPrice = settings.ADDON_PRICES.fabricConditioner.fresh;
        break;
      case "floral":
        conditionerPrice = settings.ADDON_PRICES.fabricConditioner.floral;
        break;
    }
    totalAddOnPrice += conditionerPrice * addOns.conditionerQuantity;
  }
  
  // Add stain remover price
  if (addOns.useStainRemover) {
    totalAddOnPrice += settings.ADDON_PRICES.stainRemover;
  }
  
  // Add bleach price
  if (addOns.useBleach) {
    totalAddOnPrice += settings.ADDON_PRICES.bleach;
  }
  
  return totalAddOnPrice;
};

// Legacy function for backward compatibility
export const calculateAddOnPriceLegacy = async (
  useDetergent: boolean,
  useFabricConditioner: boolean
): Promise<number> => {
  const settings = await getPricingSettings();
  let totalAddOnPrice = 0;
  
  if (useDetergent) {
    totalAddOnPrice += settings.ADDON_PRICES.detergent.regular;
  }
  
  if (useFabricConditioner) {
    totalAddOnPrice += settings.ADDON_PRICES.fabricConditioner.regular;
  }
  
  return totalAddOnPrice;
};

// Calculate price for client items
export const calculateClientItemsPrice = (
  clientItems: ClientItemWithQuantity[]
): number => {
  if (!clientItems || clientItems.length === 0) {
    return 0;
  }
  
  return clientItems.reduce(
    (total, item) => total + (item.unitPrice * item.quantity),
    0
  );
};

// Calculate price for dry cleaning items
export const calculateDryCleaningItemsPrice = async (
  dryCleaningItems: DryCleaningItem[]
): Promise<number> => {
  if (!dryCleaningItems || dryCleaningItems.length === 0) {
    return 0;
  }
  
  // Ensure we're using up-to-date prices
  const settings = await getPricingSettings();
  const currentPrices = settings.DRY_CLEANING_PRICES || {};
  
  return dryCleaningItems.reduce((total, item) => {
    // Use the current price from settings if available, otherwise use the price stored with the item
    const currentPrice = currentPrices[item.type] || item.price;
    return total + (currentPrice * item.quantity);
  }, 0);
};

// Calculate VAT amount
export const calculateVAT = async (amount: number): Promise<number> => {
  const settings = await getPricingSettings();
  return amount * settings.VAT_RATE;
};

// Calculate total price (with VAT)
export const calculateTotalPrice = (subtotal: number, vatAmount: number): number => {
  return subtotal + vatAmount;
};
