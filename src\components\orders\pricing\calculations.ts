
import { SERVICE_TYPES } from "./constants";
import { AddOnSelections, ClientItemWithQuantity, DryCleaningItem } from "../OrderFormTypes";

// Base price calculation based on weight and service type
export function calculateBasePrice(weightKilos: number, serviceType: string): number {
  // Minimum weight for standard pricing (3kg)
  const minimumWeight = 3;
  const actualWeight = Math.max(weightKilos, minimumWeight);
  
  // Base rate per kg based on service type
  let ratePerKilo = 0;
  
  switch (serviceType) {
    case SERVICE_TYPES.WASH_DRY_FOLD:
      ratePerKilo = 65;
      break;
    case SERVICE_TYPES.WASH_DRY_PRESS:
      ratePerKilo = 80;
      break;
    case SERVICE_TYPES.WASH_DRY_FOLD_SPECIAL:
      ratePerKilo = 100;
      break;
    case SERVICE_TYPES.DRY_CLEANING:
      return 0; // Dry cleaning has its own pricing
    default:
      ratePerKilo = 65; // Default to wash dry fold
  }
  
  return actualWeight * ratePerKilo;
}

// Calculate price for add-ons
export function calculateAddOnPrice(selections: AddOnSelections): number {
  let total = 0;
  
  // Detergent pricing
  if (selections.detergentType !== "none") {
    const detergentBasePrice = selections.detergentType === "regular" ? 25 : 30;
    total += detergentBasePrice * selections.detergentQuantity;
  }
  
  // Conditioner pricing
  if (selections.conditionerType !== "none") {
    let conditionerPrice = 0;
    
    switch (selections.conditionerType) {
      case "regular":
        conditionerPrice = 25;
        break;
      case "fresh":
        conditionerPrice = 30;
        break;
      case "floral":
        conditionerPrice = 35;
        break;
    }
    
    total += conditionerPrice * selections.conditionerQuantity;
  }
  
  // Stain remover
  if (selections.useStainRemover) {
    total += 15;
  }
  
  // Bleach
  if (selections.useBleach) {
    total += 20;
  }
  
  return total;
}

// Calculate price for client items
export function calculateClientItemsPrice(items: ClientItemWithQuantity[]): number {
  if (!items || items.length === 0) {
    return 0;
  }
  
  return items.reduce((total, item) => {
    const itemPrice = item.unit_price || 0;
    const quantity = item.quantity || 1;
    return total + (itemPrice * quantity);
  }, 0);
}

// Calculate price for dry cleaning items
export function calculateDryCleaningItemsPrice(items: DryCleaningItem[]): number {
  if (!items || items.length === 0) {
    return 0;
  }
  
  return items.reduce((total, item) => {
    const itemPrice = item.price || 0;
    const quantity = item.quantity || 1;
    return total + (itemPrice * quantity);
  }, 0);
}

// Calculate VAT (12%)
export function calculateVAT(subtotal: number): number {
  const vatRate = 0.12; // 12% VAT
  return subtotal * vatRate;
}

// Calculate total price (subtotal + VAT)
export function calculateTotalPrice(subtotal: number, vatAmount: number): number {
  return subtotal + vatAmount;
}
