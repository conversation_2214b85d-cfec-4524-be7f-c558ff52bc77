
import React from "react";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";

interface SpecialTreatmentsSectionProps {
  useStainRemover: boolean;
  useBleach: boolean;
  isDryCleaning: boolean;
  onStainRemoverChange: (checked: boolean) => void;
  onBleachChange: (checked: boolean) => void;
}

export function SpecialTreatmentsSection({
  useStainRemover,
  useBleach,
  isDryCleaning,
  onStainRemoverChange,
  onBleachChange
}: SpecialTreatmentsSectionProps) {
  // Component no longer displays the Special Treatments section
  // This is because the process has been replaced with a different approach
  return null;
}
