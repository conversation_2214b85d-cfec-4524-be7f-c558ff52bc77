
import React from "react";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";

interface SpecialTreatmentsSectionProps {
  useStainRemover: boolean;
  useBleach: boolean;
  isDryCleaning: boolean;
  onStainRemoverChange: (checked: boolean) => void;
  onBleachChange: (checked: boolean) => void;
}

export function SpecialTreatmentsSection({
  useStainRemover,
  useBleach,
  isDryCleaning,
  onStainRemoverChange,
  onBleachChange
}: SpecialTreatmentsSectionProps) {
  return (
    <div className="border rounded-md p-4 bg-white">
      <h4 className="font-medium mb-3">Special Treatments</h4>
      
      <div className="space-y-3">
        <div className="flex items-center gap-2">
          <Checkbox 
            id="use-stain-remover" 
            checked={useStainRemover}
            onCheckedChange={(checked) => onStainRemoverChange(!!checked)}
          />
          <Label htmlFor="use-stain-remover">Stain Remover</Label>
        </div>
        
        <div className="flex items-center gap-2">
          <Checkbox 
            id="use-bleach" 
            checked={useBleach}
            onCheckedChange={(checked) => onBleachChange(!!checked)}
          />
          <Label htmlFor="use-bleach">Bleach Treatment</Label>
        </div>
      </div>
    </div>
  );
}
