
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useAuth } from "@/contexts/auth";
import { SidebarNav } from "./SidebarNav";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";

export function Sidebar({
  onCloseMobile
}: {
  onCloseMobile?: () => void;
}) {
  const [isCollapsed, setIsCollapsed] = useState<boolean>(false);
  const [userName, setUserName] = useState<string>("User");
  const [userEmail, setUserEmail] = useState<string>("");
  const [userInitial, setUserInitial] = useState<string>("U");
  const {
    userRole,
    isClientUser,
    signOut
  } = useAuth();

  useEffect(() => {
    const savedState = localStorage.getItem("sidebar-collapsed");
    if (savedState !== null) {
      setIsCollapsed(savedState === "true");
    }
  }, []);

  useEffect(() => {
    localStorage.setItem("sidebar-collapsed", isCollapsed.toString());
  }, [isCollapsed]);

  useEffect(() => {
    const getUserData = async () => {
      const {
        data: {
          user
        }
      } = await supabase.auth.getUser();
      if (user) {
        setUserEmail(user.email || "");
        const initial = user.email ? user.email[0].toUpperCase() : "U";
        setUserInitial(initial);
        setUserName(user.email?.split('@')[0] || "User");
      }
    };
    getUserData();
    const {
      data: {
        subscription
      }
    } = supabase.auth.onAuthStateChange((event, session) => {
      if (event === 'SIGNED_OUT') {
        setUserName("User");
        setUserEmail("");
        setUserInitial("U");
      } else if (event === 'SIGNED_IN' && session?.user) {
        setUserEmail(session.user.email || "");
        const initial = session.user.email ? session.user.email[0].toUpperCase() : "U";
        setUserInitial(initial);
        setUserName(session.user.email?.split('@')[0] || "User");
      }
    });
    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      toast.success("Logged out successfully");
    } catch (error) {
      console.error("Error signing out:", error);
      toast.error("Failed to log out");
    }
  };

  if (isClientUser) {
    return null;
  }

  return (
    <div className={`flex flex-col h-full bg-slate-800 text-white transition-all duration-300 ${isCollapsed ? 'w-16' : 'w-64'}`}>
      <div className={`p-4 border-b border-slate-700 flex ${isCollapsed ? 'justify-center' : 'justify-between'} items-center`}>
        {!isCollapsed && <div className="flex items-center">
            <img src="/lovable-uploads/cbd7662e-3eeb-4ed5-b4ab-77dbbd58204b.jpg" alt="CMC Laundry Services" className="h-20 w-auto" />
          </div>}
        <Button variant="ghost" size="icon" onClick={toggleCollapse} className="text-white hover:bg-slate-700">
          {isCollapsed ? <ChevronRight size={20} /> : <ChevronLeft size={20} />}
        </Button>
      </div>
      
      <SidebarNav isCollapsed={isCollapsed} onCloseMobile={onCloseMobile} userRole={userRole} />
      
      {/* User Profile section at the bottom */}
      <div className="mt-auto border-t border-slate-700">
        <div className={`p-3 ${isCollapsed ? 'flex justify-center' : ''}`}>
          <div className="flex items-center gap-2">
            <Avatar className="h-8 w-8">
              <AvatarImage src="" alt={userName} />
              <AvatarFallback>{userInitial}</AvatarFallback>
            </Avatar>
            {!isCollapsed && <div className="flex flex-col">
                <span className="text-sm font-medium">{userName}</span>
                <span className="text-xs text-slate-400">{userEmail}</span>
              </div>}
          </div>
        </div>
      </div>
      
      {!isCollapsed && <div className="p-3 border-t border-slate-700">
          <p className="text-xs text-slate-400">© 2025 Powered by PhilVirtualOffice</p>
        </div>}
    </div>
  );
}
