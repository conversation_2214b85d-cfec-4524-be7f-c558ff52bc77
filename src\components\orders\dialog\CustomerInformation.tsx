
import { Order } from "@/types";

interface CustomerInformationProps {
  order: Order;
}

export function CustomerInformation({ order }: CustomerInformationProps) {
  return (
    <div className="space-y-2">
      <h4 className="text-sm font-medium">Customer Information</h4>
      <div className="text-sm">
        <div>
          <strong>Name:</strong> {order.customer?.name || "N/A"}
        </div>
        {order.customer?.contactPerson && (
          <div>
            <strong>Contact Person:</strong> {order.customer.contactPerson}
          </div>
        )}
        <div>
          <strong>Phone:</strong> {order.customer?.phone || "N/A"}
        </div>
        <div>
          <strong>Type:</strong> {order.customerType === "walk-in" ? "Walk-in Customer" : "Client"}
        </div>
        {/* Only try to access email if it exists in the customer object */}
        {(order.customer as any)?.email && (
          <div>
            <strong>Email:</strong> {(order.customer as any).email}
          </div>
        )}
      </div>
    </div>
  );
}
