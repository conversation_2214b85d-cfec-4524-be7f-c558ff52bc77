
import { AndroidSwitch } from "@/components/ui/android-switch";
import { Label } from "@/components/ui/label";
import { FlaskConical } from "lucide-react";

interface StainRemovalToggleProps {
  itemId: string;
  checked: boolean;
  onCheckedChange: (checked: boolean) => void;
}

export function StainRemovalToggle({
  itemId,
  checked,
  onCheckedChange
}: StainRemovalToggleProps) {
  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-2">
        <FlaskConical className="h-4 w-4 text-orange-500" />
        <Label htmlFor={`stain-${itemId}`} className="text-sm">Stain removal treatment (+10%)</Label>
      </div>
      <AndroidSwitch
        id={`stain-${itemId}`}
        checked={checked}
        onCheckedChange={onCheckedChange}
      />
    </div>
  );
}
