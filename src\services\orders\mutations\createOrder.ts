
// Import the necessary functions
import { supabase } from "@/integrations/supabase/client";
import { Order } from "@/types";
import { auditService } from '@/services/audit/auditService';
import { 
  createOrderNotification as createNotification 
} from "@/services/notifications";

// Import our helper modules
import { fetchClientData, resolveClientId } from "./helpers/clientDataHandler";
import { generateReferenceCode } from "./helpers/referenceCodeGenerator";
import { prepareOrderData } from "./helpers/orderDataPreparer";
import { mapSupabaseOrderToOrderType } from "./helpers/orderMapper";
import { createLocalOrder } from "./helpers/localStorageHandler";

/**
 * Create a new order in Supabase or local storage
 */
export async function createOrder(orderData: any): Promise<Order> {
  try {
    // Check authentication status
    const { data: { session } } = await supabase.auth.getSession();
    const isAuthenticated = !!session;
    
    // Validate customer type
    if (!orderData.customerType) {
      console.error("Missing customerType field");
      throw new Error("Customer type is required");
    }
    
    console.log("Creating order with data:", {
      customerType: orderData.customerType,
      clientId: orderData.clientId || 'none',
      amount: orderData.orderAmount || orderData.amount || 0,
      deliveryDate: orderData.deliveryDate || 'none'
    });
    
    // Ensure delivery date is properly formatted
    let deliveryDate = orderData.deliveryDate;
    if (deliveryDate) {
      try {
        // Parse the date and format it as YYYY-MM-DD
        const dateObj = new Date(deliveryDate);
        if (!isNaN(dateObj.getTime())) {
          deliveryDate = dateObj.toISOString().split('T')[0];
        } else {
          // If date is invalid, use today + 1 day
          const tomorrow = new Date();
          tomorrow.setDate(tomorrow.getDate() + 1);
          deliveryDate = tomorrow.toISOString().split('T')[0];
          console.log("Invalid delivery date provided, using tomorrow's date:", deliveryDate);
        }
      } catch (error) {
        console.error("Error formatting delivery date:", error);
        // Use tomorrow as fallback
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        deliveryDate = tomorrow.toISOString().split('T')[0];
      }
    } else {
      // If no date provided, default to tomorrow
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      deliveryDate = tomorrow.toISOString().split('T')[0];
      console.log("No delivery date provided, using tomorrow's date:", deliveryDate);
    }
    
    // Update orderData with formatted date
    orderData.deliveryDate = deliveryDate;
    
    // Resolve the client ID based on customer type
    const clientId = await resolveClientId(orderData.customerType, orderData.clientId);
    
    // For client orders, fetch client data to get prefix
    let clientPrefix = '';
    if (orderData.customerType === 'client' && clientId) {
      try {
        const clientData = await fetchClientData(clientId as string);
        clientPrefix = clientData.prefix || '';
        console.log("Using client prefix for order:", clientPrefix);
        
        // Store the client prefix in orderData for reference code generation
        orderData.clientPrefix = clientPrefix;
      } catch (error) {
        console.error("Error fetching client prefix:", error);
      }
    }
    
    // Generate reference code
    const referenceCode = generateReferenceCode(orderData);
    console.log("Using reference code:", referenceCode);
    
    // Initialize customer data
    let customerNameToUse = '';
    let customerPhoneToUse = '';
    let contactPerson = '';
    
    if (orderData.customerType === 'client') {
      // For client orders, fetch client data if not provided
      if (!orderData.contactPerson || !orderData.clientPhone) {
        try {
          const clientData = await fetchClientData(clientId as string);
          customerNameToUse = clientData.name;
          contactPerson = clientData.contactPerson;
          customerPhoneToUse = clientData.phone;
        } catch (err) {
          console.error("Error fetching client details:", err);
        }
      } else {
        // Use provided contact information
        customerNameToUse = orderData.customerName || orderData.customer?.name || '';
        contactPerson = orderData.contactPerson || '';
        customerPhoneToUse = orderData.clientPhone || orderData.phoneNumber || orderData.customer?.phone || '';
      }
    } else {
      // Use provided customer information for walk-in
      customerNameToUse = orderData.customerName || orderData.customer?.name || "Walk-in Customer";
      customerPhoneToUse = orderData.phoneNumber || orderData.customer?.phone || "";
    }
    
    // Prepare the order data for insertion
    const newOrder = prepareOrderData(
      orderData,
      { customerNameToUse, customerPhoneToUse, contactPerson },
      clientId,
      referenceCode
    );
    
    console.log("Final order data to insert:", {
      ...newOrder,
      delivery_date: newOrder.delivery_date
    });
    
    let createdOrder: any;
    
    if (isAuthenticated) {
      // Create order in Supabase
      const { data, error } = await supabase
        .from('orders')
        .insert(newOrder)
        .select()
        .single();
        
      if (error) {
        console.error("Supabase error creating order:", error);
        throw error;
      }
      
      console.log("Order created successfully in Supabase:", data);
      
      // Map the Supabase data to the Order type
      createdOrder = mapSupabaseOrderToOrderType(data);
      
      // Log the order creation
      await auditService.logOrderAction('create', createdOrder.id, {
        clientId: createdOrder.clientId,
        customerName: createdOrder.customer.name,
        amount: createdOrder.amount,
        status: createdOrder.status,
        customerType: createdOrder.customerType
      });
    } else {
      // Create order locally (offline mode)
      createdOrder = createLocalOrder(newOrder);
      console.log("Created local order (offline mode):", createdOrder);
    }
    
    // Create notification for new order
    try {
      const customerName = createdOrder.customer.name || "Customer";
      
      await createNotification(
        "New Order Created",
        `${customerName} (${createdOrder.customerType}) order has been created by staff`,
        createdOrder.uuid // Use UUID not ID for notifications
      ).catch(err => {
        // Catch and handle notification errors to prevent them from breaking the main flow
        console.log("Failed to create notification, but order was created successfully:", err);
      });
    } catch (notificationError) {
      console.error("Error creating notification:", notificationError);
      // Don't throw the error - notifications are non-critical
    }
    
    return createdOrder;
  } catch (error) {
    console.error("Error creating order:", error);
    throw error;
  }
}
