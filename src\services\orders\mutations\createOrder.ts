
// Import the necessary functions
import { supabase } from "@/integrations/supabase/client";
import { v4 as uuidv4 } from 'uuid';
import { getNextOrderNumber } from "@/services/orders/store";
import { Order } from "@/types"; // Import from types instead of local types
import { auditService } from '@/services/audit/auditService';
import { toDbNumber, fromDbNumber } from "@/utils/db-converters";

/**
 * Create a new order in Supabase or local storage
 */
export async function createOrder(orderData: any): Promise<Order> {
  try {
    // Check authentication status
    const { data: { session } } = await supabase.auth.getSession();
    const isAuthenticated = !!session;
    
    // Generate a reference code based on client prefix if available
    const referenceCode = orderData.prefix 
      ? `${orderData.prefix}-${getNextOrderNumber()}`
      : `ORD-${getNextOrderNumber()}`;
    
    console.log("Creating order with data:", {
      customerType: orderData.customerType,
      clientId: orderData.clientId || 'none',
      amount: orderData.orderAmount || orderData.amount || 0
    });
    
    // Ensure we have a valid amount value
    const orderAmount = parseFloat(orderData.orderAmount) || parseFloat(orderData.amount) || 0;
    console.log("Setting order amount to:", orderAmount);
    
    // Handle client ID based on customer type
    let clientId = null;
    
    if (orderData.customerType === 'client') {
      // For client orders, use provided client ID
      if (!orderData.clientId) {
        console.error("Missing clientId for client order");
        throw new Error("Client ID is required for client orders");
      }
      clientId = orderData.clientId;
    } else {
      // For walk-in orders, use a default/generic client
      const { data: walkInClient } = await supabase
        .from('clients')
        .select('id')
        .eq('name', 'Walk-in Customers')
        .limit(1);

      if (walkInClient && walkInClient.length > 0) {
        clientId = walkInClient[0].id;
        console.log("Using dedicated walk-in client for walk-in order:", clientId);
      } else {
        // If no dedicated walk-in client exists, get any client as fallback
        const { data: fallbackClient } = await supabase
          .from('clients')
          .select('id')
          .limit(1);
          
        if (fallbackClient && fallbackClient.length > 0) {
          clientId = fallbackClient[0].id;
          console.log("Using fallback client for walk-in order:", clientId);
        } else {
          console.error("No client found for walk-in order");
          throw new Error("No valid client found for walk-in order");
        }
      }
    }
    
    // Prepare order data with correct types for Supabase
    const newOrder = {
      client_id: clientId,
      status: orderData.status || 'processing',
      amount: orderAmount,
      paid_amount: parseFloat(orderData.paidAmount) || 0,
      delivery_date: orderData.deliveryDate,
      items: orderData.lineItems || [],
      use_detergent: orderData.useDetergent || false,
      use_conditioner: orderData.useFabricConditioner || false,
      use_stain_remover: orderData.useStainRemover || false,
      use_bleach: orderData.useBleach || false,
      detergent_quantity: toDbNumber(parseInt(orderData.detergentQuantity) || 1),
      conditioner_quantity: toDbNumber(parseInt(orderData.conditionerQuantity) || 1),
      customer_name: orderData.customer?.name || orderData.customerName || "Walk-in Customer",
      phone_number: orderData.customer?.phone || orderData.phoneNumber || "",
      reference_code: referenceCode,
      notes: orderData.notes || null,
      customer_type: orderData.customerType || 'walk-in',
      number_of_pieces: parseInt(orderData.numberOfPieces || '1', 10),
      weight_kilos: parseFloat(orderData.weightKilos || '0'),
      vat_amount: orderData.vatAmount || null,
      subtotal_before_vat: orderData.subtotal || null,
      service_type: orderData.serviceType || null
    };
    
    console.log("Final order data to insert:", newOrder);
    
    let createdOrder: any;
    
    if (isAuthenticated) {
      // Create order in Supabase
      const { data, error } = await supabase
        .from('orders')
        .insert(newOrder)
        .select()
        .single();
        
      if (error) {
        console.error("Supabase error creating order:", error);
        throw error;
      }
      
      console.log("Order created successfully in Supabase:", data);
      
      // Map the Supabase data to the Order type
      createdOrder = mapSupabaseOrderToOrderType(data);
      
      // Log the order creation
      await auditService.logOrderAction('create', createdOrder.id, {
        clientId: createdOrder.clientId,
        customerName: createdOrder.customer.name,
        amount: createdOrder.amount,
        status: createdOrder.status,
        customerType: createdOrder.customerType
      });
    } else {
      // Create order locally (offline mode)
      const localOrderData = {
        id: uuidv4(),
        ...newOrder,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };
      
      // Map the local data to the Order type
      createdOrder = mapSupabaseOrderToOrderType(localOrderData);
      
      // Save to local storage
      const localOrders = JSON.parse(localStorage.getItem('orders') || '[]');
      localOrders.push(localOrderData);
      localStorage.setItem('orders', JSON.stringify(localOrders));
      
      console.log("Created local order (offline mode):", createdOrder);
    }
    
    return createdOrder;
  } catch (error) {
    console.error("Error creating order:", error);
    throw error;
  }
}

/**
 * Helper function to map Supabase order data to our Order type
 */
function mapSupabaseOrderToOrderType(data: any): Order {
  return {
    id: data.id,
    uuid: data.id, // Store the Supabase UUID as a separate field
    clientId: data.client_id,
    orderDate: data.created_at || new Date().toISOString(),
    deliveryDate: data.delivery_date || '',
    customer: {
      name: data.customer_name || '',
      phone: data.phone_number || ''
    },
    amount: typeof data.amount === 'string' ? fromDbNumber(data.amount) : data.amount || 0, // Handle both string and number
    paidAmount: typeof data.paid_amount === 'string' ? fromDbNumber(data.paid_amount) : data.paid_amount || 0, // Handle both string and number
    status: data.status || 'processing',
    lineItems: data.items || [],
    customerType: data.customer_type || 'walk-in',
    weightKilos: data.weight_kilos || 0,
    numberOfPieces: data.number_of_pieces || 0,
    useDetergent: data.use_detergent || false,
    useFabricConditioner: data.use_conditioner || false,
    useStainRemover: data.use_stain_remover || false,
    useBleach: data.use_bleach || false,
    detergentType: 'regular', // Default since there's no column in DB
    conditionerType: 'regular', // Default since there's no column in DB
    detergentQuantity: fromDbNumber(data.detergent_quantity) || 1,
    conditionerQuantity: fromDbNumber(data.conditioner_quantity) || 1,
    prefix: data.reference_code?.split('-')[0] || '',
    serviceType: data.service_type || '',
    notes: data.notes || '',
    vatAmount: data.vat_amount || 0,
    subtotalBeforeVAT: data.subtotal_before_vat || 0
  };
}
