
import { useState, useEffect } from "react";
import { toast } from "sonner";
import { Client, getClients } from "@/services/clients";
import { supabase } from "@/integrations/supabase/client";
import { ClientWithUser } from "./types";

export function useClientBatchCreation() {
  const [clients, setClients] = useState<ClientWithUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [successfulCreations, setSuccessfulCreations] = useState<ClientWithUser[]>([]);
  
  // Load clients
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        
        // Fetch clients
        const clientData = await getClients();
        
        // Map clients to the internal format and check existing users
        const clientsWithUserState = clientData.map(client => {
          return {
            client,
            hasUser: false, // We'll update this later when we check for existing users
            selected: false,
            email: client.email || '',
            processing: false,
            success: false
          };
        });
        
        // Check which emails are already registered
        for (const client of clientsWithUserState) {
          if (client.email) {
            try {
              // Use supabase.functions.invoke instead of direct fetch for better auth handling
              const { data: result, error } = await supabase.functions.invoke("check-email-exists", {
                body: { email: client.email }
              });
              
              if (error) {
                console.error("Error checking if email exists:", error);
                continue;
              }
              
              if (result && result.exists) {
                client.hasUser = true;
              }
            } catch (error) {
              console.error("Failed to check if email exists:", error);
            }
          }
        }
        
        setClients(clientsWithUserState);
      } catch (error) {
        console.error("Failed to load clients:", error);
        toast.error("Failed to load clients");
      } finally {
        setLoading(false);
      }
    };
    
    loadData();
  }, []);

  // Toggle selection for a client
  const toggleSelect = (index: number) => {
    setClients(prev => {
      const updated = [...prev];
      updated[index] = {
        ...updated[index],
        selected: !updated[index].selected
      };
      return updated;
    });
  };
  
  // Toggle selection for all clients
  const toggleSelectAll = () => {
    const someSelected = clients.some(c => c.selected);
    setClients(prev => prev.map(client => ({
      ...client,
      selected: !someSelected && !client.hasUser && !!client.email
    })));
  };
  
  // Update email for a client
  const updateEmail = (index: number, email: string) => {
    setClients(prev => {
      const updated = [...prev];
      updated[index] = {
        ...updated[index],
        email
      };
      return updated;
    });
  };
  
  // Create user accounts for selected clients
  const createUserAccounts = async () => {
    const selectedClients = clients.filter(c => c.selected);
    if (selectedClients.length === 0) {
      toast.warning("No clients selected for user creation");
      return;
    }
    
    const anyMissingEmail = selectedClients.some(c => !c.email);
    if (anyMissingEmail) {
      toast.error("All selected clients must have an email address");
      return;
    }
    
    setProcessing(true);
    setSuccessfulCreations([]);
    
    // Create user accounts one by one
    for (let i = 0; i < clients.length; i++) {
      if (!clients[i].selected) continue;
      
      // Mark this client as processing
      setClients(prev => {
        const updated = [...prev];
        updated[i] = { ...updated[i], processing: true };
        return updated;
      });
      
      try {
        // Use supabase.functions.invoke instead of direct fetch for better auth handling
        const { data: result, error } = await supabase.functions.invoke("create-user", {
          body: {
            email: clients[i].email,
            password: "Client1234",
            first_name: clients[i].client.contact_person?.split(' ')[0] || '',
            last_name: clients[i].client.contact_person?.split(' ').slice(1).join(' ') || '',
            phone: clients[i].client.phone || '',
            role: "client",
            client_id: clients[i].client.id
          }
        });
        
        if (error) {
          throw new Error(error.message);
        }
        
        if (result && result.userId) {
          // Mark as successful
          setClients(prev => {
            const updated = [...prev];
            updated[i] = { 
              ...updated[i], 
              processing: false, 
              success: true, 
              hasUser: true 
            };
            return updated;
          });
          
          // Add to successful creations
          setSuccessfulCreations(prev => [...prev, clients[i]]);
          
          toast.success(`Created user account for ${clients[i].client.name}`);
        } else {
          // Handle error
          setClients(prev => {
            const updated = [...prev];
            updated[i] = { 
              ...updated[i], 
              processing: false, 
              success: false,
              error: "Unknown error occurred during user creation" 
            };
            return updated;
          });
          
          toast.error(`Failed to create user for ${clients[i].client.name}`);
        }
      } catch (error) {
        console.error(`Error creating user for ${clients[i].client.name}:`, error);
        setClients(prev => {
          const updated = [...prev];
          updated[i] = { 
            ...updated[i], 
            processing: false, 
            success: false,
            error: error instanceof Error ? error.message : "Unexpected error occurred" 
          };
          return updated;
        });
        
        toast.error(`Failed to create user for ${clients[i].client.name}: ${error instanceof Error ? error.message : "Unknown error"}`);
      }
    }
    
    setProcessing(false);
  };

  const selectedCount = clients.filter(c => c.selected).length;
  const availableClientsCount = clients.filter(c => !c.hasUser && !!c.email).length;

  return {
    clients,
    loading,
    processing,
    successfulCreations,
    selectedCount,
    availableClientsCount,
    toggleSelect,
    toggleSelectAll,
    updateEmail,
    createUserAccounts
  };
}
