import React from 'react';
import { cn } from '@/lib/utils';

interface AndroidSwitchProps {
  checked: boolean;
  onCheckedChange: (checked: boolean) => void;
  disabled?: boolean;
  className?: string;
  id?: string;
}

export function AndroidSwitch({
  checked,
  onCheckedChange,
  disabled = false,
  className,
  id
}: AndroidSwitchProps) {
  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!disabled) {
      console.log('AndroidSwitch clicked:', { checked, id });
      onCheckedChange(!checked);
    }
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    e.preventDefault();
    if (!disabled) {
      console.log('AndroidSwitch touch end:', { checked, id });
      onCheckedChange(!checked);
    }
  };

  return (
    <button
      type="button"
      role="switch"
      aria-checked={checked}
      id={id}
      className={cn(
        // Base styles
        "relative inline-flex h-8 w-14 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out",
        "focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2",
        "touch-action-manipulation",
        "active:scale-95",
        // Checked state
        checked
          ? "bg-primary"
          : "bg-gray-200",
        // Disabled state
        disabled && "opacity-50 cursor-not-allowed",
        className
      )}
      onClick={handleClick}
      onTouchEnd={handleTouchEnd}
      disabled={disabled}
    >
      <span
        className={cn(
          // Base thumb styles
          "pointer-events-none inline-block h-6 w-6 transform rounded-full bg-white shadow-lg ring-0 transition duration-200 ease-in-out",
          // Position based on checked state
          checked ? "translate-x-6" : "translate-x-1"
        )}
      />
    </button>
  );
}
