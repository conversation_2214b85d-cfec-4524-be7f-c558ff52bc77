
import { Order } from "@/types";
import { fromDbNumber } from "@/utils/db-converters";

/**
 * Maps Supabase order data to our Order type
 */
export function mapSupabaseOrderToOrderType(data: any): Order {
  // Handle dry cleaning items - parse JSON if it's a string
  let dryCleaningItems = [];
  if (data.dry_cleaning_items) {
    try {
      dryCleaningItems = typeof data.dry_cleaning_items === 'string' 
        ? JSON.parse(data.dry_cleaning_items) 
        : data.dry_cleaning_items;
    } catch (e) {
      console.error('Error parsing dry cleaning items:', e);
      dryCleaningItems = [];
    }
  }

  return {
    id: data.reference_code || data.id,
    uuid: data.id, // Store the Supabase UUID as a separate field
    clientId: data.client_id,
    orderDate: data.created_at || new Date().toISOString(),
    deliveryDate: data.delivery_date || '',
    customer: {
      name: data.customer_name || '',
      phone: data.phone_number || '',
      contactPerson: data.contact_person || ''
    },
    amount: typeof data.amount === 'string' ? fromDbNumber(data.amount) : data.amount || 0,
    paidAmount: typeof data.paid_amount === 'string' ? fromDbNumber(data.paid_amount) : data.paid_amount || 0,
    status: data.status || 'processing',
    lineItems: data.items || [],
    customerType: data.customer_type || 'walk-in',
    weightKilos: data.weight_kilos || 0,
    numberOfPieces: data.number_of_pieces || 0,
    useDetergent: data.use_detergent || false,
    useFabricConditioner: data.use_conditioner || false,
    useStainRemover: data.use_stain_remover || false,
    useBleach: data.use_bleach || false,
    detergentType: 'regular', // Default since there's no column in DB
    conditionerType: 'regular', // Default since there's no column in DB
    detergentQuantity: fromDbNumber(data.detergent_quantity) || 1,
    conditionerQuantity: fromDbNumber(data.conditioner_quantity) || 1,
    prefix: data.reference_code?.split('-')[0] || '',
    serviceType: data.service_type || '',
    notes: data.notes || '',
    vatAmount: data.vat_amount || 0,
    subtotalBeforeVAT: data.subtotal_before_vat || 0,
    reference_code: data.reference_code,
    isDryCleaning: data.is_dry_cleaning || false,
    dryCleaningItems: dryCleaningItems,
    selectedClientItems: data.items || [] // Also store as selectedClientItems for compatibility
  };
}
