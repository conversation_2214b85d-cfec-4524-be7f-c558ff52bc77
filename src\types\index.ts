

export type CustomerType = "walk-in" | "client";

export interface Client {
  id: string;
  created_at: string;
  name: string;
  phone: string;
  email?: string;
  billing_address: string;
  shipping_address?: string;
  payment_terms: number;
  prefix?: string;
  balance: number;
  updated_at: string;
  userId?: string;
  contact_person?: string;
}

export interface OrderItem {
  id: string;
  name: string;
  description?: string;
  price: number;
  image?: string;
}

export interface LineItem {
  id: string;
  name: string;
  quantity: number;
  unitPrice: number;
  total: number;
  treatmentDescription?: string;
}

export interface Order {
  id: string;
  uuid?: string;
  orderDate: string;
  deliveryDate: string;
  customer: {
    name: string;
    phone: string;
    contactPerson?: string;
  };
  amount: number;
  paidAmount: number;
  status: string;
  lineItems: LineItem[];
  customerType: CustomerType;
  clientId?: string;
  weightKilos?: number;
  numberOfPieces?: number;
  useDetergent?: boolean;
  useFabricConditioner?: boolean;
  useStainRemover?: boolean;
  useBleach?: boolean;
  detergentType?: 'none' | 'regular' | 'color';
  conditionerType?: 'none' | 'regular' | 'fresh' | 'floral';
  detergentQuantity?: number;
  conditionerQuantity?: number;
  prefix?: string;
  serviceType?: string;
  notes?: string;
  reference_code?: string;
  vatAmount?: number;
  subtotalBeforeVAT?: number;
  items?: string | any[]; // Added proper typing for items field
}

export interface Staff {
  id: string;
  createdAt: string;
  fullName: string;
  phone: string;
  role: string;
  email: string;
}

