
import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Printer, FileText } from "lucide-react";
import { Order } from "@/types";
import { useToast } from "@/hooks/use-toast";
import { usePrinterContext } from "@/contexts/PrinterContext";
import { fallbackBrowserPrint } from "../utils/printUtils";

interface PrintButtonsProps {
  order: Order;
  isStaff: boolean;
}

export function PrintButtons({ order, isStaff }: PrintButtonsProps) {
  const { toast } = useToast();
  const { printerStatus, printReceipt, printJobOrder, showPrinterConnect } = usePrinterContext();
  const [isPrinting, setIsPrinting] = useState(false);

  const handlePrintReceipt = async () => {
    const useBluetoothPrinter = localStorage.getItem('useBluetoothPrinter') === 'true';
    
    // If Bluetooth printing is enabled and the printer is connected
    if (useBluetoothPrinter && printerStatus === 'connected') {
      try {
        setIsPrinting(true);
        const success = await printReceipt(order);
        
        if (success) {
          toast({
            title: "Print Successful",
            description: `Customer receipt for ${order.id} has been sent to printer`,
          });
        } else {
          throw new Error('Printing failed');
        }
      } catch (error) {
        console.error('Print error:', error);
        toast({
          title: "Print Failed",
          description: "Could not print the receipt. Please try again.",
          variant: "destructive",
        });
        
        // Fall back to browser-based printing
        const result = fallbackBrowserPrint(order, 'receipt');
        if (result.success) {
          toast({
            title: "Print Initiated",
            description: `${result.title} for ${order.id} has been sent to printer`,
          });
        }
      } finally {
        setIsPrinting(false);
      }
    }
    // If Bluetooth printing is enabled but printer is not connected
    else if (useBluetoothPrinter && printerStatus !== 'connected') {
      toast({
        title: "Printer Not Connected",
        description: "Connect your Bluetooth printer in Settings",
        variant: "destructive",
        action: (
          <Button variant="outline" size="sm" onClick={showPrinterConnect}>
            Connect
          </Button>
        )
      });
      
      // Fall back to browser-based printing
      const result = fallbackBrowserPrint(order, 'receipt');
      if (result.success) {
        toast({
          title: "Print Initiated",
          description: `${result.title} for ${order.id} has been sent to printer`,
        });
      }
    }
    // Use browser-based printing
    else {
      const result = fallbackBrowserPrint(order, 'receipt');
      if (result.success) {
        toast({
          title: "Print Initiated",
          description: `${result.title} for ${order.id} has been sent to printer`,
        });
      }
    }
  };

  const handlePrintJobOrder = async () => {
    const useBluetoothPrinter = localStorage.getItem('useBluetoothPrinter') === 'true';
    
    // If Bluetooth printing is enabled and the printer is connected
    if (useBluetoothPrinter && printerStatus === 'connected') {
      try {
        setIsPrinting(true);
        const success = await printJobOrder(order);
        
        if (success) {
          toast({
            title: "Print Successful",
            description: `Job order for ${order.id} has been sent to printer`,
          });
        } else {
          throw new Error('Printing failed');
        }
      } catch (error) {
        console.error('Print error:', error);
        toast({
          title: "Print Failed",
          description: "Could not print the job order. Please try again.",
          variant: "destructive",
        });
        
        // Fall back to browser-based printing
        const result = fallbackBrowserPrint(order, 'jobOrder');
        if (result.success) {
          toast({
            title: "Print Initiated",
            description: `${result.title} for ${order.id} has been sent to printer`,
          });
        }
      } finally {
        setIsPrinting(false);
      }
    }
    // If Bluetooth printing is enabled but printer is not connected
    else if (useBluetoothPrinter && printerStatus !== 'connected') {
      toast({
        title: "Printer Not Connected",
        description: "Connect your Bluetooth printer in Settings",
        variant: "destructive",
        action: (
          <Button variant="outline" size="sm" onClick={showPrinterConnect}>
            Connect
          </Button>
        )
      });
      
      // Fall back to browser-based printing
      const result = fallbackBrowserPrint(order, 'jobOrder');
      if (result.success) {
        toast({
          title: "Print Initiated",
          description: `${result.title} for ${order.id} has been sent to printer`,
        });
      }
    }
    // Use browser-based printing
    else {
      const result = fallbackBrowserPrint(order, 'jobOrder');
      if (result.success) {
        toast({
          title: "Print Initiated",
          description: `${result.title} for ${order.id} has been sent to printer`,
        });
      }
    }
  };

  return (
    <>
      {/* Customer Receipt Button */}
      <Button
        variant="ghost"
        size="icon"
        onClick={handlePrintReceipt}
        title="Print Customer Receipt"
        disabled={isPrinting}
      >
        <Printer className={`h-4 w-4 ${isPrinting ? 'animate-pulse' : ''}`} />
      </Button>

      {/* Job Order Button - only for staff and admin */}
      {isStaff && (
        <Button
          variant="ghost"
          size="icon"
          onClick={handlePrintJobOrder}
          title="Print Job Order for Staff"
          disabled={isPrinting}
        >
          <FileText className={`h-4 w-4 ${isPrinting ? 'animate-pulse' : ''}`} />
        </Button>
      )}
    </>
  );
}
