
import { Order } from "@/types";
import { useToast } from "@/hooks/use-toast";
import { usePrinterContext } from "@/contexts/PrinterContext";
import { useState } from "react";

export function usePrintFunctions() {
  const [isPrinting, setIsPrinting] = useState(false);
  const { printerStatus, printReceipt, printJobOrder, showPrinterConnect } = usePrinterContext();
  const { toast } = useToast();
  
  const handlePrintReceipt = async (order: Order) => {
    const useBluetoothPrinter = localStorage.getItem('useBluetoothPrinter') === 'true';
    
    // If Bluetooth printing is enabled and the printer is connected
    if (useBluetoothPrinter && printerStatus === 'connected') {
      try {
        setIsPrinting(true);
        const success = await printReceipt(order);
        
        if (success) {
          toast({
            title: "Print Successful",
            description: `Customer receipt for ${order.id} has been sent to printer`,
          });
        } else {
          throw new Error('Printing failed');
        }
      } catch (error) {
        console.error('Print error:', error);
        toast({
          title: "Print Failed",
          description: "Could not print the receipt. Please try again.",
          variant: "destructive",
        });
        
        // Fall back to browser-based printing
        fallbackBrowserPrint(order, 'receipt');
      } finally {
        setIsPrinting(false);
      }
    }
    // If Bluetooth printing is enabled but printer is not connected
    else if (useBluetoothPrinter && printerStatus !== 'connected') {
      toast({
        title: "Printer Not Connected",
        description: "Connect your Bluetooth printer in Settings",
        variant: "destructive",
        action: (
          <Button variant="outline" size="sm" onClick={showPrinterConnect}>
            Connect
          </Button>
        )
      });
      
      // Fall back to browser-based printing
      fallbackBrowserPrint(order, 'receipt');
    }
    // Use browser-based printing
    else {
      fallbackBrowserPrint(order, 'receipt');
    }
  };

  const handlePrintJobOrder = async (order: Order) => {
    const useBluetoothPrinter = localStorage.getItem('useBluetoothPrinter') === 'true';
    
    // If Bluetooth printing is enabled and the printer is connected
    if (useBluetoothPrinter && printerStatus === 'connected') {
      try {
        setIsPrinting(true);
        const success = await printJobOrder(order);
        
        if (success) {
          toast({
            title: "Print Successful",
            description: `Job order for ${order.id} has been sent to printer`,
          });
        } else {
          throw new Error('Printing failed');
        }
      } catch (error) {
        console.error('Print error:', error);
        toast({
          title: "Print Failed",
          description: "Could not print the job order. Please try again.",
          variant: "destructive",
        });
        
        // Fall back to browser-based printing
        fallbackBrowserPrint(order, 'jobOrder');
      } finally {
        setIsPrinting(false);
      }
    }
    // If Bluetooth printing is enabled but printer is not connected
    else if (useBluetoothPrinter && printerStatus !== 'connected') {
      toast({
        title: "Printer Not Connected",
        description: "Connect your Bluetooth printer in Settings",
        variant: "destructive",
        action: (
          <Button variant="outline" size="sm" onClick={showPrinterConnect}>
            Connect
          </Button>
        )
      });
      
      // Fall back to browser-based printing
      fallbackBrowserPrint(order, 'jobOrder');
    }
    // Use browser-based printing
    else {
      fallbackBrowserPrint(order, 'jobOrder');
    }
  };

  const fallbackBrowserPrint = (order: Order, type: 'receipt' | 'jobOrder') => {
    try {
      // Get add-on quantities from order data
      const detergentQty = order.detergentQuantity || 1;
      const conditionerQty = order.conditionerQuantity || 1;
      
      // Format the order data for printing
      let printContent = '';
      let title = '';
      
      if (type === 'receipt') {
        title = 'Customer Receipt';
        printContent = `
CMC LAUNDRY
-----------
ORDER SLIP
----------
Order ID: ${order.id}
Date: ${order.orderDate}
Customer: ${order.customer.name}
Phone: ${order.customer.phone}

Items:
${order.lineItems?.map(item => 
  `${item.name} x${item.quantity} - ₱${item.total.toFixed(2)}`
).join('\n') || 'No items'}

Weight: ${order.weightKilos || 0} kg
Pieces: ${order.numberOfPieces || 0}

Add-ons:
${order.useDetergent ? `- Detergent x${detergentQty}` : ''}
${order.useFabricConditioner ? `- Fabric Conditioner x${conditionerQty}` : ''}
${order.useStainRemover ? '- Stain Remover' : ''}
${order.useBleach ? '- Bleach Treatment' : ''}

Subtotal: ₱${order.subtotalBeforeVAT?.toFixed(2) || '0.00'}
VAT: ₱${order.vatAmount?.toFixed(2) || '0.00'}
Total Amount: ₱${order.amount.toFixed(2)}
Paid Amount: ₱${order.paidAmount.toFixed(2)}
Balance: ₱${(order.amount - order.paidAmount).toFixed(2)}

Status: ${order.status.toUpperCase()}
        `.trim();
      } else {
        title = 'Job Order';
        printContent = `
CMC LAUNDRY
-----------
JOB ORDER
----------
Order ID: ${order.id}
Date: ${order.orderDate}
Service: ${order.serviceType?.toUpperCase() || "REGULAR SERVICE"}
Customer: ${order.customer.name}
Phone: ${order.customer.phone}

ITEMS TO PROCESS:
${order.lineItems?.map(item => 
  `- ${item.name} x${item.quantity}`
).join('\n') || 'No specific items'}

SPECIFICATIONS:
Weight: ${order.weightKilos || 0} kg
Pieces: ${order.numberOfPieces || 0}

PROCESSING INSTRUCTIONS:
${order.useDetergent ? `☐ Use Detergent: ${order.detergentType || "Standard"}\n  Quantity: ${detergentQty}` : ''}
${order.useFabricConditioner ? `☐ Use Fabric Conditioner: ${order.conditionerType || "Standard"}\n  Quantity: ${conditionerQty}` : ''}
${order.useStainRemover ? '☐ Apply Stain Remover Treatment' : ''}
${order.useBleach ? '☐ Apply Bleach Treatment' : ''}

${order.notes ? `SPECIAL NOTES:\n${order.notes}` : ''}

STAFF PROCESSING CHECKLIST:
☐ Sorting Complete
☐ Pre-treatment Applied
☐ Washing Complete
☐ Drying Complete
☐ Folding Complete
☐ Quality Check
☐ Ready for Pickup

Assigned Staff: ________________

Status: ${order.status.toUpperCase()}
        `.trim();
      }

      // Send to browser printer
      const printWindow = window.open('', '', 'width=600,height=600');
      if (!printWindow) {
        throw new Error('Could not open print window');
      }

      printWindow.document.open();
      printWindow.document.write(`
        <html>
          <head>
            <title>${title} - ${order.id}</title>
            <style>
              body {
                font-family: monospace;
                font-size: 12px;
                white-space: pre;
                margin: 0;
                padding: 20px;
              }
              @media print {
                body { margin: 0; }
              }
            </style>
          </head>
          <body>${printContent}</body>
        </html>
      `);
      printWindow.document.close();
      printWindow.print();
      printWindow.close();

      toast({
        title: "Print Initiated",
        description: `${title} for ${order.id} has been sent to printer`,
      });
    } catch (error) {
      console.error('Print error:', error);
      toast({
        title: "Print Failed",
        description: "Could not print the document. Please try again.",
        variant: "destructive",
      });
    }
  };

  return {
    isPrinting,
    handlePrintReceipt,
    handlePrintJobOrder
  };
}

// Import Button for toast action
import { Button } from "@/components/ui/button";
