
import { Order } from "@/types";
import { useState } from "react";
import { usePrinterConnection } from "./hooks/usePrinterConnection";
import { useBrowserPrinting } from "./hooks/useBrowserPrinting";
import { useBluetoothPrinting } from "./hooks/useBluetoothPrinting";

export function usePrintFunctions() {
  const { checkPrinterConnection } = usePrinterConnection();
  const { printInBrowser } = useBrowserPrinting();
  const { isPrinting, printToBluetoothDevice } = useBluetoothPrinting();
  
  const handlePrintReceipt = async (order: Order) => {
    const useBluetoothPrinter = localStorage.getItem('useBluetoothPrinter') === 'true';
    
    // Ensure treatments are applied correctly to pricing for receipt
    const processedOrder = processTreatmentsForPrinting(order);
    
    // Check if Bluetooth printer is connected
    const isConnected = checkPrinterConnection();
    
    // If Bluetooth printing is enabled and the printer is connected
    if (useBluetoothPrinter && isConnected) {
      const success = await printToBluetoothDevice(processedOrder, 'receipt');
      if (!success) {
        // Fall back to browser-based printing on failure
        printInBrowser(processedOrder, 'receipt');
      }
    } else {
      // Use browser-based printing
      printInBrowser(processedOrder, 'receipt');
    }
  };

  const handlePrintJobOrder = async (order: Order) => {
    const useBluetoothPrinter = localStorage.getItem('useBluetoothPrinter') === 'true';
    
    // Ensure treatments are applied correctly to pricing for job order
    const processedOrder = processTreatmentsForPrinting(order);
    
    // Check if Bluetooth printer is connected
    const isConnected = checkPrinterConnection();
    
    // If Bluetooth printing is enabled and the printer is connected
    if (useBluetoothPrinter && isConnected) {
      const success = await printToBluetoothDevice(processedOrder, 'jobOrder');
      if (!success) {
        // Fall back to browser-based printing on failure
        printInBrowser(processedOrder, 'jobOrder');
      }
    } else {
      // Use browser-based printing
      printInBrowser(processedOrder, 'jobOrder');
    }
  };

  // Helper function to process treatments and update pricing
  const processTreatmentsForPrinting = (order: Order): Order => {
    // Create a new copy of the order to avoid mutating the original
    const processedOrder = {...order};
    
    // Log the order before processing to confirm we have all needed data
    console.log('Processing order for printing:', processedOrder);
    
    // Process line items if they exist
    if (processedOrder.lineItems && processedOrder.lineItems.length > 0) {
      processedOrder.lineItems = processedOrder.lineItems.map(item => {
        // Calculate treatment cost if applicable
        const treatmentCost = calculateTreatmentCost(item.treatmentDescription || '');
        
        if (treatmentCost > 0) {
          return {
            ...item,
            // Update total to reflect treatment cost
            unitPrice: item.unitPrice + treatmentCost,
            total: item.quantity * (item.unitPrice + treatmentCost)
          };
        }
        
        return item;
      });
      
      // Recalculate total amount
      processedOrder.amount = processedOrder.lineItems.reduce(
        (sum, item) => sum + item.total, 
        0
      );
    }
    
    return processedOrder;
  };
  
  // Function to calculate treatment costs
  const calculateTreatmentCost = (treatment: string): number => {
    switch (treatment) {
      case 'Stain removal':
        return 15.00;
      case 'Bleaching':
        return 20.00;
      case 'Deep clean':
        return 25.00;
      case 'Delicate wash':
        return 18.00;
      default:
        return 0;
    }
  };

  return {
    isPrinting,
    handlePrintReceipt,
    handlePrintJobOrder
  };
}

// Export Button for toast action
import { Button } from "@/components/ui/button";
