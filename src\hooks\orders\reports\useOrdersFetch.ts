
import { useState, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { OrderSummary } from './types';
import { formatDate } from '@/lib/formatters';

export function useOrdersFetch() {
  const [orders, setOrders] = useState<OrderSummary[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const fetchOrders = useCallback(async (clientId: string) => {
    setIsLoading(true);
    
    try {
      console.log('Fetching orders for client ID:', clientId);
      
      const { data: ordersData, error } = await supabase
        .from('orders')
        .select('*')
        .eq('client_id', clientId);
      
      if (error) {
        throw error;
      }
      
      // Transform the data to match our OrderSummary type
      const transformedOrders: OrderSummary[] = ordersData.map(order => {
        // Parse the JSON items array
        const itemsArray = Array.isArray(order.items) ? order.items : [];
        
        // Calculate due date (delivery date + 30 days)
        const orderDate = new Date(order.created_at);
        const dueDate = new Date(orderDate);
        dueDate.setDate(dueDate.getDate() + 30); // Assuming 30 day payment terms
        
        // Check if order is overdue
        const isOverdue = order.paid_amount < order.amount && dueDate < new Date();
        
        return {
          id: order.id,
          referenceCode: order.reference_code || null,
          orderDate: formatDate(new Date(order.created_at)),
          amount: order.amount,
          paidAmount: order.paid_amount,
          status: order.status,
          dueDate: formatDate(dueDate),
          isOverdue,
          items: itemsArray.map((item: any) => ({
            name: item.name,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            total: item.total
          }))
        };
      });
      
      console.log('Fetched orders:', transformedOrders);
      setOrders(transformedOrders);
      return transformedOrders;
    } catch (error) {
      console.error('Error fetching orders:', error);
      return [];
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    isLoading,
    orders,
    fetchOrders
  };
}
