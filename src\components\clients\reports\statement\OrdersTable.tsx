
import { formatCurrency } from '@/lib/formatters';
import { OrderSummary } from '@/hooks/orders/reports/types';
import { StatusBadge } from './StatusBadge';
import { ItemsPopover } from './ItemsPopover';
import { Skeleton } from '@/components/ui/skeleton';

interface OrdersTableProps {
  orders: OrderSummary[];
  isLoading: boolean;
}

export function OrdersTable({ orders, isLoading }: OrdersTableProps) {
  if (isLoading) {
    return (
      <div className="space-y-3">
        {Array.from({ length: 5 }).map((_, index) => (
          <Skeleton key={index} className="h-12 w-full" />
        ))}
      </div>
    );
  }

  if (orders.length === 0) {
    return (
      <div className="text-center py-10 border rounded-md bg-muted/20">
        <p className="text-muted-foreground">No orders found for the selected period.</p>
      </div>
    );
  }

  return (
    <div className="overflow-x-auto">
      <table className="w-full border-collapse">
        <thead>
          <tr className="bg-muted/50">
            <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Reference</th>
            <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Date</th>
            <th className="px-4 py-3 text-right text-sm font-medium text-muted-foreground">Amount</th>
            <th className="px-4 py-3 text-right text-sm font-medium text-muted-foreground">Paid</th>
            <th className="px-4 py-3 text-right text-sm font-medium text-muted-foreground">Balance</th>
            <th className="px-4 py-3 text-center text-sm font-medium text-muted-foreground">Status</th>
            <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Items</th>
          </tr>
        </thead>
        <tbody>
          {orders.map((order) => {
            const isPaid = order.paidAmount >= order.amount;
            const isPartial = order.paidAmount > 0 && order.paidAmount < order.amount;
            const paidStatus = isPaid ? 'paid' : (isPartial ? 'partial' : 'unpaid');
            
            return (
              <tr key={order.id} className="border-b">
                <td className="px-4 py-3 text-sm">{order.referenceCode || '-'}</td>
                <td className="px-4 py-3 text-sm">{order.orderDate}</td>
                <td className="px-4 py-3 text-sm text-right">{formatCurrency(order.amount)}</td>
                <td className="px-4 py-3 text-sm text-right">{formatCurrency(order.paidAmount)}</td>
                <td className="px-4 py-3 text-sm text-right">{formatCurrency(Math.max(0, order.amount - order.paidAmount))}</td>
                <td className="px-4 py-3 text-sm text-center">
                  <StatusBadge 
                    status={order.status} 
                    paidStatus={paidStatus} 
                    isOverdue={order.isOverdue} 
                  />
                </td>
                <td className="px-4 py-3 text-sm">
                  <ItemsPopover items={order.items} />
                </td>
              </tr>
            );
          })}
        </tbody>
        <tfoot>
          <tr className="bg-muted/20 font-medium">
            <td className="px-4 py-3 text-sm" colSpan={2}>Total</td>
            <td className="px-4 py-3 text-sm text-right">{formatCurrency(orders.reduce((sum, order) => sum + order.amount, 0))}</td>
            <td className="px-4 py-3 text-sm text-right">{formatCurrency(orders.reduce((sum, order) => sum + order.paidAmount, 0))}</td>
            <td className="px-4 py-3 text-sm text-right">{formatCurrency(orders.reduce((sum, order) => sum + Math.max(0, order.amount - order.paidAmount), 0))}</td>
            <td colSpan={2}></td>
          </tr>
        </tfoot>
      </table>
    </div>
  );
}
