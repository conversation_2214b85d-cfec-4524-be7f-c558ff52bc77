
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from '@/components/ui/table';
import { Skeleton } from '@/components/ui/skeleton';
import { formatCurrency } from '@/lib/formatters';
import { ItemsPopover } from './ItemsPopover';
import { StatusBadge } from './StatusBadge';

interface OrdersTableProps {
  orders: any[];
  isLoading: boolean;
}

export function OrdersTable({ orders, isLoading }: OrdersTableProps) {
  if (isLoading) {
    return (
      <div className="space-y-2">
        {Array.from({ length: 5 }).map((_, i) => (
          <Skeleton key={i} className="h-12 w-full" />
        ))}
      </div>
    );
  }

  const getPaidStatus = (order: any) => {
    if (order.paidAmount >= order.amount) {
      return 'paid';
    } else if (order.paidAmount > 0) {
      return 'partial';
    } else {
      return 'unpaid';
    }
  };

  return (
    <div className="rounded-md border print:hidden">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Order ID</TableHead>
            <TableHead>Date</TableHead>
            <TableHead>Due Date</TableHead>
            <TableHead>Items</TableHead>
            <TableHead className="text-right">Total</TableHead>
            <TableHead className="text-right">Paid</TableHead>
            <TableHead className="text-right">Status</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {orders.length === 0 ? (
            <TableRow>
              <TableCell colSpan={7} className="text-center py-10 text-muted-foreground">
                No orders found for the selected criteria
              </TableCell>
            </TableRow>
          ) : (
            orders.map((order) => (
              <TableRow key={order.id} className={order.isOverdue ? "bg-red-50" : ""}>
                <TableCell className="font-medium">{order.referenceCode || order.id}</TableCell>
                <TableCell>{order.orderDate}</TableCell>
                <TableCell>{order.dueDate || 'N/A'}</TableCell>
                <TableCell>
                  <ItemsPopover items={order.items} />
                </TableCell>
                <TableCell className="text-right font-medium">{formatCurrency(order.amount)}</TableCell>
                <TableCell className="text-right">{formatCurrency(order.paidAmount)}</TableCell>
                <TableCell className="text-right">
                  <StatusBadge 
                    status={order.status} 
                    paidStatus={getPaidStatus(order)}
                    isOverdue={order.isOverdue}
                  />
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
}
