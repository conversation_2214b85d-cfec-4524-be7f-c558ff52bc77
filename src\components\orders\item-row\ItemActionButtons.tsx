
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Copy, X } from "lucide-react";

interface ItemActionButtonsProps {
  instanceId: string;
  onDuplicate: (instanceId: string) => void;
  onRemove: (instanceId: string) => void;
}

export function ItemActionButtons({ instanceId, onDuplicate, onRemove }: ItemActionButtonsProps) {
  return (
    <div className="flex items-center justify-between mt-2">
      <Button 
        variant="ghost" 
        size="sm" 
        className="text-xs"
        type="button"
        onClick={(e) => {
          e.preventDefault();
          onDuplicate(instanceId);
        }}
      >
        <Copy className="h-3 w-3 mr-1" /> Duplicate
      </Button>
      
      <Button 
        variant="ghost" 
        size="sm"
        className="text-xs text-destructive" 
        type="button"
        onClick={(e) => {
          e.preventDefault();
          onRemove(instanceId);
        }}
      >
        <X className="h-3 w-3 mr-1" /> Remove
      </Button>
    </div>
  );
}
