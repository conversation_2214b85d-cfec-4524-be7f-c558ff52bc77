
import { ReactNode } from "react";
import { MainLayout } from "@/components/layout/MainLayout";
import { ClientPortalLayout } from "@/components/layout/ClientPortalLayout";

// Pages - Admin/Staff
import Dashboard from "@/pages/Dashboard";
import Orders from "@/pages/Orders";
import Customers from "@/pages/Customers";
import Clients from "@/pages/Clients";
import Inventory from "@/pages/Inventory";
import Accounting from "@/pages/Accounting";
import Reports from "@/pages/Reports";
import Settings from "@/pages/Settings";
import Profile from "@/pages/Profile";
import Expenses from "@/pages/Expenses";

// Client Portal Pages
import ClientOrders from "@/pages/client/ClientOrders";
import ClientItems from "@/pages/client/ClientItems";
import ClientReports from "@/pages/client/ClientReports";

export type UserRole = "admin" | "staff" | "client" | null;

interface RouteConfig {
  path: string;
  element: ReactNode;
  layout?: React.ComponentType<{ children: ReactNode }>;
  allowedRoles: UserRole[];
}

// Admin/Staff Routes
export const adminPortalRoutes: RouteConfig[] = [
  {
    path: "/dashboard",
    element: <Dashboard />,
    layout: MainLayout,
    allowedRoles: ["admin", "staff"]
  },
  {
    path: "/orders",
    element: <Orders />,
    layout: MainLayout,
    allowedRoles: ["admin", "staff"]
  },
  {
    path: "/customers",
    element: <Customers />,
    layout: MainLayout,
    allowedRoles: ["admin", "staff"]
  },
  {
    path: "/clients",
    element: <Clients />,
    layout: MainLayout,
    allowedRoles: ["admin", "staff"]
  },
  {
    path: "/inventory",
    element: <Inventory />,
    layout: MainLayout,
    allowedRoles: ["admin"]
  },
  {
    path: "/expenses",
    element: <Expenses />,
    layout: MainLayout,
    allowedRoles: ["admin", "staff"]
  },
  {
    path: "/accounting",
    element: <Accounting />,
    layout: MainLayout,
    allowedRoles: ["admin"]
  },
  {
    path: "/reports",
    element: <Reports />,
    layout: MainLayout,
    allowedRoles: ["admin"] // Changed: removed "staff" from allowed roles
  },
  {
    path: "/settings",
    element: <Settings />,
    layout: MainLayout,
    allowedRoles: ["admin"]
  },
  {
    path: "/profile",
    element: <Profile />,
    layout: MainLayout,
    allowedRoles: ["admin", "staff"]
  }
];

// Client Portal Routes - Restricted to only allowed pages
export const clientPortalRoutes: RouteConfig[] = [
  {
    path: "/orders",
    element: <ClientOrders />,
    layout: ClientPortalLayout,
    allowedRoles: ["client"]
  },
  {
    path: "/items",  // Client service items route
    element: <ClientItems />,
    layout: ClientPortalLayout,
    allowedRoles: ["client"]
  },
  {
    path: "/reports", // New client reports route
    element: <ClientReports />,
    layout: ClientPortalLayout,
    allowedRoles: ["client"]
  },
  {
    path: "/profile",
    element: <Profile />,
    layout: ClientPortalLayout,
    allowedRoles: ["client"]
  }
];

export function getDefaultRouteForRole(role: UserRole): string {
  switch (role) {
    case "admin":
    case "staff":
      return "/dashboard";
    case "client":
      return "/orders";  // Default route for clients is /orders
    default:
      return "/login";
  }
}
