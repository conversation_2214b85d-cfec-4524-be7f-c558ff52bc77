
import { useState } from "react";
import { Order } from "@/types";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { Loader2, ArrowRight } from "lucide-react";
import { useAuth } from "@/contexts/auth";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";

interface OrderStatusWorkflowProps {
  order: Order;
  onStatusChange: (orderId: string, newStatus: string) => Promise<void>;
}

export function OrderStatusWorkflow({ order, onStatusChange }: OrderStatusWorkflowProps) {
  const [isUpdating, setIsUpdating] = useState(false);
  const [updatingToStatus, setUpdatingToStatus] = useState<string | null>(null);
  const { userRole } = useAuth();
  
  // Only allow status changes for staff and admin
  const canChangeStatus = userRole === 'admin' || userRole === 'staff';

  // Define available status transitions based on order type and current status
  const getNextStatuses = (currentStatus: string, isClientOrder: boolean): string[] => {
    // Common statuses for both client and walk-in orders
    const commonStatuses = ['processing', 'fulfilled', 'cancelled'];
    
    // Client order statuses
    if (isClientOrder) {
      return [
        ...commonStatuses,
        'ready_for_pickup',
        'for_pickup',
        'pickup_complete',
        'for_treatment',
        'hard_stain',
        'partial_delivery'
      ];
    } 
    // Walk-in order statuses
    else {
      return [
        ...commonStatuses, 
        'for_treatment',
        'hard_stain'
      ];
    }
  };

  const handleStatusChange = async (newStatus: string) => {
    if (!newStatus) return;
    
    setIsUpdating(true);
    setUpdatingToStatus(newStatus);
    
    try {
      await onStatusChange(order.id, newStatus);
    } catch (error) {
      console.error("Error updating order status:", error);
    } finally {
      setIsUpdating(false);
      setUpdatingToStatus(null);
    }
  };

  const isClientOrder = order.customerType === 'client';
  const currentStatus = order.status || 'pending';
  const availableStatuses = getNextStatuses(currentStatus, isClientOrder);
  
  // Get status display name for UI
  const getStatusDisplayName = (status: string) => {
    switch (status) {
      case 'ready_for_pickup': return 'Ready For Pickup';
      case 'for_pickup': return 'For Pickup';
      case 'pickup_complete': return 'Pickup Complete';
      case 'processing': return 'Order Processing';
      case 'for_treatment': return 'For Treatment';
      case 'hard_stain': return 'With Hard Stain';
      case 'partial_delivery': return 'Partial Delivery';
      case 'fulfilled': return 'Order Fulfilled';
      case 'cancelled': return 'Cancelled';
      case 'pending': return 'Pending';
      default: return status.charAt(0).toUpperCase() + status.slice(1).replace(/_/g, ' ');
    }
  };

  // Get badge color based on status
  const getStatusBadgeStyle = (status: string) => {
    switch (status) {
      case 'ready_for_pickup':
        return "bg-green-100 text-green-800 border-green-300";
      case 'for_pickup':
        return "bg-blue-100 text-blue-800 border-blue-300";
      case 'pickup_complete':
        return "bg-green-100 text-green-800 border-green-300";
      case 'processing':
        return "bg-indigo-100 text-indigo-800 border-indigo-300";
      case 'for_treatment':
        return "bg-orange-100 text-orange-800 border-orange-300";
      case 'hard_stain':
        return "bg-red-100 text-red-800 border-red-300";
      case 'partial_delivery':
        return "bg-amber-100 text-amber-800 border-amber-300";
      case 'fulfilled':
        return "bg-purple-100 text-purple-800 border-purple-300";
      case 'cancelled':
        return "bg-gray-100 text-gray-800 border-gray-300";
      default:
        return "bg-gray-100 text-gray-800 border-gray-300";
    }
  };

  return (
    <div className="space-y-4">
      <h3 className="text-sm font-medium">Order Status</h3>
      
      {/* Current Status Display */}
      <div className="flex items-center gap-2">
        <span className="text-sm">Current Status:</span>
        <Badge className={cn("uppercase px-2 py-1", getStatusBadgeStyle(currentStatus))}>
          {getStatusDisplayName(currentStatus)}
        </Badge>
      </div>

      {/* Status Change Controls - Only visible to staff and admin */}
      {canChangeStatus && (
        <div className="space-y-2">
          <label className="text-sm font-medium">Change Status:</label>
          <div className="flex items-center gap-2">
            <Select
              onValueChange={handleStatusChange}
              disabled={isUpdating}
              value={currentStatus}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select new status" />
              </SelectTrigger>
              <SelectContent>
                {availableStatuses.map((status) => (
                  <SelectItem key={status} value={status}>
                    {getStatusDisplayName(status)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            {isUpdating && (
              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                <Loader2 className="h-3 w-3 animate-spin" />
                <span>Updating...</span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* For non-staff/admin users */}
      {!canChangeStatus && (
        <div className="text-sm text-muted-foreground italic">
          Only staff and admin can change the order status
        </div>
      )}
    </div>
  );
}
