
import { Order } from "@/types";
import { useAuth } from "@/contexts/auth";
import { StatusDisplay } from "./StatusDisplay";
import { StatusSelector } from "./StatusSelector";
import { NonStaffMessage } from "./NonStaffMessage";
import { useStatusWorkflow } from "./useStatusWorkflow";
import { getNextStatuses } from "./utils/statusUtils";

interface OrderStatusWorkflowProps {
  order: Order;
  onStatusChange: (orderId: string, newStatus: string) => Promise<void>;
}

export function OrderStatusWorkflow({ order, onStatusChange }: OrderStatusWorkflowProps) {
  const { userRole } = useAuth();
  const { isUpdating, handleStatusChange } = useStatusWorkflow({ order, onStatusChange });
  
  // Only allow status changes for staff and admin
  const canChangeStatus = userRole === 'admin' || userRole === 'staff';

  const isClientOrder = order.customerType === 'client';
  const currentStatus = order.status || 'pending';
  const availableStatuses = getNextStatuses(currentStatus, isClientOrder);
  
  return (
    <div className="space-y-4">
      <h3 className="text-sm font-medium">Order Status</h3>
      
      {/* Current Status Display */}
      <StatusDisplay currentStatus={currentStatus} />

      {/* Status Change Controls - Only visible to staff and admin */}
      {canChangeStatus && (
        <StatusSelector
          currentStatus={currentStatus}
          availableStatuses={availableStatuses}
          isUpdating={isUpdating}
          onStatusChange={handleStatusChange}
        />
      )}

      {/* For non-staff/admin users */}
      {!canChangeStatus && <NonStaffMessage />}
    </div>
  );
}
