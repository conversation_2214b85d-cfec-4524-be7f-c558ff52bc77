
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { DialogFooter } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { ItemFormValues, itemFormSchema } from "./types";
import { ClientItem } from "@/services/clientItemService";
import { useToast } from "@/hooks/use-toast";

interface AddClientItemFormProps {
  onSubmit: (data: ItemFormValues) => void;
  onCancel: () => void;
  isSubmitting: boolean;
  existingItems?: ClientItem[];
}

export function AddClientItemForm({ 
  onSubmit, 
  onCancel, 
  isSubmitting,
  existingItems = [] 
}: AddClientItemFormProps) {
  const { toast } = useToast();
  const [nameError, setNameError] = useState("");

  const form = useForm<ItemFormValues>({
    resolver: zodResolver(itemFormSchema),
    defaultValues: {
      name: "",
      unitPrice: 100 // Default price
    }
  });

  const validateItemName = (name: string): boolean => {
    if (!name) return true;
    
    const normalizedName = name.trim().toLowerCase();
    const isDuplicate = existingItems.some(
      item => item.name.toLowerCase() === normalizedName
    );
    
    if (isDuplicate) {
      setNameError(`"${name}" already exists in the items list`);
      return false;
    }
    
    setNameError("");
    return true;
  };

  const handleSubmit = (data: ItemFormValues) => {
    if (!validateItemName(data.name)) {
      return;
    }
    onSubmit(data);
  };

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newName = e.target.value;
    form.setValue("name", newName);
    validateItemName(newName);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4 pt-2">
        {/* Item Name Field */}
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Item Name</FormLabel>
              <FormControl>
                <Input 
                  placeholder="Enter item name" 
                  {...field} 
                  onChange={handleNameChange}
                  className={nameError ? "border-red-500" : ""}
                />
              </FormControl>
              {nameError && <p className="text-sm text-red-500 mt-1">{nameError}</p>}
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Unit Price Field */}
        <FormField
          control={form.control}
          name="unitPrice"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Price (₱)</FormLabel>
              <FormControl>
                <Input 
                  type="number" 
                  placeholder="100.00" 
                  {...field} 
                  onChange={e => form.setValue("unitPrice", parseFloat(e.target.value) || 0)}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <DialogFooter className="pt-4">
          <Button 
            type="button" 
            variant="outline" 
            onClick={onCancel}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button 
            type="submit" 
            disabled={isSubmitting || !!nameError}
          >
            {isSubmitting ? "Adding..." : "Add Item"}
          </Button>
        </DialogFooter>
      </form>
    </Form>
  );
}
