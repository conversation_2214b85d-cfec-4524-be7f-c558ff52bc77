
import React from "react";
import { AndroidDatePicker } from "@/components/ui/android-date-picker";

interface DatePickerProps {
  date: Date | undefined;
  onSelect: (date: Date | undefined) => void;
}

export const DatePicker: React.FC<DatePickerProps> = ({ date, onSelect }) => {
  const handleDateSelect = (selectedDate: Date) => {
    console.log("Filter DatePicker: Date selected:", selectedDate);
    onSelect(selectedDate);
  };

  return (
    <div className="space-y-2">
      <label className="text-sm">Date</label>
      <AndroidDatePicker
        value={date}
        onChange={handleDateSelect}
        placeholder="Pick a date"
        className="w-full"
      />
    </div>
  );
};
