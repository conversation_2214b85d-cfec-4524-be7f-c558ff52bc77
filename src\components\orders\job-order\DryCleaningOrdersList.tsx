
import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Shirt, FileText } from "lucide-react";
import { EmptyState } from "@/components/ui/empty-state";
import { OrderStatusBadge } from "../OrderStatusBadge";
import { Order } from "@/types";
import { format } from "date-fns";
import { useOrder } from "@/contexts/OrderContext";
import { JobOrderProcessDialog } from "./JobOrderProcessDialog";

interface DryCleaningOrdersListProps {
  orders: Order[];
  searchTerm: string;
  statusFilter: string;
}

export function DryCleaningOrdersList({ orders, searchTerm, statusFilter }: DryCleaningOrdersListProps) {
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [isProcessDialogOpen, setIsProcessDialogOpen] = useState(false);
  const { dispatch } = useOrder();
  
  const handleProcessOrder = (order: Order) => {
    setSelectedOrder(order);
    dispatch({ type: 'SET_ORDER', payload: order });
    setIsProcessDialogOpen(true);
  };
  
  // Add a proper status change handler that returns a Promise
  const handleOrderUpdated = async (): Promise<void> => {
    console.log('Order updated, refreshing data');
    return Promise.resolve();
  };
  
  if (orders.length === 0) {
    return (
      <EmptyState
        icon={<Shirt className="h-12 w-12 text-muted-foreground" />}
        title="No dry cleaning orders found"
        description={
          searchTerm || statusFilter !== 'all'
            ? "Try adjusting your filters to see more orders."
            : "There are no dry cleaning orders to process at the moment."
        }
      />
    );
  }

  return (
    <div className="space-y-4">
      {orders.map((order) => (
        <Card key={order.id} className="overflow-hidden">
          <CardContent className="p-6">
            <div className="space-y-2">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="text-lg font-semibold">
                    {order.reference_code || order.id.substring(0, 8)}
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    Client: {order.customer.name}
                  </p>
                </div>
                <OrderStatusBadge status={order.status} />
              </div>
              
              <div className="grid grid-cols-2 gap-2 text-sm text-muted-foreground">
                <p>Order Date: {format(new Date(order.orderDate), 'MMM d, yyyy')}</p>
                <p>Items: {order.lineItems.length}</p>
                {order.notes && <p className="col-span-2">Notes: {order.notes}</p>}
                <p className="col-span-2 font-medium text-amber-600">
                  Service: Dry Cleaning
                </p>
              </div>
              
              <div className="flex justify-between items-center pt-4">
                <div className="text-sm">
                  <p className="font-medium">
                    Amount: {new Intl.NumberFormat('en-US', {
                      style: 'currency',
                      currency: 'PHP'
                    }).format(order.amount)}
                  </p>
                </div>
                
                <Button size="sm" onClick={() => handleProcessOrder(order)}>
                  <FileText className="h-4 w-4 mr-2" />
                  Process Order
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
      
      {selectedOrder && (
        <JobOrderProcessDialog
          order={selectedOrder}
          open={isProcessDialogOpen}
          onOpenChange={setIsProcessDialogOpen}
          onOrderUpdated={handleOrderUpdated}
        />
      )}
    </div>
  );
}
