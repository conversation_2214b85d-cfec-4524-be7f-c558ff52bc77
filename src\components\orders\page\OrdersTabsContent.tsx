
import React from "react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Order } from "@/types";
import { OrdersLoadingState } from "@/components/orders/OrdersLoadingState";
import { OrdersErrorState } from "@/components/orders/OrdersErrorState";
import { SimpleClientOrdersTable } from "@/components/orders/SimpleClientOrdersTable";
import { SimpleWalkInOrdersTable } from "@/components/orders/SimpleWalkInOrdersTable";
import { OrderFilters } from "@/components/orders/filters/OrderFilters";

interface OrdersTabsContentProps {
  isLoading: boolean;
  error: string | null;
  refreshOrders: () => Promise<void>;
  activeTab: string;
  setActiveTab: (tab: string) => void;
  clientOrders: Order[];
  walkInOrders: Order[];
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  statusFilter: string;
  setStatusFilter: (status: string) => void;
  dateFilter: Date | undefined;
  setDateFilter: (date: Date | undefined) => void;
  clientFilter: string;
  setClientFilter: (clientId: string) => void;
  clientOptions: Array<{ id: string; name: string }>;
  onViewOrder: (order: Order) => void;
  onOrderDeleted?: () => void;
}

export function OrdersTabsContent({
  isLoading,
  error,
  refreshOrders,
  activeTab,
  setActiveTab,
  clientOrders,
  walkInOrders,
  searchTerm,
  setSearchTerm,
  statusFilter,
  setStatusFilter,
  dateFilter,
  setDateFilter,
  clientFilter,
  setClientFilter,
  clientOptions,
  onViewOrder,
  onOrderDeleted
}: OrdersTabsContentProps) {
  
  console.log(`OrdersTabsContent: Showing ${clientOrders.length} client orders and ${walkInOrders.length} walk-in orders`);
  
  if (isLoading) {
    return <OrdersLoadingState />;
  }
  
  if (error) {
    return <OrdersErrorState onRetry={refreshOrders} message={error} />;
  }
  
  return (
    <>
      <div className="mb-6">
        <OrderFilters
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          statusFilter={statusFilter}
          setStatusFilter={setStatusFilter}
          dateFilter={dateFilter}
          setDateFilter={setDateFilter}
          clientFilter={activeTab === "clients" ? clientFilter : undefined}
          setClientFilter={activeTab === "clients" ? setClientFilter : undefined}
          clientOptions={clientOptions}
          isClientView={activeTab === "clients"}
        />
      </div>
      
      <Tabs defaultValue="clients" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="bg-muted/40 w-full md:w-auto">
          <TabsTrigger value="clients">Client Orders ({clientOrders.length})</TabsTrigger>
          <TabsTrigger value="customers">Walk-in Orders ({walkInOrders.length})</TabsTrigger>
        </TabsList>

        <TabsContent value="clients" className="mt-0">
          <SimpleClientOrdersTable 
            orders={clientOrders} 
            onViewOrder={onViewOrder} 
            onOrderDeleted={onOrderDeleted} 
          />
        </TabsContent>

        <TabsContent value="customers" className="mt-0">
          <SimpleWalkInOrdersTable 
            orders={walkInOrders} 
            onViewOrder={onViewOrder} 
            onOrderDeleted={onOrderDeleted} 
          />
        </TabsContent>
      </Tabs>
    </>
  );
}
