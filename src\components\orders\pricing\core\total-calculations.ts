
import { calculateAddOnPrice } from "./add-on-pricing";
import { AddOnSelections, ClientItemWithQuantity, DryCleaningItem, ServiceType } from "../../OrderFormTypes";
import { calculateItemsPrice, calculateDryCleaningItemsPrice } from "./item-pricing";
import { SERVICE_TYPES } from "../constants";
import { calculateWeightPrice } from "./weight-pricing";

const VAT_RATE = 0.12; // 12% VAT

interface PriceCalculationParams {
  weightKilos: number;
  pricingMethod: "weight" | "client_item" | "dry_cleaning";
  serviceType: ServiceType;
  addOns: AddOnSelections;
  clientItems?: ClientItemWithQuantity[];
  dryCleaningItems?: DryCleaningItem[];
}

export async function calculateEnhancedPrice({
  weightKilos,
  pricingMethod,
  serviceType,
  addOns,
  clientItems = [],
  dryCleaningItems = []
}: PriceCalculationParams) {
  let basePrice = 0;
  
  // Calculate base price according to pricing method
  if (pricingMethod === "weight" || !pricingMethod) {
    basePrice = await calculateWeightPrice(weightKilos, serviceType);
  } else if (pricingMethod === "client_item") {
    basePrice = await calculateItemsPrice(clientItems);
  } else if (pricingMethod === "dry_cleaning" || serviceType === SERVICE_TYPES.DRY_CLEANING) {
    basePrice = await calculateDryCleaningItemsPrice(dryCleaningItems);
  }

  // Calculate add-on prices (detergents, etc.)
  const addOnPrice = await calculateAddOnPrice(weightKilos, addOns);
  
  // Calculate subtotal
  const subtotal = basePrice + addOnPrice;
  
  // Calculate VAT amount
  const vatAmount = subtotal * VAT_RATE;
  
  // Calculate total price including VAT
  const totalPrice = subtotal + vatAmount;
  
  return {
    basePrice,
    addOnPrice,
    subtotal,
    vatAmount,
    totalPrice
  };
}
