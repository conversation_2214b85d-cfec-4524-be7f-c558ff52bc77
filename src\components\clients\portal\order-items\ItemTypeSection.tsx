
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { ItemTreatments } from "./ItemTreatmentOptions";
import { ClientOrderItem } from "../order-form/types";
import { ItemInstanceList } from "./ItemInstanceList";

interface ItemTypeSectionProps {
  type: string;
  typeItems: { id: string; name: string; item_type: string; unit_price: number }[];
  selectedItems: ClientOrderItem[];
  expandedItems: Record<string, boolean>;
  onAddItem: (item: { id: string; name: string; item_type: string; unit_price: number }) => void;
  onRemoveItem: (instanceId: string) => void;
  onDuplicateItem: (instanceId: string) => void;
  onToggleExpand: (instanceId: string) => void;
  onQuantityChange: (instanceId: string, newQuantity: number) => void;
  onTreatmentChange: (instanceId: string, treatments: ItemTreatments) => void;
}

export function ItemTypeSection({
  type,
  typeItems,
  selectedItems,
  expandedItems,
  onAddItem,
  onRemoveItem,
  onDuplicateItem,
  onToggleExpand,
  onQuantityChange,
  onTreatmentChange
}: ItemTypeSectionProps) {
  // Get all instances of items of this type
  const typeInstances = selectedItems.filter(
    item => typeItems.some(typeItem => typeItem.id === item.id)
  );

  // Format type name for display
  const formattedType = type
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');

  return (
    <div>
      <div className="mb-2 font-medium">{formattedType}</div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
        {typeItems.map((item) => (
          <button
            key={item.id}
            type="button"
            className="flex justify-between items-center h-auto py-3 px-4 border border-gray-300 rounded-md bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary touch-action-manipulation active:scale-[0.98] transition-all duration-150"
            onClick={() => {
              console.log("ItemTypeSection: Adding item:", item);
              onAddItem(item);
            }}
            onTouchEnd={() => {
              console.log("ItemTypeSection: Touch end for item:", item);
              onAddItem(item);
            }}
          >
            <span className="text-left mr-2 font-medium">{item.name}</span>
            <div className="flex items-center">
              <span className="text-muted-foreground text-sm mr-2">
                ₱{item.unit_price.toFixed(2)}
              </span>
              <Plus className="h-5 w-5" />
            </div>
          </button>
        ))}
      </div>

      {typeInstances.length > 0 && (
        <div className="mt-2 border p-3 rounded-md bg-slate-50">
          <h4 className="text-sm font-semibold mb-2">Added {formattedType} Items:</h4>
          <ItemInstanceList
            items={typeInstances}
            type={type}
            expandedItems={expandedItems}
            onRemoveItem={onRemoveItem}
            onDuplicateItem={onDuplicateItem}
            onToggleExpand={onToggleExpand}
            onQuantityChange={onQuantityChange}
            onTreatmentChange={onTreatmentChange}
          />
        </div>
      )}
    </div>
  );
}
