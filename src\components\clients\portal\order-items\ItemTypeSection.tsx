
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { ItemTreatments } from "./ItemTreatmentOptions";
import { ClientOrderItem } from "../order-form/types";
import { ItemInstanceList } from "./ItemInstanceList";

interface ItemTypeSectionProps {
  type: string;
  typeItems: { id: string; name: string; item_type: string; unit_price: number }[];
  selectedItems: ClientOrderItem[];
  expandedItems: Record<string, boolean>;
  onAddItem: (item: { id: string; name: string; item_type: string; unit_price: number }) => void;
  onRemoveItem: (instanceId: string) => void;
  onDuplicateItem: (instanceId: string) => void;
  onToggleExpand: (instanceId: string) => void;
  onQuantityChange: (instanceId: string, newQuantity: number) => void;
  onTreatmentChange: (instanceId: string, treatments: ItemTreatments) => void;
}

export function ItemTypeSection({
  type,
  typeItems,
  selectedItems,
  expandedItems,
  onAddItem,
  onRemoveItem,
  onDuplicateItem,
  onToggleExpand,
  onQuantityChange,
  onTreatmentChange
}: ItemTypeSectionProps) {
  // Get all instances of items of this type
  const typeInstances = selectedItems.filter(
    item => typeItems.some(typeItem => typeItem.id === item.id)
  );

  // Format type name for display
  const formattedType = type
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');

  return (
    <div>
      <div className="mb-2 font-medium">{formattedType}</div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
        {typeItems.map((item) => (
          <Button
            key={item.id}
            variant="outline"
            className="justify-between h-auto py-2"
            onClick={() => onAddItem(item)}
          >
            <span className="text-left mr-2">{item.name}</span>
            <div className="flex items-center">
              <span className="text-muted-foreground text-xs mr-2">
                ₱{item.unit_price.toFixed(2)}
              </span>
              <Plus className="h-4 w-4" />
            </div>
          </Button>
        ))}
      </div>
      
      {typeInstances.length > 0 && (
        <div className="mt-2 border p-3 rounded-md bg-slate-50">
          <h4 className="text-sm font-semibold mb-2">Added {formattedType} Items:</h4>
          <ItemInstanceList
            items={typeInstances}
            type={type}
            expandedItems={expandedItems}
            onRemoveItem={onRemoveItem}
            onDuplicateItem={onDuplicateItem}
            onToggleExpand={onToggleExpand}
            onQuantityChange={onQuantityChange}
            onTreatmentChange={onTreatmentChange}
          />
        </div>
      )}
    </div>
  );
}
