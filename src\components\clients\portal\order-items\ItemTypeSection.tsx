
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Plus, ChevronDown, ChevronUp } from "lucide-react";
import { useState } from "react";
import { ItemInstanceList } from "./ItemInstanceList";
import { ItemTreatments } from "./ItemTreatmentOptions";
import { NoItemsMessage } from "./NoItemsMessage";
import { formatCurrency } from "@/lib/utils";
import { ClientOrderItem } from "../order-form/types";

interface ItemTypeSectionProps {
  type: string;
  typeItems: { id: string; name: string; item_type: string; unit_price?: number }[];
  selectedItems: ClientOrderItem[];
  expandedItems: Record<string, boolean>;
  onAddItem: (item: { id: string; name: string; item_type: string; unit_price?: number }) => void;
  onRemoveItem: (instanceId: string) => void;
  onDuplicateItem: (instanceId: string) => void;
  onToggleExpand: (instanceId: string) => void;
  onQuantityChange: (instanceId: string, quantity: number) => void;
  onTreatmentChange: (instanceId: string, treatments: ItemTreatments) => void;
}

export function ItemTypeSection({
  type,
  typeItems,
  selectedItems,
  expandedItems,
  onAddItem,
  onRemoveItem,
  onDuplicateItem,
  onToggleExpand,
  onQuantityChange,
  onTreatmentChange
}: ItemTypeSectionProps) {
  const [isExpanded, setIsExpanded] = useState(true);
  
  // Get all instances that belong to this section's item types
  const sectionInstances = selectedItems.filter(item => 
    typeItems.some(typeItem => typeItem.id === item.id)
  );
  
  const hasSelectedItems = sectionInstances.length > 0;
  
  // Format the type name for display
  const formattedType = type === "standard" 
    ? "Standard Items" 
    : type.charAt(0).toUpperCase() + type.slice(1);
  
  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <Button 
          type="button"
          variant="ghost" 
          className="font-medium p-0 h-auto hover:bg-transparent hover:underline"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          {formattedType}
          {isExpanded ? (
            <ChevronUp className="ml-1 h-4 w-4" />
          ) : (
            <ChevronDown className="ml-1 h-4 w-4" />
          )}
        </Button>
        
        {hasSelectedItems && (
          <Badge variant="secondary" className="font-normal">
            {sectionInstances.length} {sectionInstances.length === 1 ? 'item' : 'items'} selected
          </Badge>
        )}
      </div>
      
      {isExpanded && (
        <Card>
          <CardContent className="p-3">
            {typeItems.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
                {typeItems.map(item => {
                  // Check if this item is already in the selected items
                  const isSelected = selectedItems.some(
                    selectedItem => selectedItem.id === item.id
                  );
                  
                  return (
                    <div key={item.id} className="min-w-0">
                      <Button
                        type="button"
                        variant={isSelected ? "secondary" : "outline"}
                        size="sm"
                        className="w-full justify-between text-left font-normal h-auto py-1.5 px-3"
                        onClick={() => onAddItem(item)}
                      >
                        <span className="truncate">{item.name}</span>
                        {item.unit_price !== undefined && (
                          <span className="ml-2 whitespace-nowrap text-xs">
                            {formatCurrency(item.unit_price)}
                          </span>
                        )}
                        <Plus className="ml-2 h-3.5 w-3.5 shrink-0" />
                      </Button>
                    </div>
                  );
                })}
              </div>
            ) : (
              <NoItemsMessage />
            )}
            
            {/* Selected items from this section */}
            {hasSelectedItems && (
              <div className="mt-4 border-t pt-3">
                <ItemInstanceList
                  items={sectionInstances}
                  expandedItems={expandedItems}
                  onRemove={onRemoveItem}
                  onDuplicate={onDuplicateItem}
                  onToggleExpand={onToggleExpand}
                  onQuantityChange={onQuantityChange}
                  onTreatmentChange={onTreatmentChange}
                />
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
