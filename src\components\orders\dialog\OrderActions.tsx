
import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Trash2, Edit } from "lucide-react";
import { Order } from "@/types";

interface OrderActionsProps {
  order: Order;
  canEdit: boolean;
  isAdmin: boolean;
  onEdit: () => void;
  onDelete: () => void;
}

export function OrderActions({ 
  order, 
  canEdit, 
  isAdmin, 
  onEdit, 
  onDelete 
}: OrderActionsProps) {
  return (
    <div className="flex space-x-2">
      {/* Edit button - only for admin/staff users */}
      {canEdit && (
        <Button
          variant="outline"
          size="icon"
          onClick={onEdit}
          className="text-blue-500 hover:text-blue-700 hover:bg-blue-100"
        >
          <Edit className="h-4 w-4" />
        </Button>
      )}
      
      {/* Delete button - only for admin users */}
      {isAdmin && (
        <Button
          variant="outline"
          size="icon"
          onClick={onDelete}
          className="text-red-500 hover:text-red-700 hover:bg-red-100"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      )}
    </div>
  );
}
