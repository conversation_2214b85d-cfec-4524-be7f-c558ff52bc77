
import React from "react";
import { Button } from "@/components/ui/button";
import { Trash2, X } from "lucide-react";
import { Order } from "@/types";

interface OrderActionsProps {
  order: Order;
  canEdit: boolean;
  isAdmin: boolean;
  onEdit: () => void;
  onDelete: () => void;
  onClose?: () => void;
}

export function OrderActions({
  order,
  canEdit,
  isAdmin,
  onEdit,
  onDelete,
  onClose
}: OrderActionsProps) {
  return (
    <div className="flex space-x-2">
      {/* Close button (if provided) */}
      {onClose && (
        <Button variant="outline" size="icon" onClick={onClose} className="rounded-full">
          <X className="h-4 w-4" />
        </Button>
      )}
      
      {/* Delete button - only for admin users */}
      {isAdmin && (
        <Button 
          variant="outline" 
          size="icon" 
          onClick={onDelete} 
          className="text-red-500 hover:text-red-700 hover:bg-red-100"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      )}
    </div>
  );
}
