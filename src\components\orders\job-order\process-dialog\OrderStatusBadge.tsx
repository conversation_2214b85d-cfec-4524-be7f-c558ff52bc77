
interface OrderStatusBadgeProps {
  status: string;
  className?: string;
}

export function OrderStatusBadge({ status, className = "" }: OrderStatusBadgeProps) {
  let badgeClass = "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium " + className;
  
  switch (status) {
    case "processing":
      badgeClass += " bg-blue-100 text-blue-800";
      break;
    case "ready_for_pickup":
      badgeClass += " bg-green-100 text-green-800";
      break;
    case "fulfilled":
      badgeClass += " bg-purple-100 text-purple-800";
      break;
    case "cancelled":
      badgeClass += " bg-red-100 text-red-800";
      break;
    default:
      badgeClass += " bg-gray-100 text-gray-800";
  }
  
  // Update the status display name
  const displayStatus = status === "fulfilled" ? "delivery complete" : status?.replace(/_/g, " ");
  
  return (
    <span className={badgeClass}>
      {displayStatus}
    </span>
  );
}
