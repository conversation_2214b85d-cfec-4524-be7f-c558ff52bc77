
import { FormField, FormItem, FormLabel, FormControl } from "@/components/ui/form";
import { UseFormReturn } from "react-hook-form";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { useState, useEffect } from "react";

interface DetergentSectionProps {
  form: UseFormReturn<any>;
}

export function DetergentSection({ form }: DetergentSectionProps) {
  const [showQuantity, setShowQuantity] = useState(form.watch("detergentType") !== "none");
  
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === "detergentType") {
        setShowQuantity(value.detergentType !== "none");
      }
    });
    return () => subscription.unsubscribe();
  }, [form]);
  
  return (
    <div className="space-y-3">
      <h3 className="font-medium">Detergent</h3>
      
      <FormField
        control={form.control}
        name="detergentType"
        render={({ field }) => (
          <FormItem className="space-y-2">
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                value={field.value}
                className="grid grid-cols-3 gap-2"
              >
                <div className="flex items-center space-x-2 rounded-md border p-2">
                  <RadioGroupItem value="none" id="detergent-none" />
                  <Label htmlFor="detergent-none" className="flex-1 cursor-pointer">None</Label>
                </div>
                <div className="flex items-center space-x-2 rounded-md border p-2">
                  <RadioGroupItem value="regular" id="detergent-regular" />
                  <Label htmlFor="detergent-regular" className="flex-1 cursor-pointer">Regular</Label>
                </div>
                <div className="flex items-center space-x-2 rounded-md border p-2">
                  <RadioGroupItem value="color" id="detergent-color" />
                  <Label htmlFor="detergent-color" className="flex-1 cursor-pointer">Color Safe</Label>
                </div>
              </RadioGroup>
            </FormControl>
          </FormItem>
        )}
      />
      
      {showQuantity && (
        <FormField
          control={form.control}
          name="detergentQuantity"
          render={({ field }) => (
            <FormItem className="space-y-1">
              <FormLabel>Quantity</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  min="1"
                  max="10"
                  {...field}
                  className="w-24"
                />
              </FormControl>
            </FormItem>
          )}
        />
      )}
    </div>
  );
}
