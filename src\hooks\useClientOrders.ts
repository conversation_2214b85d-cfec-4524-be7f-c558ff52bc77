
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useClientData } from './orders/useClientData';
import { useClientOrdersFetch } from './orders/useClientOrdersFetch';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/auth';
import { ensureClientAssociation } from '@/services/clients/helpers/ensureClientAssociation';

export const useClientOrders = () => {
  const { isLoading: isLoadingClient, clientId: loadedClientId, error: clientError } = useClientData();
  const [clientId, setClientId] = useState<string | null>(null);
  const { orders, fetchOrders, isLoading: isLoadingOrders } = useClientOrdersFetch();
  const { user } = useAuth();
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Set client ID when it's loaded from useClientData
    if (loadedClientId) {
      setClientId(loadedClientId);
      setError(null);
    } else if (clientError) {
      setError(clientError);
    }
  }, [loadedClientId, clientError]);

  // Ensure client association and fetch orders once we have the clientId
  useEffect(() => {
    const setupClientAndFetchOrders = async () => {
      if (isProcessing) return;
      
      try {
        setIsProcessing(true);
        setError(null);
        
        // If we don't have a client ID yet and we have a user, try to ensure the association
        if (!clientId && user) {
          console.log("No client ID available, attempting to ensure association");
          const associatedClientId = await ensureClientAssociation(user);
          
          if (associatedClientId) {
            console.log("Successfully ensured client association:", associatedClientId);
            setClientId(associatedClientId);
          } else if (user.email === '<EMAIL>' || user.email === '<EMAIL>') {
            // Special handling for specific emails - direct lookup
            const { data: clientData, error: lookupError } = await supabase
              .from('clients')
              .select('id')
              .ilike('email', user.email)
              .maybeSingle();
              
            if (lookupError) {
              console.error("Error looking up client:", lookupError);
              setError("Error finding client information");
            } else if (clientData?.id) {
              console.log("Found client by direct email lookup:", clientData.id);
              setClientId(clientData.id);
              
              // Associate user with client using edge function
              try {
                const { error: fnError } = await supabase.functions.invoke('associate-client-user', {
                  body: {
                    userId: user.id,
                    clientId: clientData.id
                  }
                });
                
                if (fnError) {
                  console.error("Error associating user with client:", fnError);
                }
              } catch (err) {
                console.error("Error calling associate-client-user function:", err);
              }
            } else {
              console.log("Could not find client even with direct email lookup");
              
              // Try domain matching as last resort
              const domainPart = user.email.split('@')[1]; // e.g., "bighotel.ph"
              const { data: domainMatches } = await supabase
                .from('clients')
                .select('id')
                .ilike('email', `%${domainPart}%`)
                .limit(1);
                
              if (domainMatches && domainMatches.length > 0) {
                console.log("Found client by domain match:", domainMatches[0].id);
                setClientId(domainMatches[0].id);
                
                // Associate user with client using edge function
                try {
                  const { error: fnError } = await supabase.functions.invoke('associate-client-user', {
                    body: {
                      userId: user.id,
                      clientId: domainMatches[0].id
                    }
                  });
                  
                  if (fnError) {
                    console.error("Error associating user with client:", fnError);
                  }
                } catch (err) {
                  console.error("Error calling associate-client-user function:", err);
                }
              } else {
                setError("Client information not found");
                toast("Client information not found", { 
                  description: "Please contact support for assistance"
                });
              }
            }
          }
        }
        
        // Fetch orders if we have a clientId
        if (clientId) {
          console.log("Fetching orders for client ID:", clientId);
          await fetchOrders(clientId);
          
          // Set up real-time subscription for orders
          const channel = supabase
            .channel('orders-changes')
            .on(
              'postgres_changes',
              {
                event: '*',
                schema: 'public',
                table: 'orders',
                filter: `client_id=eq.${clientId}`
              },
              () => {
                fetchOrders(clientId);
              }
            )
            .subscribe();

          return () => {
            supabase.removeChannel(channel);
          };
        }
      } catch (error: any) {
        console.error("Error in setupClientAndFetchOrders:", error);
        setError(error.message || "Error loading orders");
        toast("Error loading orders", {
          description: "Please try refreshing the page"
        });
      } finally {
        setIsProcessing(false);
      }
    };

    setupClientAndFetchOrders();
  }, [clientId, user, fetchOrders]);

  const handleOrderAdded = () => {
    if (clientId) {
      fetchOrders(clientId);
    }
  };

  const isLoading = isLoadingClient || isLoadingOrders || isProcessing;

  return {
    isLoading,
    orders,
    clientId,
    error,
    handleOrderAdded
  };
};
