
import { Order } from "@/types";
import { EditOrderDialog } from "../EditOrderDialog";
import { DeleteOrderDialog } from "../DeleteOrderDialog";
import { OrderContextProvider } from "@/contexts/OrderContext";

interface OrderDialogManagerProps {
  order: Order;
  canEdit: boolean;
  isEditDialogOpen: boolean;
  setIsEditDialogOpen: (open: boolean) => void;
  isDeleteDialogOpen: boolean;
  setIsDeleteDialogOpen: (open: boolean) => void;
  isEditItemsDialogOpen: boolean;
  setIsEditItemsDialogOpen: (open: boolean) => void;
  isEditAddOnsDialogOpen: boolean;
  setIsEditAddOnsDialogOpen: (open: boolean) => void;
  onOrderDeleted: () => void;
  onOrderUpdated: () => void;
}

export function OrderDialogManager({
  order,
  canEdit,
  isEditDialogOpen,
  setIsEditDialogOpen,
  isDeleteDialogOpen,
  setIsDeleteDialogOpen,
  onOrderDeleted,
  onOrderUpdated
}: OrderDialogManagerProps) {
  // Only render dialogs if order is available and user has edit permissions
  if (!order || !canEdit) return null;
  
  return (
    <OrderContextProvider initialOrder={order} onOrderRefreshed={onOrderUpdated}>
      {/* Edit Order Dialog */}
      <EditOrderDialog 
        order={order} 
        open={isEditDialogOpen} 
        onOpenChange={setIsEditDialogOpen}
        onOrderUpdated={onOrderUpdated}
      />
      
      {/* Delete Order Confirmation Dialog */}
      <DeleteOrderDialog 
        orderId={order.id}
        orderReference={order.id}
        open={isDeleteDialogOpen} 
        onOpenChange={setIsDeleteDialogOpen}
        onOrderDeleted={onOrderDeleted}
      />
    </OrderContextProvider>
  );
}
