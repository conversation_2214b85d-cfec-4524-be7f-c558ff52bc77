
import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/auth';
import { useToast } from '@/hooks/use-toast';
import { ClientRequestItemDialog } from '@/components/clients/portal/ClientRequestItemDialog';
import { ClientItemsList } from '@/components/clients/portal/ClientItemsList';
import { useClientData } from '@/hooks/orders/useClientData';

export default function ClientItems() {
  const [isRequestDialogOpen, setIsRequestDialogOpen] = useState(false);
  const { toast } = useToast();
  const { user } = useAuth();
  const { clientId, isLoading } = useClientData();
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const handleItemRequested = () => {
    // Increment refresh trigger to cause the ClientItemsList to refresh
    setRefreshTrigger(prev => prev + 1);
    
    toast({
      title: 'Item Requested',
      description: 'Your service item request has been submitted.',
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <h1 className="text-2xl font-bold tracking-tight">Service Items</h1>
        <Button onClick={() => setIsRequestDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" /> Request New Item
        </Button>
      </div>
      
      {isLoading ? (
        <div className="text-center py-8 border rounded-md bg-muted/20">
          <p className="text-muted-foreground">
            Loading client information...
          </p>
        </div>
      ) : clientId ? (
        <ClientItemsList 
          clientId={clientId} 
          key={`itemsList-${refreshTrigger}`} // Force refresh when items change
        />
      ) : (
        <div className="text-center py-8 border rounded-md bg-muted/20">
          <p className="text-muted-foreground">
            No client account found. Please contact support.
          </p>
        </div>
      )}
      
      <ClientRequestItemDialog
        open={isRequestDialogOpen}
        onOpenChange={setIsRequestDialogOpen}
        clientId={clientId}
        onRequestSubmitted={handleItemRequested}
      />
    </div>
  );
}
