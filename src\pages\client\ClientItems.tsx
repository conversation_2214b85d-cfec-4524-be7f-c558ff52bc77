
import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert';
import { Plus, AlertCircle, Loader2, RefreshCw } from 'lucide-react';
import { useAuth } from '@/contexts/auth';
import { useToast } from '@/hooks/use-toast';
import { ClientRequestItemDialog } from '@/components/clients/portal/ClientRequestItemDialog';
import { ClientItemsList } from '@/components/clients/portal/ClientItemsList';
import { useClientData } from '@/hooks/orders/useClientData';

export default function ClientItems() {
  const [isRequestDialogOpen, setIsRequestDialogOpen] = useState(false);
  const { toast } = useToast();
  const { user } = useAuth();
  const { clientId, isLoading, error } = useClientData();
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [retryCount, setRetryCount] = useState(0);

  // Automatic retry logic for client data loading
  useEffect(() => {
    if (error && retryCount < 2) {
      const timer = setTimeout(() => {
        console.log("Retrying client data fetch...");
        setRetryCount(prev => prev + 1);
        // Force refresh trigger to reload data
        setRefreshTrigger(prev => prev + 1);
      }, 2000);
      
      return () => clearTimeout(timer);
    }
  }, [error, retryCount]);

  const handleItemRequested = () => {
    // Increment refresh trigger to cause the ClientItemsList to refresh
    setRefreshTrigger(prev => prev + 1);
    
    toast({
      title: 'Item Requested',
      description: 'Your service item request has been submitted.',
    });
  };

  const handleRetry = () => {
    setRefreshTrigger(prev => prev + 1);
    setRetryCount(0);
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <h1 className="text-2xl font-bold tracking-tight">Service Items</h1>
        {clientId && (
          <Button onClick={() => setIsRequestDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" /> Request New Item
          </Button>
        )}
      </div>
      
      {isLoading ? (
        <div className="text-center py-12 border rounded-md bg-muted/20">
          <Loader2 className="h-8 w-8 mx-auto animate-spin text-primary mb-4" />
          <p className="text-muted-foreground">
            Loading client information...
          </p>
        </div>
      ) : error ? (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription className="flex flex-col gap-4">
            <p>{error}</p>
            <Button 
              variant="outline" 
              size="sm" 
              className="self-start flex items-center"
              onClick={handleRetry}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      ) : clientId ? (
        <ClientItemsList 
          clientId={clientId} 
          key={`itemsList-${refreshTrigger}`} // Force refresh when items change
        />
      ) : (
        <div className="text-center py-12 border rounded-md bg-muted/20">
          <AlertCircle className="h-8 w-8 mx-auto text-amber-500 mb-4" />
          <p className="text-muted-foreground mb-4">
            No client account found. Please contact support.
          </p>
          <p className="text-sm text-muted-foreground">
            If you believe this is an error, try logging out and back in.
          </p>
        </div>
      )}
      
      {clientId && (
        <ClientRequestItemDialog
          open={isRequestDialogOpen}
          onOpenChange={setIsRequestDialogOpen}
          clientId={clientId}
          onRequestSubmitted={handleItemRequested}
        />
      )}
    </div>
  );
}
