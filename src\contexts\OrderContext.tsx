import React, { createContext, useContext, useState, ReactNode, useReducer, useCallback } from 'react';
import { Order, DryCleaningItem } from '@/types';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/auth';

// Define possible action types for the reducer
type OrderAction = 
  | { type: 'SET_ORDER'; payload: Order }
  | { type: 'UPDATE_ITEMS'; payload: any[] }
  | { type: 'UPDATE_AMOUNT'; payload: number }
  | { type: 'RESET_ORDER' };

// Define the context type
interface OrderContextType {
  order: Order | null;
  refreshOrder: () => Promise<void>;
  userRole: string;
  dispatch: React.Dispatch<OrderAction>;
  updateOrder: (updatedOrder: Partial<Order>) => Promise<void>;
}

const OrderContext = createContext<OrderContextType | undefined>(undefined);

// Define the OrderProvider props
interface OrderContextProviderProps {
  children: ReactNode;
  initialOrder?: Order | null;
  onOrderRefreshed?: () => void;
}

// Reducer function to handle order state updates
function orderReducer(state: Order | null, action: OrderAction): Order | null {
  switch (action.type) {
    case 'SET_ORDER':
      return action.payload;
    case 'UPDATE_ITEMS':
      return state ? { ...state, lineItems: action.payload } : state;
    case 'UPDATE_AMOUNT':
      return state ? { ...state, amount: action.payload } : state;
    case 'RESET_ORDER':
      return null;
    default:
      return state;
  }
}

export const OrderContextProvider: React.FC<OrderContextProviderProps> = ({ 
  children, 
  initialOrder = null,
  onOrderRefreshed
}) => {
  const [order, dispatch] = useReducer(orderReducer, initialOrder);
  const { userRole } = useAuth();

  const refreshOrder = async () => {
    if (!order?.id) return;

    try {
      const orderId = order.uuid || order.id;
      const { data, error } = await supabase
        .from('orders')
        .select('*')
        .eq('id', orderId)
        .single();

      if (error) throw error;

      if (data) {
        // Transform the data to match the Order type
        const refreshedOrder: Order = {
          ...order,
          id: data.reference_code || data.id,
          uuid: data.id,
          clientId: data.client_id,
          orderDate: data.created_at || order.orderDate,
          deliveryDate: data.delivery_date || order.deliveryDate,
          customer: {
            name: data.customer_name || order.customer.name,
            phone: data.phone_number || order.customer.phone,
            contactPerson: data.contact_person || order.customer.contactPerson,
          },
          amount: typeof data.amount === 'string' ? parseFloat(data.amount) : data.amount || 0,
          paidAmount: typeof data.paid_amount === 'string' ? parseFloat(data.paid_amount) : data.paid_amount || 0,
          status: data.status || order.status,
          lineItems: order.lineItems || [],
          customerType: data.customer_type as any || order.customerType,
          weightKilos: data.weight_kilos || order.weightKilos,
          numberOfPieces: data.number_of_pieces || order.numberOfPieces,
          useDetergent: data.use_detergent || order.useDetergent,
          useFabricConditioner: data.use_conditioner || order.useFabricConditioner,
          useStainRemover: data.use_stain_remover || order.useStainRemover,
          useBleach: data.use_bleach || order.useBleach,
          detergentQuantity: parseFloat(data.detergent_quantity) || order.detergentQuantity,
          conditionerQuantity: parseFloat(data.conditioner_quantity) || order.conditionerQuantity,
          notes: data.notes || order.notes,
          isDryCleaning: data.is_dry_cleaning ?? order.isDryCleaning ?? false,
          dryCleaningItems: data.dry_cleaning_items ? 
                           (typeof data.dry_cleaning_items === 'string' ? 
                            JSON.parse(data.dry_cleaning_items) : 
                            data.dry_cleaning_items) : 
                           order.dryCleaningItems || [],
          // Safe handling of items - ensure it's properly converted to expected format
          items: Array.isArray(data.items) ? data.items : 
                 (typeof data.items === 'object' && data.items !== null) ? 
                 JSON.stringify(data.items) : order.items
        };
        
        // Use the reducer dispatch
        dispatch({ type: 'SET_ORDER', payload: refreshedOrder });
        
        if (onOrderRefreshed) onOrderRefreshed();
      }
    } catch (error) {
      console.error('Error refreshing order:', error);
    }
  };

  const updateOrder = async (updatedOrderData: Partial<Order>) => {
    if (!order?.id) return;

    try {
      // Convert camelCase to snake_case for database fields
      const dbData: Record<string, any> = {};
      
      // Map the fields that need to be transformed
      if (updatedOrderData.isDryCleaning !== undefined) {
        dbData.is_dry_cleaning = updatedOrderData.isDryCleaning;
      }
      
      if (updatedOrderData.dryCleaningItems !== undefined) {
        dbData.dry_cleaning_items = updatedOrderData.dryCleaningItems;
      }
      
      // Always include updated timestamp
      dbData.updated_at = new Date().toISOString();
      
      // Add any other fields directly with proper naming conversion
      Object.entries(updatedOrderData).forEach(([key, value]) => {
        if (key !== 'isDryCleaning' && key !== 'updated_at' && key !== 'dryCleaningItems') {
          // Convert camelCase to snake_case for database column names
          const snakeCaseKey = key.replace(/([A-Z])/g, '_$1').toLowerCase();
          dbData[snakeCaseKey] = value;
        }
      });
      
      const { error } = await supabase
        .from('orders')
        .update(dbData)
        .eq('id', order.uuid || order.id);

      if (error) throw error;
      
      // Refresh the order data after updating
      await refreshOrder();
    } catch (error) {
      console.error('Error updating order:', error);
      throw error;
    }
  };

  return (
    <OrderContext.Provider value={{ 
      order, 
      refreshOrder,
      userRole,
      dispatch,
      updateOrder
    }}>
      {children}
    </OrderContext.Provider>
  );
};

// Export as OrderProvider for compatibility with existing imports
export const OrderProvider = OrderContextProvider;

export const useOrder = () => {
  const context = useContext(OrderContext);
  if (context === undefined) {
    throw new Error('useOrder must be used within an OrderContextProvider');
  }
  return context;
};
