
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

// Type for notification
export interface Notification {
  id: string;
  user_id?: string;
  role: string;
  title: string;
  message: string;
  type: 'order' | 'inventory' | 'system';
  is_read: boolean;
  related_id?: string;
  created_at: string;
}

/**
 * Create a new notification
 */
export const createNotification = async (notification: Omit<Notification, 'id' | 'is_read' | 'created_at'>) => {
  try {
    // First check if user is authenticated
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      console.log("Cannot create notification: No active session");
      return null;
    }

    const { data, error } = await supabase
      .from('notifications')
      .insert({
        user_id: notification.user_id || session.user.id,
        role: notification.role,
        title: notification.title,
        message: notification.message,
        type: notification.type,
        related_id: notification.related_id
      })
      .select()
      .single();
      
    if (error) {
      if (error.code === '42501') {
        // This is an RLS policy error, log it but don't treat as fatal
        console.log("Note: Notification not created due to RLS policy. This is expected if you don't have the right permissions.");
        return null;
      } else {
        console.error("Error creating notification:", error);
        return null;
      }
    }
    
    return data;
  } catch (error) {
    console.error("Error in createNotification:", error);
    return null;
  }
};

/**
 * Get notifications for the current user
 */
export const getNotifications = async (limit: number = 10): Promise<Notification[]> => {
  try {
    // First check if user is authenticated
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return [];
    }
    
    const { data, error } = await supabase
      .from('notifications')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(limit);
      
    if (error) {
      console.error("Error fetching notifications:", error);
      return [];
    }
    
    return data || [];
  } catch (error) {
    console.error("Error in getNotifications:", error);
    return [];
  }
};

/**
 * Get unread notification count
 */
export const getUnreadCount = async (): Promise<number> => {
  try {
    // First check if user is authenticated
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return 0;
    }
    
    const { count, error } = await supabase
      .from('notifications')
      .select('*', { count: 'exact', head: true })
      .eq('is_read', false);
      
    if (error) {
      console.error("Error counting unread notifications:", error);
      return 0;
    }
    
    return count || 0;
  } catch (error) {
    console.error("Error in getUnreadCount:", error);
    return 0;
  }
};

/**
 * Mark a notification as read
 */
export const markAsRead = async (id: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('notifications')
      .update({ is_read: true })
      .eq('id', id);
      
    if (error) {
      console.error("Error marking notification as read:", error);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error("Error in markAsRead:", error);
    return false;
  }
};

/**
 * Mark all notifications as read
 */
export const markAllAsRead = async (): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('notifications')
      .update({ is_read: true })
      .eq('is_read', false);
      
    if (error) {
      console.error("Error marking all notifications as read:", error);
      return false;
    }
    
    toast.success("All notifications marked as read");
    return true;
  } catch (error) {
    console.error("Error in markAllAsRead:", error);
    return false;
  }
};

/**
 * Delete a notification
 */
export const deleteNotification = async (id: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('notifications')
      .delete()
      .eq('id', id);
      
    if (error) {
      console.error("Error deleting notification:", error);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error("Error in deleteNotification:", error);
    return false;
  }
};

/**
 * Create an order notification
 */
export const createOrderNotification = async (
  title: string, 
  message: string, 
  orderId: string, 
  role: string = 'staff'
) => {
  try {
    const result = await createNotification({
      title,
      message,
      type: 'order',
      related_id: orderId,
      role
    });
    return result;
  } catch (error) {
    console.log("Order notification not created. This is not critical for application functionality.");
    return null;
  }
};

/**
 * Create a system notification
 */
export const createSystemNotification = async (
  title: string, 
  message: string,
  role: string = 'all'
) => {
  try {
    return await createNotification({
      title,
      message,
      type: 'system',
      role
    });
  } catch (error) {
    console.log("System notification not created. This is not critical for application functionality.");
    return null;
  }
};

/**
 * Create an inventory notification
 */
export const createInventoryNotification = async (
  title: string, 
  message: string,
  inventoryId?: string, 
  role: string = 'staff'
) => {
  try {
    return await createNotification({
      title,
      message,
      type: 'inventory',
      related_id: inventoryId,
      role
    });
  } catch (error) {
    console.log("Inventory notification not created. This is not critical for application functionality.");
    return null;
  }
};
