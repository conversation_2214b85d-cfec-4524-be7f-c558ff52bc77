
import React from 'react';
import { PriceBreakdown, ClientItemWithQuantity } from '../OrderFormTypes';
import { formatCurrency, getServiceLabel } from './utils';
import { Card, CardContent } from '@/components/ui/card';
import { TotalSection } from './TotalSection';
import { ClientItemsBreakdown, DryCleaningItemsBreakdown } from './ItemsBreakdownSection';
import { calculateItemTotalWithTreatments } from '@/utils/priceCalculations';

interface PriceCalculationProps {
  priceBreakdown: PriceBreakdown;
  clientItems?: ClientItemWithQuantity[];
  dryCleaningItems?: any[];
  showDetailedBreakdown?: boolean;
  // Add the additional props that are being passed
  detergentType?: "none" | "regular" | "color";
  detergentQuantity?: number;
  conditionerType?: "none" | "regular" | "fresh" | "floral";
  conditionerQuantity?: number;
  useStainRemover?: boolean;
  useBleach?: boolean;
  pricingMethod?: string;
  serviceType?: string;
  weightKilos?: number;
  numberOfPieces?: number;
  selectedServiceTypes?: string[];
  serviceWeights?: { serviceType: string; weightKilos: number }[];
}

export function PriceCalculation({ 
  priceBreakdown, 
  clientItems = [], 
  dryCleaningItems = [],
  showDetailedBreakdown = true,
  // These props are not used in this component but are passed from OrderFormContent
  // Added here to satisfy TypeScript
  detergentType,
  detergentQuantity,
  conditionerType,
  conditionerQuantity,
  useStainRemover,
  useBleach,
  pricingMethod,
  serviceType,
  weightKilos,
  numberOfPieces,
  selectedServiceTypes,
  serviceWeights
}: PriceCalculationProps) {
  const { basePrice, addOnPrice, subtotal, vatAmount, totalPrice } = priceBreakdown;
  
  // Group client items by name for better organization
  const clientItemsByName = clientItems.reduce((acc: Record<string, ClientItemWithQuantity[]>, item) => {
    if (!acc[item.name]) {
      acc[item.name] = [];
    }
    acc[item.name].push(item);
    return acc;
  }, {});
  
  return (
    <Card className="border-border">
      <CardContent className="pt-6 space-y-4">
        <h3 className="font-medium text-base">Price Calculation</h3>
        
        {showDetailedBreakdown && (
          <div className="space-y-4">
            {/* Items Section */}
            <div>
              <h4 className="text-sm font-medium mb-2">Items:</h4>
              
              {clientItems && clientItems.length > 0 && (
                <div className="space-y-2">
                  {Object.entries(clientItemsByName).map(([itemName, items]) => (
                    <div key={itemName} className="space-y-1">
                      {items.length === 1 ? (
                        // Single item without special treatment
                        <div className="flex justify-between text-sm">
                          <span>{itemName} (x{items[0].quantity})</span>
                          <span>{formatCurrency(calculateItemTotalWithTreatments(items[0]))}</span>
                        </div>
                      ) : (
                        // Multiple instances of the same item
                        <>
                          <div className="text-sm font-medium">{itemName}:</div>
                          {items.map((item, index) => {
                            const hasTreatments = item.treatments?.useStainRemoval || item.treatments?.useBeachTreatment;
                            return (
                              <div key={item.instanceId} className="flex justify-between text-sm pl-4">
                                <span>
                                  Instance #{index + 1} (x{item.quantity})
                                  {hasTreatments ? " with special treatment" : ""}
                                </span>
                                <span>{formatCurrency(calculateItemTotalWithTreatments(item))}</span>
                              </div>
                            );
                          })}
                        </>
                      )}
                    </div>
                  ))}
                </div>
              )}
              
              {dryCleaningItems && dryCleaningItems.length > 0 && (
                <DryCleaningItemsBreakdown items={dryCleaningItems} basePrice={basePrice} />
              )}
              
              {(!clientItems || clientItems.length === 0) && 
               (!dryCleaningItems || dryCleaningItems.length === 0) && (
                <div className="flex justify-between text-sm">
                  <span>Base Price</span>
                  <span>{formatCurrency(basePrice)}</span>
                </div>
              )}
            </div>
            
            {/* Add-on Price Section */}
            {addOnPrice > 0 && (
              <div>
                <h4 className="text-sm font-medium mb-2">Add-ons:</h4>
                <div className="flex justify-between text-sm">
                  <span>Additional Services</span>
                  <span>{formatCurrency(addOnPrice)}</span>
                </div>
              </div>
            )}
          </div>
        )}
        
        {/* Total Section */}
        <TotalSection 
          priceBreakdown={priceBreakdown}
        />
      </CardContent>
    </Card>
  );
}
