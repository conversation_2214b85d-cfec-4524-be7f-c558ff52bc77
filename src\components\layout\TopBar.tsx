
import { useIsMobile } from "@/hooks/use-mobile";
import { MobileMenuButton } from "./MobileMenuButton";
import { NotificationsDropdown } from "./notifications/NotificationsDropdown";
import { PrinterStatusIndicator } from "./PrinterStatusIndicator";
import { BreadcrumbNav } from "./BreadcrumbNav";

interface TopBarProps {
  onMenuClick?: () => void;
}

export function TopBar({ onMenuClick }: TopBarProps) {
  const isMobile = useIsMobile();

  return (
    <div className="border-b bg-background">
      <div className="flex h-16 items-center px-4">
        {isMobile && <MobileMenuButton onClick={onMenuClick || (() => {})} />}
        
        <div className="flex-1 flex items-center">
          {/* Breadcrumbs are not shown on mobile - implemented within BreadcrumbNav */}
          <div className="hidden md:block ml-4 lg:ml-0">
            <BreadcrumbNav />
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          {/* Notifications dropdown */}
          <NotificationsDropdown />
          
          {/* Printer status indicator */}
          <PrinterStatusIndicator />
        </div>
      </div>
    </div>
  );
}
