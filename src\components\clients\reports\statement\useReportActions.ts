
import { useState } from 'react';
import { toast } from 'sonner';
import { useReactToPrint } from '@/hooks/use-print';
import { OrderSummary } from '@/hooks/orders/reports/types';

interface UseReportActionsProps {
  reportRef: React.RefObject<HTMLDivElement>;
  summary: {
    paidOrders: OrderSummary[];
    unpaidOrders: OrderSummary[];
  };
}

export function useReportActions({ reportRef, summary }: UseReportActionsProps) {
  const [isPrinting, setIsPrinting] = useState(false);

  // Handle print
  const handlePrint = useReactToPrint({
    content: () => reportRef.current,
    documentTitle: `Statement of Account`,
    onBeforePrint: () => setIsPrinting(true),
    onAfterPrint: () => {
      setIsPrinting(false);
      toast.success("Report printed successfully");
    },
    onPrintError: () => {
      setIsPrinting(false);
      toast.error("Printing failed");
    }
  });

  // Handle export as CSV
  const handleExportCSV = () => {
    try {
      // Simple CSV export
      const headers = ["Reference", "Date", "Amount", "Paid", "Status"];
      const csvData = [
        headers.join(","),
        ...(summary.paidOrders.concat(summary.unpaidOrders)).map(order => [
          order.referenceCode || '-',
          order.orderDate,
          order.amount,
          order.paidAmount,
          order.isOverdue ? "Overdue" : (order.paidAmount >= order.amount ? "Paid" : "Unpaid")
        ].join(","))
      ].join("\n");
      
      const blob = new Blob([csvData], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'statement-of-account.csv';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      toast.success("CSV exported successfully");
    } catch (error) {
      console.error("Failed to export CSV:", error);
      toast.error("Failed to export report");
    }
  };

  return {
    isPrinting,
    handlePrint,
    handleExportCSV
  };
}
