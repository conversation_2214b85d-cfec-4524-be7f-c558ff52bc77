
import { ChevronLeft } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";

interface BackButtonProps {
  route?: string;
  label?: string;
  onClick?: () => void;
}

export function BackButton({ 
  route,
  label = "Back",
  onClick
}: BackButtonProps) {
  const navigate = useNavigate();
  
  const handleClick = () => {
    if (onClick) {
      onClick();
    } else if (route) {
      navigate(route);
    } else {
      navigate(-1);
    }
  };
  
  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={handleClick}
      className="gap-1 h-9 px-2"
    >
      <ChevronLeft className="h-4 w-4" />
      <span>{label}</span>
    </Button>
  );
}
