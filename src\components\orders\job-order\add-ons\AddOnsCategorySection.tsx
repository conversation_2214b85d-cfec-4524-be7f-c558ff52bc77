
import { ReactNode } from "react";

interface AddOnsCategorySectionProps {
  title: string;
  children: ReactNode;
}

export function AddOnsCategorySection({ title, children }: AddOnsCategorySectionProps) {
  return (
    <div className="space-y-3 border-b pb-4 mb-4 last:border-none last:pb-0 last:mb-0">
      <h4 className="font-medium text-sm text-gray-700">{title}</h4>
      <div className="space-y-2">
        {children}
      </div>
    </div>
  );
}
