
import { ClientItemWithQuantity } from "../../OrderFormTypes";

// Default price if no price is found
const DEFAULT_ITEM_PRICE = 50;

export async function calculateItemsPrice(
  items: ClientItemWithQuantity[]
): Promise<number> {
  if (!items || items.length === 0) {
    return 0;
  }
  
  // Sum the prices of all selected items with their quantities
  return items.reduce((total, item) => {
    // Use the unitPrice property instead of unit_price
    const price = item.unitPrice || DEFAULT_ITEM_PRICE;
    const quantity = item.quantity || 1;
    
    return total + (price * quantity);
  }, 0);
}

export async function calculateDryCleaningItemsPrice(
  items: any[]
): Promise<number> {
  if (!items || items.length === 0) {
    return 0;
  }
  
  // Sum the prices of all dry cleaning items
  return items.reduce((total, item) => {
    const price = item.price || 0;
    const quantity = item.quantity || 1;
    return total + (price * quantity);
  }, 0);
}
