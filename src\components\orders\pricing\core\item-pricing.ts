
import { ClientItemWithQuantity, DryCleaningItem } from "../../OrderFormTypes";
import { DRY_CLEANING_PRICES, DEFAULT_CLIENT_ITEM_PRICE } from "../constants";

/**
 * Calculate the price for an individual item
 */
export const getItemPrice = (name: string, defaultPrice: number): number => {
  // If a specific price is defined for this item, use it
  return defaultPrice || DEFAULT_CLIENT_ITEM_PRICE;
};

/**
 * Calculate the total price for all client items
 */
export const calculateClientItemsPrice = (items: ClientItemWithQuantity[]): number => {
  if (!items || items.length === 0) {
    return 0;
  }
  
  // Map through each item, multiply unit_price by quantity, and sum
  return items.reduce((total, item) => {
    const itemPrice = typeof item.unit_price === 'number' 
      ? item.unit_price 
      : DEFAULT_CLIENT_ITEM_PRICE;
      
    const quantity = typeof item.quantity === 'number' 
      ? item.quantity 
      : 1;
      
    return total + (itemPrice * quantity);
  }, 0);
};

/**
 * Calculate the total price for all dry cleaning items
 */
export const calculateDryCleaningItemsPrice = (items: DryCleaningItem[]): number => {
  if (!items || items.length === 0) {
    return 0;
  }
  
  // Map through each item, multiply price by quantity, and sum
  return items.reduce((total, item) => {
    const itemPrice = item.price || 
      (item.type && DRY_CLEANING_PRICES[item.type as keyof typeof DRY_CLEANING_PRICES]) || 
      0;
      
    const quantity = typeof item.quantity === 'number' ? item.quantity : 1;
    
    return total + (itemPrice * quantity);
  }, 0);
};
