
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Form } from '@/components/ui/form';
import { Skeleton } from '@/components/ui/skeleton';
import { ClientOrderItemSelector } from './ClientOrderItemSelector';
import { DeliveryDateField } from './order-form/DeliveryDateField';
import { NotesField } from './order-form/NotesField';
import { FormActions } from './order-form/FormActions';
import { OrderFormError } from './order-form/OrderFormError';
import { orderFormSchema, type OrderFormValues, type ClientOrderItem } from './order-form/types';
import { v4 as uuidv4 } from 'uuid';
import { DryCleaningIndicator } from '@/components/orders/job-order/add-ons/DryCleaningIndicator';
import { useState } from 'react';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { DryCleaningItems } from '@/components/orders/laundry-details/DryCleaningItems';

interface ClientOrderFormProps {
  clientItems: { id: string; name: string; item_type: string; unit_price: number; }[];
  isLoading: boolean;
  isSubmitting: boolean;
  onSubmit: (data: OrderFormValues) => void;
}

export function ClientOrderForm({ 
  clientItems = [], 
  isLoading,
  isSubmitting,
  onSubmit 
}: ClientOrderFormProps) {
  const [isDryCleaning, setIsDryCleaning] = useState(false);

  const form = useForm<OrderFormValues>({
    resolver: zodResolver(orderFormSchema),
    defaultValues: {
      deliveryDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // 2 days from now
      notes: '',
      selectedItems: [],
      dryCleaningItems: [],
      isDryCleaning: false
    },
  });

  const handleItemSelection = (items: ClientOrderItem[]) => {
    // Ensure each item keeps its original unitPrice from the database
    const enrichedItems = items.map(item => ({
      ...item,
      // Don't set a default for unitPrice, keep what was passed from the database
      // through the ClientOrderItemSelector
      treatments: item.treatments || {
        useStainRemoval: false,
        detergentType: 'none' as const,
        conditionerType: 'none' as const
      },
      isDryCleaning: isDryCleaning, // Add dry cleaning flag
      instanceId: item.instanceId || uuidv4() // Ensure instanceId exists
    }));
    
    console.log('Setting selected items with original prices:', enrichedItems);
    form.setValue('selectedItems', enrichedItems);
  };

  // Update items when dry cleaning toggle changes
  const handleDryCleaningToggle = (checked: boolean) => {
    setIsDryCleaning(checked);
    form.setValue('isDryCleaning', checked);
    
    // Update existing selected items with the new dry cleaning status
    const currentItems = form.getValues('selectedItems') || [];
    if (currentItems.length > 0) {
      const updatedItems = currentItems.map(item => ({
        ...item,
        isDryCleaning: checked
      }));
      form.setValue('selectedItems', updatedItems);
    }
  };

  const handleFormSubmit = (data: OrderFormValues) => {
    // For dry cleaning orders, validate that items are selected
    if (isDryCleaning && (!data.dryCleaningItems || data.dryCleaningItems.length === 0)) {
      form.setError('dryCleaningItems', {
        type: 'manual',
        message: 'Please select at least one dry cleaning item'
      });
      return;
    }

    // For regular orders, validate that items are selected
    if (!isDryCleaning && (!data.selectedItems || data.selectedItems.length === 0)) {
      form.setError('selectedItems', {
        type: 'manual',
        message: 'Please select at least one item'
      });
      return;
    }

    onSubmit({
      ...data,
      isDryCleaning: isDryCleaning
    });
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleFormSubmit)} className="space-y-4">
        <DeliveryDateField form={form} />

        <div className="flex items-center space-x-2 py-2">
          <Switch 
            id="dry-cleaning" 
            checked={isDryCleaning}
            onCheckedChange={handleDryCleaningToggle}
          />
          <Label htmlFor="dry-cleaning" className="font-medium">
            Dry Cleaning Service
          </Label>
        </div>
        
        {isDryCleaning && (
          <>
            <DryCleaningIndicator isDryCleaning={isDryCleaning} />
            <div className="bg-white p-4 border rounded-md">
              <DryCleaningItems form={form as any} />
            </div>
          </>
        )}

        {!isDryCleaning && (
          <>
            {isLoading ? (
              <div className="space-y-2">
                <Skeleton className="h-5 w-40" />
                <Skeleton className="h-40 w-full" />
              </div>
            ) : (
              <ClientOrderItemSelector 
                items={clientItems} 
                onItemsChange={handleItemSelection} 
              />
            )}
          </>
        )}

        <NotesField form={form} />
        <FormActions form={form} isSubmitting={isSubmitting} />
        <OrderFormError errors={form.formState.errors} />
      </form>
    </Form>
  );
}
