import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Form } from '@/components/ui/form';
import { Skeleton } from '@/components/ui/skeleton';
import { ClientOrderItemSelector } from './ClientOrderItemSelector';
import { DeliveryDateField } from './order-form/DeliveryDateField';
import { NotesField } from './order-form/NotesField';
import { FormActions } from './order-form/FormActions';
import { OrderFormError } from './order-form/OrderFormError';
import { orderFormSchema, type OrderFormValues, type ClientOrderItem } from './order-form/types';
import { v4 as uuidv4 } from 'uuid';

interface ClientOrderFormProps {
  clientItems: { id: string; name: string; item_type: string; unit_price: number; }[];
  isLoading: boolean;
  isSubmitting: boolean;
  onSubmit: (data: OrderFormValues) => void;
}

export function ClientOrderForm({ 
  clientItems = [], 
  isLoading,
  isSubmitting,
  onSubmit 
}: ClientOrderFormProps) {
  const form = useForm<OrderFormValues>({
    resolver: zodResolver(orderFormSchema),
    defaultValues: {
      deliveryDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // 2 days from now
      notes: '',
      selectedItems: [],
    },
  });

  const handleItemSelection = (items: ClientOrderItem[]) => {
    // Ensure each item keeps its original unitPrice from the database
    const enrichedItems = items.map(item => ({
      ...item,
      // Don't set a default for unitPrice, keep what was passed from the database
      // through the ClientOrderItemSelector
      treatments: item.treatments || {
        useStainRemoval: false,
        detergentType: 'none' as const,
        conditionerType: 'none' as const
      },
      instanceId: item.instanceId || uuidv4() // Ensure instanceId exists
    }));
    
    console.log('Setting selected items with original prices:', enrichedItems);
    form.setValue('selectedItems', enrichedItems);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <DeliveryDateField form={form} />

        {isLoading ? (
          <div className="space-y-2">
            <Skeleton className="h-5 w-40" />
            <Skeleton className="h-40 w-full" />
          </div>
        ) : (
          <ClientOrderItemSelector 
            items={clientItems} 
            onItemsChange={handleItemSelection} 
          />
        )}

        <NotesField form={form} />
        <FormActions form={form} isSubmitting={isSubmitting} />
        <OrderFormError errors={form.formState.errors} />
      </form>
    </Form>
  );
}
