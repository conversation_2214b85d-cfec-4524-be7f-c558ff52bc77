import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Form } from '@/components/ui/form';
import { Skeleton } from '@/components/ui/skeleton';
import { ClientOrderItemSelector } from './ClientOrderItemSelector';
import { DeliveryDateField } from './order-form/DeliveryDateField';
import { NotesField } from './order-form/NotesField';
import { FormActions } from './order-form/FormActions';
import { OrderFormError } from './order-form/OrderFormError';
import { orderFormSchema, type OrderFormValues, type ClientOrderItem } from './order-form/types';
import { v4 as uuidv4 } from 'uuid';
import { DryCleaningIndicator } from '@/components/orders/job-order/add-ons/DryCleaningIndicator';
import { format } from 'date-fns';
import { useState } from 'react';
import { DryCleaningItems } from '@/components/orders/laundry-details/DryCleaningItems';
import { SERVICE_TYPES } from '@/components/orders/pricing/constants';
import { Card, CardContent } from '@/components/ui/card';
import { FormControl, FormField, FormItem, FormLabel } from '@/components/ui/form';
import { AndroidCheckbox } from '@/components/ui/android-checkbox';
import { SelectedItemsSummary } from './order-items/SelectedItemsSummary';

interface ClientOrderFormProps {
  clientItems: { id: string; name: string; item_type: string; unit_price: number; }[];
  isLoading: boolean;
  isSubmitting: boolean;
  onSubmit: (data: OrderFormValues) => void;
}

export function ClientOrderForm({
  clientItems = [],
  isLoading,
  isSubmitting,
  onSubmit
}: ClientOrderFormProps) {
  const form = useForm<OrderFormValues>({
    resolver: zodResolver(orderFormSchema),
    defaultValues: {
      deliveryDate: format(new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), "yyyy-MM-dd"), // 2 days from now as string
      notes: '',
      selectedItems: [],
      dryCleaningItems: [],
      serviceType: SERVICE_TYPES.WASH_DRY_FOLD,
      selectedServiceTypes: [SERVICE_TYPES.WASH_DRY_FOLD],
      isDryCleaning: false, // Add default value
    },
  });

  const serviceType = form.watch("serviceType");
  const selectedServiceTypes = form.watch("selectedServiceTypes") || [SERVICE_TYPES.WASH_DRY_FOLD];
  const isDryCleaning = selectedServiceTypes.includes(SERVICE_TYPES.DRY_CLEANING);
  const isWashAndFold = selectedServiceTypes.includes(SERVICE_TYPES.WASH_DRY_FOLD);
  const selectedItems = form.watch("selectedItems") || [];

  const handleItemSelection = (items: ClientOrderItem[]) => {
    // Ensure each item keeps its original unitPrice from the database
    const enrichedItems = items.map(item => ({
      ...item,
      // Don't set a default for unitPrice, keep what was passed from the database
      // through the ClientOrderItemSelector
      treatments: item.treatments || {
        useStainRemoval: false,
        useBeachTreatment: false, // Make sure to include this property
        detergentType: 'none' as const,
        conditionerType: 'none' as const
      },
      instanceId: item.instanceId || uuidv4() // Ensure instanceId exists
    }));

    console.log('Setting selected items with original prices:', enrichedItems);
    form.setValue('selectedItems', enrichedItems);
  };

  // Handle service type selection
  const handleServiceTypeToggle = (serviceType: string, checked: boolean) => {
    let currentSelectedTypes = [...selectedServiceTypes];

    if (checked && !currentSelectedTypes.includes(serviceType)) {
      currentSelectedTypes.push(serviceType);
    } else if (!checked && currentSelectedTypes.includes(serviceType)) {
      currentSelectedTypes = currentSelectedTypes.filter(type => type !== serviceType);
    }

    // Ensure we always have at least one service type selected
    if (currentSelectedTypes.length === 0) {
      currentSelectedTypes = [SERVICE_TYPES.WASH_DRY_FOLD];
    }

    // Update form values
    form.setValue("selectedServiceTypes", currentSelectedTypes);

    // Set primary service type for backward compatibility
    if (currentSelectedTypes.includes(SERVICE_TYPES.DRY_CLEANING)) {
      form.setValue("serviceType", SERVICE_TYPES.DRY_CLEANING);
      form.setValue("isDryCleaning", true); // Now this field exists in our schema
    } else {
      form.setValue("serviceType", currentSelectedTypes[0]);
      form.setValue("isDryCleaning", false); // Now this field exists in our schema
    }
  };

  const handleFormSubmit = (data: OrderFormValues) => {
    const errors: {[key: string]: {type: string, message: string}} = {};
    const serviceTypes = data.selectedServiceTypes || [data.serviceType];

    // Validate based on selected service types
    if (serviceTypes.includes(SERVICE_TYPES.DRY_CLEANING) &&
        (!data.dryCleaningItems || data.dryCleaningItems.length === 0)) {
      errors.dryCleaningItems = {
        type: 'manual',
        message: 'Please select at least one dry cleaning item'
      };
    }

    if (serviceTypes.includes(SERVICE_TYPES.WASH_DRY_FOLD) &&
        (!data.selectedItems || data.selectedItems.length === 0)) {
      errors.selectedItems = {
        type: 'manual',
        message: 'Please select at least one item'
      };
    }

    // If there are validation errors, set them and return
    if (Object.keys(errors).length > 0) {
      Object.entries(errors).forEach(([field, error]) => {
        form.setError(field as any, error);
      });
      return;
    }

    // Set isDryCleaning based on serviceType for API compatibility
    const formData = {
      ...data,
      isDryCleaning: serviceTypes.includes(SERVICE_TYPES.DRY_CLEANING)
    };

    onSubmit(formData);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleFormSubmit)} className="space-y-4">
        <DeliveryDateField form={form} />

        {/* Service Type Selection */}
        <Card className="border-border">
          <CardContent className="pt-4">
            <FormItem className="space-y-3">
              <FormLabel className="text-base font-medium">Service Type</FormLabel>
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center space-x-3 rounded-md border p-3 cursor-pointer hover:bg-accent">
                  <AndroidCheckbox
                    id="wash-fold-checkbox"
                    checked={selectedServiceTypes.includes(SERVICE_TYPES.WASH_DRY_FOLD)}
                    onCheckedChange={(checked) => {
                      handleServiceTypeToggle(SERVICE_TYPES.WASH_DRY_FOLD, checked);
                    }}
                  />
                  <FormLabel
                    htmlFor="wash-fold-checkbox"
                    className="font-normal cursor-pointer flex-1"
                    onClick={() => {
                      const isCurrentlySelected = selectedServiceTypes.includes(SERVICE_TYPES.WASH_DRY_FOLD);
                      handleServiceTypeToggle(SERVICE_TYPES.WASH_DRY_FOLD, !isCurrentlySelected);
                    }}
                  >
                    Wash, Dry & Fold
                  </FormLabel>
                </div>
                <div className="flex items-center space-x-3 rounded-md border p-3 cursor-pointer hover:bg-accent">
                  <AndroidCheckbox
                    id="dry-cleaning-checkbox"
                    checked={selectedServiceTypes.includes(SERVICE_TYPES.DRY_CLEANING)}
                    onCheckedChange={(checked) => {
                      handleServiceTypeToggle(SERVICE_TYPES.DRY_CLEANING, checked);
                    }}
                  />
                  <FormLabel
                    htmlFor="dry-cleaning-checkbox"
                    className="font-normal cursor-pointer flex-1"
                    onClick={() => {
                      const isCurrentlySelected = selectedServiceTypes.includes(SERVICE_TYPES.DRY_CLEANING);
                      handleServiceTypeToggle(SERVICE_TYPES.DRY_CLEANING, !isCurrentlySelected);
                    }}
                  >
                    Dry Cleaning Service
                  </FormLabel>
                </div>
              </div>
            </FormItem>
          </CardContent>
        </Card>

        {isDryCleaning && (
          <DryCleaningIndicator isDryCleaning={true} />
        )}

        {/* Show appropriate sections based on selected service types */}
        {isWashAndFold && (
          <>
            {isLoading ? (
              <div className="space-y-2">
                <Skeleton className="h-5 w-40" />
                <Skeleton className="h-40 w-full" />
              </div>
            ) : (
              <>
                <ClientOrderItemSelector
                  items={clientItems}
                  onItemsChange={handleItemSelection}
                />
                <SelectedItemsSummary selectedItems={selectedItems} />
              </>
            )}
          </>
        )}

        {isDryCleaning && (
          <div className="bg-white p-4 border rounded-md">
            {/* Cast the form to any to prevent type errors */}
            <DryCleaningItems form={form as any} />
          </div>
        )}

        <NotesField form={form} />
        <FormActions form={form} isSubmitting={isSubmitting} />
        <OrderFormError errors={form.formState.errors} />
      </form>
    </Form>
  );
}
