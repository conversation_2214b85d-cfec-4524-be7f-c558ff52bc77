
import { useState, useCallback } from 'react';
import { DateRange } from 'react-day-picker';
import { OrderSummary } from './types';

export function useOrdersFilter() {
  const [dateRange, setDateRange] = useState<DateRange>({
    from: undefined,
    to: undefined,
  });
  
  const [overdueFilter, setOverdueFilter] = useState<boolean | null>(null);

  const updateDateFilter = useCallback((newRange: DateRange) => {
    setDateRange(newRange);
    return newRange;
  }, []);

  const updateOverdueFilter = useCallback((isOverdue: boolean | null) => {
    setOverdueFilter(isOverdue);
    return isOverdue;
  }, []);

  const applyFilters = useCallback((orders: OrderSummary[], range: DateRange, showOverdueOnly: boolean | null) => {
    let filteredOrders = [...orders];
    
    // Apply date range filter if set
    if (range.from || range.to) {
      filteredOrders = filteredOrders.filter(order => {
        const orderDate = new Date(order.orderDate);
        
        if (range.from && range.to) {
          return orderDate >= range.from && orderDate <= range.to;
        } else if (range.from) {
          return orderDate >= range.from;
        } else if (range.to) {
          return orderDate <= range.to;
        }
        
        return true;
      });
    }
    
    // Apply overdue filter if set
    if (showOverdueOnly === true) {
      filteredOrders = filteredOrders.filter(order => order.isOverdue === true);
    }
    
    return filteredOrders;
  }, []);

  return {
    dateRange,
    overdueFilter,
    updateDateFilter,
    updateOverdueFilter,
    applyFilters,
    filteredOrders: [] // Initial empty array, will be populated by the parent hook
  };
}
