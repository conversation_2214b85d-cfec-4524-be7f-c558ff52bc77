
import { useState, useCallback } from 'react';
import { DateRange } from 'react-day-picker';
import { OrderSummary } from './types';

export function useOrdersFilter() {
  const [dateRange, setDateRange] = useState<DateRange | undefined>(undefined);
  const [overdueFilter, setOverdueFilter] = useState<boolean | null>(null);
  const [filteredOrders, setFilteredOrders] = useState<OrderSummary[]>([]);

  const updateDateFilter = useCallback((newRange: DateRange) => {
    setDateRange(newRange);
    return newRange;
  }, []);

  const updateOverdueFilter = useCallback((isOverdue: boolean | null) => {
    setOverdueFilter(isOverdue);
    return isOverdue;
  }, []);

  const applyFilters = useCallback((
    orders: OrderSummary[], 
    dateRange?: DateRange, 
    overdueOnly?: boolean | null
  ): OrderSummary[] => {
    let filtered = [...orders];

    // Apply date filter if set
    if (dateRange?.from) {
      const fromDate = new Date(dateRange.from);
      filtered = filtered.filter(order => {
        const orderDate = new Date(order.orderDate);
        return orderDate >= fromDate;
      });
    }

    if (dateRange?.to) {
      const toDate = new Date(dateRange.to);
      filtered = filtered.filter(order => {
        const orderDate = new Date(order.orderDate);
        return orderDate <= toDate;
      });
    }

    // Apply overdue filter if set
    if (overdueOnly !== null) {
      filtered = filtered.filter(order => order.isOverdue === overdueOnly);
    }

    setFilteredOrders(filtered);
    return filtered;
  }, []);

  return {
    dateRange,
    overdueFilter,
    filteredOrders,
    updateDateFilter,
    updateOverdueFilter,
    applyFilters
  };
}
