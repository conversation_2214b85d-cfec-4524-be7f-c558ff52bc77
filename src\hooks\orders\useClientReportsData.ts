
import { useState, useEffect } from 'react';
import { useClientReportState } from './reports/useClientReportState';
import { OrderSummary } from './reports/types';
import { DateRange } from 'react-day-picker';

export function useClientReportsData(clientId: string) {
  const [isLoading, setIsLoading] = useState(true);
  
  const {
    orders,
    filteredOrders,
    summary,
    dateRange,
    overdueFilter,
    updateDateFilter,
    updateOverdueFilter,
    datePresets,
    isLoading: dataLoading,
    error
  } = useClientReportState(clientId);

  useEffect(() => {
    if (!dataLoading && !error) {
      setIsLoading(false);
    }
  }, [dataLoading, error]);

  return {
    isLoading: isLoading || dataLoading,
    summary: {
      totalOrders: summary.totalOrders,
      totalAmount: summary.totalAmount,
      totalPaid: summary.totalPaid,
      totalPayable: summary.totalPayable,
      paidOrdersCount: summary.paidOrders.length,
      unpaidOrdersCount: summary.unpaidOrders.length,
      overdueOrdersCount: summary.overdueOrders.length,
      paidOrders: summary.paidOrders,
      unpaidOrders: summary.unpaidOrders,
      overdueOrders: summary.overdueOrders
    },
    dateRange,
    overdueFilter,
    updateDateFilter,
    updateOverdueFilter,
    datePresets
  };
}
