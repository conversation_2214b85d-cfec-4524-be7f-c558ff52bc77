
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.43.1";

interface Profile {
  id: string;
  first_name: string | null;
  last_name: string | null;
  phone: string | null;
  position: string | null;
}

interface UserRole {
  user_id: string;
  role: string;
}

interface ResponseUser {
  id: string;
  email: string;
  first_name: string | null;
  last_name: string | null;
  phone: string | null;
  position: string | null;
  role: string;
  created_at: string;
}

// Define CORS headers for browser compatibility
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { 
      headers: corsHeaders,
      status: 204
    });
  }
  
  // Create a Supabase client with the Admin key
  const supabaseUrl = Deno.env.get("SUPABASE_URL") || "";
  const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";
  
  if (!supabaseUrl || !supabaseServiceKey) {
    console.error("Missing environment variables: SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY");
    return new Response(
      JSON.stringify({ error: "Server configuration error" }),
      { 
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500
      }
    );
  }
  
  const supabase = createClient(supabaseUrl, supabaseServiceKey);

  try {
    console.log("Fetching auth users with admin privileges");
    // Get auth users with admin privileges
    const { data: authUsers, error: authError } = await supabase.auth.admin.listUsers();
    
    if (authError) {
      console.error("Auth error:", authError);
      throw authError;
    }
    
    console.log(`Found ${authUsers.users.length} auth users`);
    
    // Get profiles
    console.log("Fetching profiles");
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('*');
      
    if (profilesError) {
      console.error("Profiles error:", profilesError);
      throw profilesError;
    }
    
    console.log(`Found ${profiles?.length || 0} profiles`);
    
    // Get roles
    console.log("Fetching user roles");
    const { data: roles, error: rolesError } = await supabase
      .from('user_roles')
      .select('*');
      
    if (rolesError) {
      console.error("Roles error:", rolesError);
      throw rolesError;
    }
    
    console.log(`Found ${roles?.length || 0} user roles`);
    
    // Combine data
    const users: ResponseUser[] = authUsers.users.map(user => {
      const profile = profiles?.find((p: Profile) => p.id === user.id) || {
        first_name: null,
        last_name: null,
        phone: null,
        position: null,
      };
      
      const userRole = roles?.find((r: UserRole) => r.user_id === user.id);
      
      return {
        id: user.id,
        email: user.email || "",
        first_name: profile.first_name,
        last_name: profile.last_name,
        phone: profile.phone,
        position: profile.position,
        role: userRole?.role || "client",
        created_at: user.created_at,
      };
    });
    
    console.log(`Returning ${users.length} combined user records`);
    
    return new Response(JSON.stringify(users), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 200,
    });
  } catch (error) {
    console.error("Edge function error:", error.message);
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 500,
    });
  }
});
