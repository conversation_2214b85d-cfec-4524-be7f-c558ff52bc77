
import React, { useEffect } from "react";
import { Input } from "@/components/ui/input";
import { ShoppingBag } from "lucide-react";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { UseFormReturn } from "react-hook-form";
import { OrderFormValues } from "../OrderFormTypes";

interface PiecesInputProps {
  form: UseFormReturn<OrderFormValues>;
}

export function PiecesInput({ form }: PiecesInputProps) {
  // Calculate total pieces from selected items
  useEffect(() => {
    const orderType = form.watch("orderType");
    
    if (orderType === "client") {
      // For client orders, calculate total pieces from selected client items
      const selectedClientItems = form.watch("selectedClientItems") || [];
      const totalPieces = selectedClientItems.reduce((total, item) => {
        return total + (parseInt(item.quantity) || 1);
      }, 0);
      
      // Only update if we have items and the total is greater than 0
      if (selectedClientItems.length > 0 && totalPieces > 0) {
        form.setValue("numberOfPieces", totalPieces.toString());
      }
    }
  }, [form.watch("selectedClientItems"), form.watch("orderType")]);

  const orderType = form.watch("orderType");
  const readOnly = orderType === "client";

  return (
    <FormField
      control={form.control}
      name="numberOfPieces"
      render={({ field }) => (
        <FormItem>
          <FormLabel className="text-base flex items-center">
            <ShoppingBag className="mr-2 h-4 w-4" />
            Number of Pieces {readOnly ? "(auto-calculated)" : "(for records)"}
          </FormLabel>
          <FormControl>
            <Input 
              inputMode="numeric" 
              pattern="[0-9]*" 
              min="1" 
              step="1" 
              placeholder="0" 
              {...field} 
              className="h-12 text-center text-base"
              readOnly={readOnly}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
