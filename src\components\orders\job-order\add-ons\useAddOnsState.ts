
import { useState, useEffect } from "react";
import { Order, DryCleaningItem } from "@/types";
import { SERVICE_TYPES } from "../../pricing/constants";

interface AddOnsState {
  useDetergent: boolean;
  detergentType: 'none' | 'regular' | 'color';
  detergentQuantity: string;
  useFabricConditioner: boolean;
  conditionerType: 'none' | 'regular' | 'fresh' | 'floral';
  conditionerQuantity: string;
  useStainRemover: boolean;
  useBleach: boolean;
  dryCleaningItems: DryCleaningItem[];
}

export function useAddOnsState(order: Order) {
  const [addOnsState, setAddOnsState] = useState<AddOnsState>({
    useDetergent: order.useDetergent || false,
    detergentType: mapStringToDetergentType(order.detergentType),
    detergentQuantity: order.detergentQuantity?.toString() || "1",
    
    useFabricConditioner: order.useFabricConditioner || false,
    conditionerType: mapStringToConditionerType(order.conditionerType),
    conditionerQuantity: order.conditionerQuantity?.toString() || "1",
    
    useStainRemover: order.useStainRemover || false,
    useBleach: order.useBleach || false,
    
    dryCleaningItems: order.dryCleaningItems || []
  });
  
  // Helper function to map string to detergent type
  function mapStringToDetergentType(type: string | undefined): 'none' | 'regular' | 'color' {
    if (!type) return 'none';
    if (type === 'regular' || type === 'color') return type;
    return 'none';
  }
  
  // Helper function to map string to conditioner type
  function mapStringToConditionerType(type: string | undefined): 'none' | 'regular' | 'fresh' | 'floral' {
    if (!type) return 'none';
    if (type === 'regular' || type === 'fresh' || type === 'floral') return type;
    return 'none';
  }
  
  // Update dryCleaningItems when order changes
  useEffect(() => {
    if (order.dryCleaningItems) {
      // Ensure all dry cleaning items have a total property
      const itemsWithTotal: DryCleaningItem[] = (order.dryCleaningItems || []).map(item => ({
        ...item,
        total: item.total || (item.price * item.quantity)
      }));
      
      setAddOnsState(prev => ({
        ...prev,
        dryCleaningItems: itemsWithTotal
      }));
    }
  }, [order.dryCleaningItems]);
  
  const updateAddOnState = (key: keyof AddOnsState, value: any) => {
    setAddOnsState(prev => ({ ...prev, [key]: value }));
  };
  
  const handleDetergentChange = (checked: boolean) => {
    updateAddOnState('useDetergent', checked);
    if (!checked) {
      updateAddOnState('detergentType', 'none');
    } else if (addOnsState.detergentType === 'none') {
      updateAddOnState('detergentType', 'regular');
    }
  };
  
  const handleConditionerChange = (checked: boolean) => {
    updateAddOnState('useFabricConditioner', checked);
    if (!checked) {
      updateAddOnState('conditionerType', 'none');
    } else if (addOnsState.conditionerType === 'none') {
      updateAddOnState('conditionerType', 'regular');
    }
  };
  
  return {
    addOnsState,
    updateAddOnState,
    handleDetergentChange,
    handleConditionerChange
  };
}
