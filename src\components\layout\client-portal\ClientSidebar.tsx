import { Link, useLocation } from 'react-router-dom';
import { Package, FileText, User, LogOut, BarChart } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { User as UserType } from '@supabase/supabase-js';
interface ClientSidebarProps {
  clientName: string;
  user: UserType | null;
  onLogout: () => void;
}
export function ClientSidebar({
  clientName,
  user,
  onLogout
}: ClientSidebarProps) {
  const location = useLocation();

  // Restricted navigation items - only show allowed pages
  const navItems = [{
    href: '/orders',
    icon: Package,
    text: 'Orders'
  }, {
    href: '/items',
    icon: FileText,
    text: 'Service Items'
  }, {
    href: '/reports',
    icon: BarChart,
    text: 'Reports'
  }];
  return <aside className="hidden md:flex flex-col w-64 bg-[#1E293B] text-gray-300">
      <div className="p-4 border-b border-slate-700 flex justify-between items-center">
        <img src="/public/lovable-uploads/2098e888-16b9-4a6b-bbbb-b20700ff6651.png" alt="CMC Laundry Services" className="h-20 w-auto " />
      </div>
      <div className="flex-1 overflow-y-auto py-4">
        <nav className="space-y-1">
          {navItems.map(item => <Link key={item.href} to={item.href} className={`flex items-center px-6 py-2 text-sm ${location.pathname === item.href ? 'bg-[#2A2F3C] text-white' : 'text-gray-300 hover:bg-[#2A2F3C] hover:text-white'}`}>
              <item.icon className="mr-3 h-5 w-5" />
              {item.text}
            </Link>)}
        </nav>
      </div>
      
      {/* User profile section */}
      <div className="mt-auto border-t border-slate-700 p-3">
        <div className="space-y-3">
          <div className="flex items-center gap-2 p-2 rounded-md hover:bg-[#2A2F3C]">
            <Avatar className="h-8 w-8">
              <AvatarFallback className="bg-slate-600">{user?.email?.charAt(0).toUpperCase() || 'U'}</AvatarFallback>
            </Avatar>
            <div className="flex flex-col">
              <span className="text-sm font-medium">{user?.email?.split('@')[0] || 'User'}</span>
              <span className="text-xs text-slate-400">{user?.email || ''}</span>
            </div>
          </div>
          <div className="space-y-1">
            <Link to="/profile">
              <Button variant="ghost" className="w-full justify-start text-slate-300 hover:text-white hover:bg-[#2A2F3C]">
                <User className="mr-2 h-4 w-4" />
                <span>Profile</span>
              </Button>
            </Link>
            <Button variant="ghost" className="w-full justify-start text-slate-300 hover:text-white hover:bg-[#2A2F3C]" onClick={onLogout}>
              <LogOut className="mr-2 h-4 w-4" />
              <span>Logout</span>
            </Button>
          </div>
        </div>
      </div>
      
      <div className="p-4 border-t border-gray-700">
        <p className="text-xs text-gray-400 text-center">© 2025 Powered by PhilVirtualOffice</p>
      </div>
    </aside>;
}