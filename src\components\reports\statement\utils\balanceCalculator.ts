
import { ClientDetails } from '../types';
import { OrderSummary } from '@/hooks/orders/reports/types';

export function calculateCurrentBalance(client: ClientDetails, orders: OrderSummary[]): number {
  if (!orders || orders.length === 0) {
    return 0;
  }

  // Calculate the total amount due from all orders
  const totalAmount = orders.reduce((sum, order) => sum + order.amount, 0);
  
  // Calculate the total amount paid across all orders
  const totalPaid = orders.reduce((sum, order) => sum + order.paidAmount, 0);
  
  // The balance due is the total amount minus total paid
  const balanceDue = totalAmount - totalPaid;
  
  return balanceDue;
}
