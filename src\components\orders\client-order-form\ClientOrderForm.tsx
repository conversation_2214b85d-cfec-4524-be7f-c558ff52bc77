
import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Form } from "@/components/ui/form";
import { OrderFormProps } from "../order-form/OrderFormTypes";
import { OrderFormValues, PriceBreakdown } from "../OrderFormTypes";
import { useIsMobile } from "@/hooks/use-mobile";
import { orderFormSchema } from "../OrderFormTypes";
import { FormActions } from "../order-form/FormActions";
import { FormContainer } from "../order-form/FormContainer";
import { SERVICE_TYPES } from "../pricing/constants";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { DryCleaningIndicator } from "../job-order/add-ons/DryCleaningIndicator";

// Import refactored components
import { ClientFormHeader } from "./components/ClientFormHeader";
import { ClientFormErrorDisplay } from "./components/ClientFormErrorDisplay";
import { ClientSelectorSection } from "./components/ClientSelectorSection";
import { ClientItemsSection } from "./components/ClientItemsSection";
import { LaundryDetailsSection } from "./components/LaundryDetailsSection";
// Removed AddOnsSection import
import { PriceSection } from "./components/PriceSection";
import { PaymentSection } from "./components/PaymentSection";
import { DryCleaningItems } from "../laundry-details/DryCleaningItems";

export function ClientOrderForm({
  onSubmit,
  isSubmitting = false
}: OrderFormProps) {
  const [priceBreakdown, setPriceBreakdown] = useState<PriceBreakdown>({
    basePrice: 0,
    addOnPrice: 0,
    subtotal: 0,
    vatAmount: 0,
    totalPrice: 0
  });
  const [isDryCleaning, setIsDryCleaning] = useState(false);
  const isMobile = useIsMobile();

  // Initialize form specifically for client orders
  const form = useForm<OrderFormValues>({
    resolver: zodResolver(orderFormSchema),
    defaultValues: {
      orderType: "client",
      pricingMethod: "client_item",
      deliveryDate: "",
      weightKilos: "1.0",
      numberOfPieces: "1",
      detergentType: "none" as "none" | "regular" | "color",
      detergentQuantity: "1",
      conditionerType: "none" as "none" | "regular" | "fresh" | "floral",
      conditionerQuantity: "1",
      useStainRemover: false,
      useBleach: false,
      useDetergent: false,
      useFabricConditioner: false,
      isDryCleaning: false,
      paidAmount: "0",
      clientId: "",
      serviceType: SERVICE_TYPES.WASH_DRY_FOLD, // Default service type
      selectedClientItems: [],
      dryCleaningItems: [] // Initialize empty dry cleaning items array
    },
    mode: "onChange"
  });

  const handleSubmit = async (data: OrderFormValues) => {
    console.log("ClientOrderForm submit triggered", data);
    if (!data.clientId) {
      form.setError("clientId", { 
        type: "manual", 
        message: "Please select a client" 
      });
      return;
    }

    if (!isDryCleaning && (!data.selectedClientItems || data.selectedClientItems.length === 0)) {
      form.setError("selectedClientItems", { 
        type: "manual", 
        message: "Please select at least one item" 
      });
      return;
    }

    // For dry cleaning orders, validate that dry cleaning items are selected
    if (isDryCleaning && (!data.dryCleaningItems || data.dryCleaningItems.length === 0)) {
      form.setError("dryCleaningItems", { 
        type: "manual", 
        message: "Please select at least one dry cleaning item" 
      });
      return;
    }

    onSubmit({
      ...data,
      orderAmount: priceBreakdown.totalPrice,
      vatAmount: priceBreakdown.vatAmount,
      subtotal: priceBreakdown.subtotal,
      isDryCleaning: isDryCleaning,
      serviceType: isDryCleaning ? SERVICE_TYPES.DRY_CLEANING : data.serviceType
    });
  };
  
  // Handle dry cleaning toggle
  const handleDryCleaningToggle = (checked: boolean) => {
    setIsDryCleaning(checked);
    form.setValue("isDryCleaning", checked);
    
    // Update service type based on dry cleaning selection
    if (checked) {
      form.setValue("serviceType", SERVICE_TYPES.DRY_CLEANING);
      form.setValue("pricingMethod", "dry_cleaning");
    } else {
      form.setValue("serviceType", SERVICE_TYPES.WASH_DRY_FOLD);
      form.setValue("pricingMethod", "client_item");
    }
  };
  
  // Extract watched form values
  const clientId = form.watch("clientId");
  const formErrors = form.formState.errors;
  const hasErrors = Object.keys(formErrors).length > 0;
  
  // Extract error messages
  const errorMessages = Object.entries(formErrors).map(([field, error]) => ({
    field,
    message: error?.message as string || `Please check ${field}`
  }));

  // Extract form values for pricing calculation
  const detergentType = form.watch("detergentType") as "none" | "regular" | "color";
  const conditionerType = form.watch("conditionerType") as "none" | "regular" | "fresh" | "floral";
  const detergentQuantity = parseInt(form.watch("detergentQuantity") || "1");
  const conditionerQuantity = parseInt(form.watch("conditionerQuantity") || "1");
  const useStainRemover = form.watch("useStainRemover") || false;
  const useBleach = form.watch("useBleach") || false;
  const weightKilos = parseFloat(form.watch("weightKilos") || "0");
  const numberOfPieces = parseInt(form.watch("numberOfPieces") || "0");
  const clientItems = form.watch("selectedClientItems") || [];
  const dryCleaningItems = form.watch("dryCleaningItems") || [];
  const serviceType = form.watch("serviceType");
  
  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className={`${isMobile ? 'space-y-3' : 'space-y-6'}`}>
        {/* Form container for price calculation logic */}
        <FormContainer form={form} setPriceBreakdown={setPriceBreakdown} />
        
        {/* Form header and error display */}
        <ClientFormHeader form={form} />
        <ClientFormErrorDisplay errorMessages={errorMessages} hasErrors={hasErrors} />
        
        {/* Client selection section */}
        <ClientSelectorSection form={form} />
        
        {/* Dry cleaning toggle */}
        <div className="flex items-center space-x-2 py-2 mb-4">
          <Switch 
            id="dry-cleaning" 
            checked={isDryCleaning}
            onCheckedChange={handleDryCleaningToggle}
          />
          <Label htmlFor="dry-cleaning" className="font-medium">
            Dry Cleaning Service
          </Label>
        </div>
        
        {isDryCleaning && (
          <DryCleaningIndicator isDryCleaning={isDryCleaning} />
        )}
        
        {/* Conditional rendering based on dry cleaning selection */}
        {isDryCleaning ? (
          <div className="bg-white p-4 border rounded-md">
            <DryCleaningItems form={form} />
          </div>
        ) : (
          /* Client items selection (only show when client is selected and not dry cleaning) */
          clientId && (
            <ClientItemsSection form={form} clientId={clientId} />
          )
        )}
        
        {/* Laundry details section */}
        <LaundryDetailsSection form={form} />
        
        {/* Add-ons section removed */}
        
        {/* Price calculation section */}
        <PriceSection 
          priceBreakdown={priceBreakdown}
          detergentType={detergentType}
          detergentQuantity={detergentQuantity}
          conditionerType={conditionerType}
          conditionerQuantity={conditionerQuantity}
          useStainRemover={useStainRemover}
          useBleach={useBleach}
          serviceType={serviceType}
          weightKilos={weightKilos}
          numberOfPieces={numberOfPieces}
          clientItems={clientItems}
          dryCleaningItems={isDryCleaning ? dryCleaningItems : []}
        />

        {/* Payment section */}
        <PaymentSection form={form} priceBreakdown={priceBreakdown} />

        {/* Form actions */}
        <FormActions form={form} isSubmitting={isSubmitting} />
      </form>
    </Form>
  );
}
