
import { Form } from "@/components/ui/form";
import { OrderFormProps } from "../order-form/OrderFormTypes";
import { useIsMobile } from "@/hooks/use-mobile";
import { FormActions } from "../order-form/FormActions";
import { FormContainer } from "../order-form/FormContainer";
import { SERVICE_TYPES } from "../pricing/constants";
import { DryCleaningIndicator } from "../job-order/add-ons/DryCleaningIndicator";

// Import custom components
import { ClientFormHeader } from "./components/ClientFormHeader";
import { ClientFormErrorDisplay } from "./components/ClientFormErrorDisplay";
import { ClientSelectorSection } from "./components/ClientSelectorSection";
import { ClientItemsSection } from "./components/ClientItemsSection";
import { LaundryDetailsSection } from "./components/LaundryDetailsSection";
import { PriceSection } from "./components/PriceSection";
import { PaymentSection } from "./components/PaymentSection";
import { DryCleaningItems } from "../laundry-details/DryCleaningItems";
import { ServiceWeightsSection } from "../laundry-details/ServiceWeightsSection";
import { ServiceTypeSelector } from "./components/ServiceTypeSelector";
import { useClientOrderFormState } from "./hooks/useClientOrderFormState";
import { extractFormErrors } from "./components/FormErrorExtractor";

export function ClientOrderForm({
  onSubmit,
  isSubmitting = false
}: OrderFormProps) {
  const isMobile = useIsMobile();
  const {
    form,
    priceBreakdown,
    setPriceBreakdown,
    handleServiceTypeToggle,
    handleSubmit,
    isSubmitting: submissionInProgress
  } = useClientOrderFormState(onSubmit, isSubmitting);
  
  // Extract watched form values
  const clientId = form.watch("clientId");
  const selectedServiceTypes = form.watch("selectedServiceTypes") || [SERVICE_TYPES.WASH_DRY_FOLD];
  const serviceType = form.watch("serviceType");
  const isDryCleaning = selectedServiceTypes.includes(SERVICE_TYPES.DRY_CLEANING);
  const isWashAndFold = selectedServiceTypes.includes(SERVICE_TYPES.WASH_DRY_FOLD);
  
  // Extract errors
  const { errorMessages, hasErrors } = extractFormErrors(form);

  // Extract form values for pricing calculation
  const detergentType = form.watch("detergentType") as "none" | "regular" | "color";
  const conditionerType = form.watch("conditionerType") as "none" | "regular" | "fresh" | "floral";
  const detergentQuantity = parseInt(String(form.watch("detergentQuantity")) || "1");
  const conditionerQuantity = parseInt(String(form.watch("conditionerQuantity")) || "1");
  const useStainRemover = form.watch("useStainRemover") || false;
  const useBleach = form.watch("useBleach") || false;
  const weightKilos = parseFloat(String(form.watch("weightKilos")) || "0");
  const numberOfPieces = parseInt(String(form.watch("numberOfPieces")) || "0");
  const clientItems = form.watch("selectedClientItems") || [];
  const dryCleaningItems = form.watch("dryCleaningItems") || [];
  
  // Extract service weights and ensure they have required properties
  const serviceWeights = form.watch("serviceWeights")?.map(sw => ({
    serviceType: sw.serviceType,
    weightKilos: sw.weightKilos
  })) || [];
  
  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className={`${isMobile ? 'space-y-3' : 'space-y-6'}`}>
        {/* Form container for price calculation logic */}
        <FormContainer form={form} setPriceBreakdown={setPriceBreakdown} />
        
        {/* Form header and error display */}
        <ClientFormHeader form={form} />
        <ClientFormErrorDisplay errorMessages={errorMessages} hasErrors={hasErrors} />
        
        {/* Client selection section */}
        <ClientSelectorSection form={form} />
        
        {/* Service Type Selection - Using the extracted component */}
        {clientId && (
          <ServiceTypeSelector
            form={form}
            selectedServiceTypes={selectedServiceTypes}
            handleServiceTypeToggle={handleServiceTypeToggle}
          />
        )}
        
        {isDryCleaning && (
          <DryCleaningIndicator isDryCleaning={true} />
        )}
        
        {/* Conditional rendering based on service types */}
        {clientId && (
          <>
            {/* Show items section if wash and fold is selected */}
            {isWashAndFold && (
              <ClientItemsSection form={form} clientId={clientId} />
            )}
            
            {/* Show dry cleaning items if dry cleaning is selected */}
            {isDryCleaning && (
              <div className="bg-white p-4 border rounded-md">
                <DryCleaningItems form={form} />
              </div>
            )}
          </>
        )}

        {/* Service weights section for client orders */}
        {clientId && selectedServiceTypes.length > 1 && (
          <ServiceWeightsSection form={form} />
        )}
        
        {/* Laundry details section */}
        <LaundryDetailsSection form={form} />
        
        {/* Price calculation section */}
        <PriceSection 
          priceBreakdown={priceBreakdown}
          detergentType={detergentType}
          detergentQuantity={detergentQuantity}
          conditionerType={conditionerType}
          conditionerQuantity={conditionerQuantity}
          useStainRemover={useStainRemover}
          useBleach={useBleach}
          serviceType={serviceType}
          weightKilos={weightKilos}
          numberOfPieces={numberOfPieces}
          clientItems={clientItems}
          dryCleaningItems={isDryCleaning ? dryCleaningItems : []}
          selectedServiceTypes={selectedServiceTypes}
          serviceWeights={serviceWeights}
        />

        {/* Payment section */}
        <PaymentSection form={form} priceBreakdown={priceBreakdown} />

        {/* Form actions */}
        <FormActions form={form} isSubmitting={submissionInProgress} />
      </form>
    </Form>
  );
}
