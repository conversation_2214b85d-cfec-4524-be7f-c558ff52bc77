
import { Badge } from "@/components/ui/badge";
import { Order, LineItem } from "@/types";
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table";

interface TableExpandedContentProps {
  order: Order;
  onOrderDeleted?: () => void;
}

export function TableExpandedContent({ order, onOrderDeleted }: TableExpandedContentProps) {
  // Format currency consistently
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-PH", {
      style: "currency",
      currency: "PHP",
    }).format(value);
  };
  
  // Try to parse service items if available
  let serviceItems = [];
  try {
    // The items field might be stored as a JSON string
    if (typeof order.items === 'string' && order.items) {
      serviceItems = JSON.parse(order.items);
    } else if (Array.isArray(order.items)) {
      serviceItems = order.items;
    }
  } catch (e) {
    console.error("Failed to parse service items:", e);
  }
  
  // Determine if this is a client order
  const isClientOrder = order.customerType === "client" || 
                      (!!order.clientId && order.clientId !== '00000000-0000-0000-0000-000000000000');
  
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <h4 className="font-medium mb-2">Order Details</h4>
          <dl className="grid grid-cols-2 gap-1 text-sm">
            <dt className="text-muted-foreground">Order ID:</dt>
            <dd>{order.id}</dd>
            {order.reference_code && order.reference_code !== order.id && (
              <>
                <dt className="text-muted-foreground">Reference Code:</dt>
                <dd>{order.reference_code}</dd>
              </>
            )}
            <dt className="text-muted-foreground">Order Date:</dt>
            <dd>{order.orderDate}</dd>
            <dt className="text-muted-foreground">Delivery Date:</dt>
            <dd>{order.deliveryDate}</dd>
            <dt className="text-muted-foreground">Status:</dt>
            <dd>{order.status}</dd>
            <dt className="text-muted-foreground">Customer Type:</dt>
            <dd>{isClientOrder ? "Client" : "Walk-in"}</dd>
          </dl>
        </div>
        
        <div>
          <h4 className="font-medium mb-2">Payment Information</h4>
          <dl className="grid grid-cols-2 gap-1 text-sm">
            <dt className="text-muted-foreground">Total:</dt>
            <dd>{formatCurrency(order.amount)}</dd>
            <dt className="text-muted-foreground">Paid:</dt>
            <dd>{formatCurrency(order.paidAmount)}</dd>
            <dt className="text-muted-foreground">Balance:</dt>
            <dd>{formatCurrency(order.amount - order.paidAmount)}</dd>
          </dl>
        </div>
      </div>
      
      {order.lineItems && order.lineItems.length > 0 && (
        <div>
          <h4 className="font-medium mb-2">Items</h4>
          <div className={`bg-background rounded border p-2 ${isClientOrder ? 'border-blue-200' : 'border-green-200'}`}>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Item</TableHead>
                  <TableHead className="text-right">Qty</TableHead>
                  <TableHead className="text-right">Price</TableHead>
                  <TableHead className="text-right">Total</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {order.lineItems.map((item, idx) => (
                  <TableRow key={item.id || idx}>
                    <TableCell>
                      {item.name}
                      {item.treatmentDescription && (
                        <div className="text-xs text-muted-foreground mt-1">
                          {item.treatmentDescription}
                        </div>
                      )}
                    </TableCell>
                    <TableCell className="text-right">{item.quantity}</TableCell>
                    <TableCell className="text-right">
                      {formatCurrency(item.unitPrice)}
                    </TableCell>
                    <TableCell className="text-right">
                      {formatCurrency(item.total)}
                    </TableCell>
                  </TableRow>
                ))}
                
                {/* Show add-ons if applicable */}
                {(order.useDetergent || order.useFabricConditioner || order.useStainRemover) && (
                  <TableRow>
                    <TableCell colSpan={4} className="text-sm">
                      <strong>Add-ons:</strong>{' '}
                      {[
                        order.useDetergent ? `Detergent (${order.detergentType || 'Regular'})` : null,
                        order.useFabricConditioner ? `Fabric Conditioner (${order.conditionerType || 'Regular'})` : null,
                        order.useStainRemover ? 'Stain Removal' : null
                      ].filter(Boolean).join(', ')}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </div>
      )}
      
      {/* Show service information if no line items */}
      {(!order.lineItems || order.lineItems.length === 0) && order.serviceType && (
        <div>
          <h4 className="font-medium mb-2">
            {isClientOrder ? "Client Service Information" : "Walk-in Service Information"}
          </h4>
          <div className={`bg-background rounded border p-3 ${isClientOrder ? 'bg-blue-50' : 'bg-green-50'}`}>
            <div className="flex justify-between mb-2">
              <span className="font-medium">{order.serviceType.replace(/_/g, ' ').toUpperCase()} service</span>
              <span>{formatCurrency(order.subtotalBeforeVAT || order.amount)}</span>
            </div>
            
            {/* Display customer information for walk-in orders */}
            {!isClientOrder && (
              <div className="text-sm mb-2">
                <strong>Customer:</strong> {order.customer?.name} 
                {order.customer?.phone ? ` (${order.customer.phone})` : ''}
              </div>
            )}
            
            {/* Display weight/pieces information */}
            {(order.weightKilos || order.numberOfPieces) && (
              <div className="text-sm text-muted-foreground mb-2">
                {order.weightKilos ? `Weight: ${order.weightKilos} kg` : ''}
                {order.weightKilos && order.numberOfPieces ? ' · ' : ''}
                {order.numberOfPieces ? `Pieces: ${order.numberOfPieces}` : ''}
              </div>
            )}
            
            {/* Display service items if they exist */}
            {serviceItems && serviceItems.length > 0 && (
              <div className="mt-3 border-t pt-2">
                <p className="font-medium text-sm mb-1">Service Items:</p>
                <ul className="list-disc list-inside space-y-1 pl-2 text-sm">
                  {serviceItems.map((item, idx) => (
                    <li key={idx}>
                      {item.name || item.type} 
                      {item.quantity ? ` × ${item.quantity}` : ''} 
                      {item.unitPrice || item.price ? ` - ${formatCurrency(item.unitPrice || item.price)}` : ''}
                      {item.treatmentDescription && (
                        <span className="text-xs text-muted-foreground ml-1">
                          ({item.treatmentDescription})
                        </span>
                      )}
                    </li>
                  ))}
                </ul>
              </div>
            )}
            
            {/* Display total with VAT if applicable */}
            {order.vatAmount > 0 && (
              <div className="flex justify-between text-sm border-t pt-2 mt-2">
                <span>VAT:</span>
                <span>{formatCurrency(order.vatAmount)}</span>
              </div>
            )}
            
            {/* Display final total */}
            <div className="flex justify-between text-sm font-medium border-t pt-2 mt-2">
              <span>Total:</span>
              <span>{formatCurrency(order.amount)}</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

