
import { Order } from "@/types";
import { LineItemsTable } from "./components/LineItemsTable";
import { OrderDetails } from "./components/OrderDetails";
import { ServiceInformation } from "./components/ServiceInformation";
import { formatCurrency, parseServiceItems, isClientOrder } from "./utils/formatUtils";

interface TableExpandedContentProps {
  order: Order;
  onOrderDeleted?: () => void;
}

export function TableExpandedContent({ order, onOrderDeleted }: TableExpandedContentProps) {
  // Parse service items if available
  const serviceItems = parseServiceItems(order);
  
  // Determine if this is a client order
  const isClient = isClientOrder(order);
  
  return (
    <div className="space-y-4">
      <OrderDetails 
        order={order} 
        formatCurrency={formatCurrency} 
        isClientOrder={isClient} 
      />
      
      {order.lineItems && order.lineItems.length > 0 && (
        <LineItemsTable 
          order={order} 
          lineItems={order.lineItems} 
          formatCurrency={formatCurrency} 
          isClientOrder={isClient} 
        />
      )}
      
      {/* Show service information if no line items */}
      {(!order.lineItems || order.lineItems.length === 0) && order.serviceType && (
        <ServiceInformation 
          order={order} 
          serviceItems={serviceItems} 
          formatCurrency={formatCurrency} 
          isClientOrder={isClient} 
        />
      )}
    </div>
  );
}
