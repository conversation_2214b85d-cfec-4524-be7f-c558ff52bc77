
import { Badge } from "@/components/ui/badge";
import { Order, LineItem } from "@/types";
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table";

interface TableExpandedContentProps {
  order: Order;
  onOrderDeleted?: () => void;
}

export function TableExpandedContent({ order, onOrderDeleted }: TableExpandedContentProps) {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <h4 className="font-medium mb-2">Order Details</h4>
          <dl className="grid grid-cols-2 gap-1 text-sm">
            <dt className="text-muted-foreground">Order Date:</dt>
            <dd>{order.orderDate}</dd>
            <dt className="text-muted-foreground">Delivery Date:</dt>
            <dd>{order.deliveryDate}</dd>
            <dt className="text-muted-foreground">Status:</dt>
            <dd>{order.status}</dd>
          </dl>
        </div>
        
        <div>
          <h4 className="font-medium mb-2">Payment Information</h4>
          <dl className="grid grid-cols-2 gap-1 text-sm">
            <dt className="text-muted-foreground">Total:</dt>
            <dd>{new Intl.NumberFormat("en-PH", {
              style: "currency",
              currency: "PHP",
            }).format(order.amount)}</dd>
            <dt className="text-muted-foreground">Paid:</dt>
            <dd>{new Intl.NumberFormat("en-PH", {
              style: "currency",
              currency: "PHP",
            }).format(order.paidAmount)}</dd>
            <dt className="text-muted-foreground">Balance:</dt>
            <dd>{new Intl.NumberFormat("en-PH", {
              style: "currency",
              currency: "PHP",
            }).format(order.amount - order.paidAmount)}</dd>
          </dl>
        </div>
      </div>
      
      {order.lineItems && order.lineItems.length > 0 && (
        <div>
          <h4 className="font-medium mb-2">Items</h4>
          <div className="bg-background rounded border p-2">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Item</TableHead>
                  <TableHead className="text-right">Qty</TableHead>
                  <TableHead className="text-right">Price</TableHead>
                  <TableHead className="text-right">Total</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {order.lineItems.map((item, idx) => (
                  <TableRow key={item.id || idx}>
                    <TableCell>{item.name}</TableCell>
                    <TableCell className="text-right">{item.quantity}</TableCell>
                    <TableCell className="text-right">
                      {new Intl.NumberFormat("en-PH", {
                        style: "currency",
                        currency: "PHP",
                      }).format(item.unitPrice)}
                    </TableCell>
                    <TableCell className="text-right">
                      {new Intl.NumberFormat("en-PH", {
                        style: "currency",
                        currency: "PHP",
                      }).format(item.total)}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      )}
    </div>
  );
}
