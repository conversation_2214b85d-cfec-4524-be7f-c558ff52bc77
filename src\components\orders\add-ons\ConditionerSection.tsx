
import { FormField, FormItem, FormLabel, FormControl } from "@/components/ui/form";
import { UseFormReturn } from "react-hook-form";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { useState, useEffect } from "react";

interface ConditionerSectionProps {
  form: UseFormReturn<any>;
}

export function ConditionerSection({ form }: ConditionerSectionProps) {
  const [showQuantity, setShowQuantity] = useState(form.watch("conditionerType") !== "none");
  
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === "conditionerType") {
        setShowQuantity(value.conditionerType !== "none");
      }
    });
    return () => subscription.unsubscribe();
  }, [form]);
  
  return (
    <div className="space-y-3">
      <h3 className="font-medium">Fabric Conditioner</h3>
      
      <FormField
        control={form.control}
        name="conditionerType"
        render={({ field }) => (
          <FormItem className="space-y-2">
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                value={field.value}
                className="grid grid-cols-4 gap-2"
              >
                <div className="flex items-center space-x-2 rounded-md border p-2">
                  <RadioGroupItem value="none" id="conditioner-none" />
                  <Label htmlFor="conditioner-none" className="flex-1 cursor-pointer">None</Label>
                </div>
                <div className="flex items-center space-x-2 rounded-md border p-2">
                  <RadioGroupItem value="regular" id="conditioner-regular" />
                  <Label htmlFor="conditioner-regular" className="flex-1 cursor-pointer">Regular</Label>
                </div>
                <div className="flex items-center space-x-2 rounded-md border p-2">
                  <RadioGroupItem value="fresh" id="conditioner-fresh" />
                  <Label htmlFor="conditioner-fresh" className="flex-1 cursor-pointer">Fresh</Label>
                </div>
                <div className="flex items-center space-x-2 rounded-md border p-2">
                  <RadioGroupItem value="floral" id="conditioner-floral" />
                  <Label htmlFor="conditioner-floral" className="flex-1 cursor-pointer">Floral</Label>
                </div>
              </RadioGroup>
            </FormControl>
          </FormItem>
        )}
      />
      
      {showQuantity && (
        <FormField
          control={form.control}
          name="conditionerQuantity"
          render={({ field }) => (
            <FormItem className="space-y-1">
              <FormLabel>Quantity</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  min="1"
                  max="10"
                  {...field}
                  className="w-24"
                />
              </FormControl>
            </FormItem>
          )}
        />
      )}
    </div>
  );
}
