
import { AndroidSwitch } from "@/components/ui/android-switch";
import { Label } from "@/components/ui/label";
import { WavesIcon } from "lucide-react";

interface BeachTreatmentToggleProps {
  itemId: string;
  checked: boolean;
  onCheckedChange: (checked: boolean) => void;
}

export function BeachTreatmentToggle({
  itemId,
  checked,
  onCheckedChange
}: BeachTreatmentToggleProps) {
  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-2">
        <WavesIcon className="h-4 w-4 text-blue-500" />
        <Label htmlFor={`beach-${itemId}`} className="text-sm">Beach treatment (+15%)</Label>
      </div>
      <AndroidSwitch
        id={`beach-${itemId}`}
        checked={checked}
        onCheckedChange={onCheckedChange}
      />
    </div>
  );
}
