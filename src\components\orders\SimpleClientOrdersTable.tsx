
import React from "react";
import { Order } from "@/types";
import { 
  Table, 
  TableBody, 
  TableHeader, 
  TableRow, 
  TableHead, 
  TableCell 
} from "@/components/ui/table";
import { OrderStatusBadge } from "./OrderStatusBadge";
import { Button } from "@/components/ui/button";
import { Eye } from "lucide-react";
import { formatCurrency } from "@/lib/utils";

interface SimpleClientOrdersTableProps {
  orders: Order[];
  onViewOrder: (order: Order) => void;
  onOrderDeleted?: () => void;
}

export function SimpleClientOrdersTable({ orders, onViewOrder, onOrderDeleted }: SimpleClientOrdersTableProps) {
  // Filter to make sure we're only showing client orders
  // Include orders that have a client prefix in their ID (like TBMND-)
  const clientOrders = orders.filter(order => 
    order.customerType === "client" || 
    Boolean(order.clientId && order.clientId !== '00000000-0000-0000-0000-000000000000') ||
    (order.id.includes('-') && !order.id.startsWith('ORD-'))
  );
  
  // Log client orders count
  console.log(`SimpleClientOrdersTable - Total orders: ${orders.length} Client orders: ${clientOrders.length}`);
  
  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Order ID</TableHead>
            <TableHead>Client</TableHead>
            <TableHead>Date</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-right">Amount</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {clientOrders.length === 0 ? (
            <TableRow>
              <TableCell colSpan={6} className="text-center py-8">
                No client orders found
              </TableCell>
            </TableRow>
          ) : (
            clientOrders.map((order) => (
              <TableRow key={order.id}>
                <TableCell>{order.id}</TableCell>
                <TableCell>
                  <div>
                    <div className="font-medium">{order.customer?.name || "Unknown Client"}</div>
                    <div className="text-sm text-muted-foreground">{order.customer?.phone || ""}</div>
                  </div>
                </TableCell>
                <TableCell>{order.orderDate}</TableCell>
                <TableCell>
                  <OrderStatusBadge status={order.status} />
                </TableCell>
                <TableCell className="text-right">
                  {formatCurrency(order.amount)}
                </TableCell>
                <TableCell className="text-right">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onViewOrder(order)}
                  >
                    <Eye className="h-4 w-4 mr-1" />
                    View
                  </Button>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
}
