
import { useClientsData } from "@/hooks/useClientsData";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Building2 } from "lucide-react";
import { ClientData } from "@/hooks/useClientsData";
import { cn } from "@/lib/utils";
import { useState, useEffect } from "react";

interface ClientButtonsProps {
  clientId: string;
  setClientId: (id: string) => void;
  setClientName?: (name: string) => void;
  setClientPrefix?: (prefix: string) => void;
  setClientContactPerson?: (contactPerson: string) => void;
  setClientPhone?: (phone: string) => void;
}

export function ClientButtons({ 
  clientId, 
  setClientId,
  setClientName,
  setClientPrefix,
  setClientContactPerson,
  setClientPhone
}: ClientButtonsProps) {
  const { clients, loading, error } = useClientsData();
  const [localClientId, setLocalClientId] = useState(clientId);

  // Handle selected client synchronization
  useEffect(() => {
    if (clientId !== localClientId) {
      setLocalClientId(clientId);
    }
  }, [clientId]);

  const handleSelectClient = (client: ClientData) => {
    console.log("ClientButtons: Selecting client:", client);
    
    // Use local state to avoid re-renders
    setLocalClientId(client.id);
    
    // Update parent state in a single batch
    setClientId(client.id);
    
    if (setClientName && client.name) {
      setClientName(client.name);
    }
    
    if (setClientPrefix && client.prefix) {
      setClientPrefix(client.prefix);
    }
    
    if (setClientContactPerson && client.contact_person) {
      setClientContactPerson(client.contact_person);
    }
    
    if (setClientPhone && client.phone) {
      setClientPhone(client.phone);
    }
  };

  if (loading) {
    return <div className="text-muted-foreground">Loading clients...</div>;
  }

  if (error) {
    return <div className="text-destructive">{error}</div>;
  }

  if (!clients.length) {
    return <div className="text-muted-foreground">No clients available</div>;
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
      {clients.map((client) => (
        <Button
          key={client.id}
          variant={localClientId === client.id ? "default" : "outline"}
          onClick={() => handleSelectClient(client)}
          className={cn(
            "flex items-center justify-start gap-2 h-auto py-3 px-4 text-left",
            localClientId === client.id ? "bg-primary text-primary-foreground" : ""
          )}
          data-testid={`client-button-${client.id}`}
          type="button" // Explicitly set button type to prevent form submission
        >
          <Building2 className="h-4 w-4 shrink-0" />
          <div className="flex flex-col items-start gap-0.5 w-full overflow-hidden">
            <span className="font-medium truncate w-full">{client.name}</span>
            <div className="text-xs opacity-70 truncate w-full">
              {client.prefix && <span>({client.prefix})</span>}
              {client.contact_person && <span> • {client.contact_person}</span>}
            </div>
          </div>
        </Button>
      ))}
    </div>
  );
}
