
import { Order } from "@/types";

export function parseServiceItems(order: Order): any[] {
  let serviceItems: any[] = [];
  try {
    // Case 1: Items stored as a JSON string
    if (typeof order.items === 'string' && order.items) {
      try {
        serviceItems = JSON.parse(order.items);
        console.log("Successfully parsed items string:", serviceItems);
      } catch (e) {
        console.error("Failed to parse service items string:", e);
      }
    } 
    // Case 2: Items already exist as an array
    else if (Array.isArray(order.items)) {
      serviceItems = order.items;
      console.log("Items already in array format:", serviceItems);
    }
    // Case 3: Order has lineItems
    else if (Array.isArray(order.lineItems) && order.lineItems.length > 0) {
      serviceItems = order.lineItems;
      console.log("Using lineItems as service items:", serviceItems);
    }
    // Case 4: Order has selectedClientItems
    else if (order.selectedClientItems && Array.isArray(order.selectedClientItems) && order.selectedClientItems.length > 0) {
      serviceItems = order.selectedClientItems.map(item => ({
        name: item.name || '',
        quantity: item.quantity || 1,
        unitPrice: item.unit_price || 0,
        total: (item.unit_price || 0) * (item.quantity || 1)
      }));
      console.log("Extracted from selectedClientItems:", serviceItems);
    }
    
    // Additional logging to debug item structure
    if (serviceItems.length > 0) {
      console.log("First service item example:", JSON.stringify(serviceItems[0]));
    } else {
      // Case 5: Order might have service data in other formats
      // Try to extract from service-specific fields if items array is empty
      if (order.serviceType) {
        // Create a default service item based on service type
        serviceItems = [{
          name: order.serviceType.replace(/_/g, ' ').toUpperCase(),
          quantity: 1,
          unitPrice: order.subtotalBeforeVAT || order.amount || 0,
          treatmentDescription: `${order.weightKilos || 0}kg - ${order.numberOfPieces || 1} pieces`
        }];
        console.log("Created default service item from service type:", serviceItems);
      }
    }
    
    // Ensure we have an array
    if (!Array.isArray(serviceItems)) {
      console.error("Service items is not an array:", serviceItems);
      return [];
    }

    // Filter out empty or invalid items and map to a consistent format
    return serviceItems.filter(item => item && (item.name || item.type)).map(item => ({
      name: item.name || item.type || 'Unnamed Service',
      quantity: item.quantity || 1,
      unitPrice: item.unitPrice || item.unit_price || item.price || 0,
      treatmentDescription: item.treatmentDescription || item.treatment_description || '',
      total: item.total || (item.quantity || 1) * (item.unitPrice || item.unit_price || item.price || 0)
    }));
    
  } catch (e) {
    console.error("Failed to parse service items:", e);
    return [];
  }
}

export function isClientOrder(order: Order): boolean {
  return order.customerType === "client" || 
         (!!order.clientId && order.clientId !== '00000000-0000-0000-0000-000000000000');
}
