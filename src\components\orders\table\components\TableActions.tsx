
import React from "react";
import { Order } from "@/types";
import { Button } from "@/components/ui/button";
import { Edit, FileText, Printer } from "lucide-react";
import { usePrinterContext } from "@/contexts/PrinterContext";
import { useToast } from "@/hooks/use-toast";

interface TableActionsProps {
  order: Order;
  onViewOrder?: (order: Order) => void;
}

export function TableActions({ order, onViewOrder }: TableActionsProps) {
  const { printerStatus, printReceipt, printJobOrder } = usePrinterContext();
  const { toast } = useToast();
  
  const handlePrintReceipt = async (e: React.MouseEvent) => {
    e.stopPropagation();
    
    if (printerStatus !== 'connected') {
      toast({
        title: "Printer Not Connected",
        description: "Please connect a printer first",
        variant: "destructive"
      });
      return;
    }
    
    const success = await printReceipt(order);
    
    if (!success) {
      toast({
        title: "Print Failed",
        description: "Failed to print receipt",
        variant: "destructive"
      });
    }
  };
  
  const handlePrintJobOrder = async (e: React.MouseEvent) => {
    e.stopPropagation();
    
    if (printerStatus !== 'connected') {
      toast({
        title: "Printer Not Connected",
        description: "Please connect a printer first",
        variant: "destructive"
      });
      return;
    }
    
    const success = await printJobOrder(order);
    
    if (!success) {
      toast({
        title: "Print Failed",
        description: "Failed to print job order",
        variant: "destructive"
      });
    }
  };

  return (
    <div className="flex justify-end gap-2">
      <Button 
        size="icon" 
        variant="outline"
        onClick={handlePrintReceipt}
        title="Print Receipt"
      >
        <Printer className="h-4 w-4" />
      </Button>
      <Button 
        size="icon" 
        variant="outline"
        onClick={handlePrintJobOrder}
        title="Print Job Order"
      >
        <FileText className="h-4 w-4" />
      </Button>
      <Button 
        size="icon" 
        variant="outline" 
        onClick={() => onViewOrder?.(order)}
        title="Edit Order"
      >
        <Edit className="h-4 w-4" />
      </Button>
    </div>
  );
}
