
import React from "react";
import { AddOrderDialog } from "@/components/orders/AddOrderDialog";
import { ViewOrderDialog } from "@/components/orders/ViewOrderDialog";
import { Order } from "@/types";

interface OrdersDialogsProps {
  isAddOrderOpen: boolean;
  setIsAddOrderOpen: (open: boolean) => void;
  refreshOrders: () => Promise<void>;
  activeTab: string;
  selectedOrder: Order | null;
  isViewOrderOpen: boolean;
  setIsViewOrderOpen: (open: boolean) => void;
  onStatusChange: (orderId: string, newStatus: string) => Promise<void>;
}

export function OrdersDialogs({
  isAddOrderOpen,
  setIsAddOrderOpen,
  refreshOrders,
  activeTab,
  selectedOrder,
  isViewOrderOpen,
  setIsViewOrderOpen,
  onStatusChange
}: OrdersDialogsProps) {
  return (
    <>
      <AddOrderDialog 
        open={isAddOrderOpen} 
        onOpenChange={setIsAddOrderOpen} 
        onOrderAdded={refreshOrders} 
        initialOrderType={activeTab === "clients" ? "client" : "walk-in"} 
      />

      {selectedOrder && (
        <ViewOrderDialog 
          order={selectedOrder} 
          open={isViewOrderOpen} 
          onOpenChange={setIsViewOrderOpen} 
          onStatusChange={onStatusChange} 
        />
      )}
    </>
  );
}
