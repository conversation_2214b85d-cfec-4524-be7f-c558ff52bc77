
import React from "react";
import { AddOrderDialog } from "@/components/orders/AddOrderDialog";
import { ViewOrderDialog } from "@/components/orders/ViewOrderDialog";
import { Order } from "@/types";

interface OrdersDialogsProps {
  isAddOrderOpen: boolean;
  setIsAddOrderOpen: (open: boolean) => void;
  refreshOrders: () => Promise<void>;
  activeTab: string;
  selectedOrder: Order | null;
  isViewOrderOpen: boolean;
  setIsViewOrderOpen: (open: boolean) => void;
  onStatusChange: (orderId: string, newStatus: string) => Promise<void>;
  initialOrderType: "walk-in" | "client";
}

export function OrdersDialogs({
  isAddOrderOpen,
  setIsAddOrderOpen,
  refreshOrders,
  activeTab,
  selectedOrder,
  isViewOrderOpen,
  setIsViewOrderOpen,
  onStatusChange,
  initialOrderType
}: OrdersDialogsProps) {
  return (
    <>
      <AddOrderDialog 
        open={isAddOrderOpen} 
        onOpenChange={setIsAddOrderOpen} 
        onOrderAdded={refreshOrders} 
        initialOrderType={initialOrderType} 
      />

      {selectedOrder && (
        <ViewOrderDialog 
          order={selectedOrder} 
          open={isViewOrderOpen} 
          onOpenChange={setIsViewOrderOpen} 
          onStatusChange={onStatusChange} 
        />
      )}
    </>
  );
}
