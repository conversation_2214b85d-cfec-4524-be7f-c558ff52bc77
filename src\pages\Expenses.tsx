
import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { ExpensesTable } from "@/components/expenses/ExpensesTable";
import { ExpenseSummaryCards } from "@/components/expenses/ExpenseSummaryCards";
import { AddExpenseDialog } from "@/components/expenses/AddExpenseDialog";
import { useExpenseData } from "@/components/expenses/hooks/useExpenseData";
import { useAuth } from "@/contexts/auth";
import { BreadcrumbNav, BreadcrumbItem } from "@/components/layout/BreadcrumbNav";

export default function Expenses() {
  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const { expenses, summary, isLoading, addExpense } = useExpenseData();
  const { userRole } = useAuth();
  
  // Only admins and staff can add expenses
  const canAddExpense = userRole === 'admin' || userRole === 'staff';
  
  // Define breadcrumb items
  const breadcrumbItems: BreadcrumbItem[] = [
    { label: "Expenses", path: "/expenses" }
  ];

  return (
    <div className="container py-4 md:py-6 space-y-4 md:space-y-6">
      {/* Breadcrumb navigation */}
      <BreadcrumbNav items={breadcrumbItems} />
      
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h1 className="text-xl md:text-2xl font-bold">Expense Tracker</h1>
        
        {canAddExpense && (
          <Button 
            onClick={() => setAddDialogOpen(true)}
            size="sm"
            className="h-9 sm:h-10"
          >
            <Plus className="mr-2 h-4 w-4" /> Add Expense
          </Button>
        )}
      </div>
      
      {summary && (
        <div className="grid gap-4 md:gap-6">
          <ExpenseSummaryCards summary={summary} />
        </div>
      )}
      
      <Card>
        <CardContent className="p-0 pt-6">
          <h2 className="text-lg font-semibold px-4 md:px-6 pb-4">Expense Records</h2>
          <ExpensesTable expenses={expenses} isLoading={isLoading} />
        </CardContent>
      </Card>

      <AddExpenseDialog 
        open={addDialogOpen} 
        onOpenChange={setAddDialogOpen}
        onExpenseAdded={addExpense}
      />
    </div>
  );
}
