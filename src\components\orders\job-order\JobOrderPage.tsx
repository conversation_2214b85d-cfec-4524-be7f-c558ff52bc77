
import React from "react";
import { JobOrderHeader } from "./JobOrderHeader";
import { JobOrderFilters } from "./JobOrderFilters";
import { useOrder } from "@/contexts/OrderContext";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { EmptyState } from "@/components/ui/empty-state";
import { FileWarning } from "lucide-react";
import { ClientOrdersList } from "./ClientOrdersList";
import { useJobOrders } from "@/hooks/orders/useJobOrders";
import { useJobOrderFilters } from "@/hooks/orders/useJobOrderFilters";
import { OrdersNavigation } from "@/components/orders/page/OrdersNavigation";

export default function JobOrderPage() {
  const { refreshOrder } = useOrder();
  
  // Use our custom hooks
  const { orders, isLoading, error, refreshOrders } = useJobOrders();
  const { searchTerm, setSearchTerm, statusFilter, setStatusFilter, filteredOrders } = 
    useJobOrderFilters(orders);

  const handleRefresh = () => {
    console.log("Refreshing job orders");
    refreshOrders();
    refreshOrder(); 
  };

  return (
    <div className="space-y-6">
      <JobOrderHeader onRefresh={handleRefresh} />
      
      <OrdersNavigation />
      
      <JobOrderFilters
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        statusFilter={statusFilter}
        setStatusFilter={setStatusFilter}
      />

      {isLoading ? (
        <LoadingSpinner message="Loading orders..." />
      ) : error ? (
        <EmptyState
          icon={<FileWarning className="h-12 w-12 text-muted-foreground" />}
          title="Failed to load orders"
          description="There was an error loading the job orders. Please try refreshing."
          action={{
            label: "Try Again",
            onClick: handleRefresh,
          }}
        />
      ) : (
        <div>
          <ClientOrdersList 
            orders={filteredOrders}
            searchTerm={searchTerm} 
            statusFilter={statusFilter} 
          />
        </div>
      )}
    </div>
  );
}
