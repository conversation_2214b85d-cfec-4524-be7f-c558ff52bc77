
import React, { useState } from "react";
import { JobOrderHeader } from "./JobOrderHeader";
import { useOrder } from "@/contexts/OrderContext";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { EmptyState } from "@/components/ui/empty-state";
import { FileWarning, Filter, Plus } from "lucide-react";
import { ClientOrdersList } from "./ClientOrdersList";
import { useJobOrders } from "@/hooks/orders/useJobOrders";
import { OrdersNavigation } from "@/components/orders/page/OrdersNavigation";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { OrdersSearch } from "@/components/orders/page/OrdersSearch";
import { Button } from "@/components/ui/button";
import { JobOrderFilters } from "./JobOrderFilters";
import { 
  Popover,
  PopoverContent,
  PopoverTrigger 
} from "@/components/ui/popover";
import { AddOrderDialog } from "@/components/orders/AddOrderDialog";
import { useJobOrderFilters } from "@/hooks/orders/useJobOrderFilters";

export default function JobOrderPage() {
  const { refreshOrder } = useOrder();
  const [activeTab, setActiveTab] = useState<string>("client");
  const [filtersOpen, setFiltersOpen] = useState(false);
  const [isAddOrderOpen, setIsAddOrderOpen] = useState(false);
  const [orderType, setOrderType] = useState<"walk-in" | "client">("walk-in");
  
  // Use our custom hooks
  const { orders, isLoading, error, refreshOrders } = useJobOrders();
  
  // Use the job order filters hook
  const {
    searchTerm,
    setSearchTerm,
    statusFilter,
    setStatusFilter, 
    dateFilter,
    setDateFilter,
    clientFilter,
    setClientFilter,
    orderType: filterOrderType,
    setOrderType: setFilterOrderType,
    filteredOrders
  } = useJobOrderFilters(orders);
  
  const handleRefresh = () => {
    console.log("Refreshing job orders");
    refreshOrders();
    refreshOrder(); 
  };

  // Reset filters function
  const resetFilters = () => {
    setStatusFilter("all");
    setDateFilter(undefined);
    setClientFilter("all");
  };
  
  // Handle order creation based on type
  const handleAddOrder = (type: "walk-in" | "client") => {
    setOrderType(type);
    setIsAddOrderOpen(true);
  };
  
  // Correctly filter orders based on customer type
  const clientOrders = filteredOrders.filter(order => order.customerType === "client");
  const walkInOrders = filteredOrders.filter(order => order.customerType === "walk-in");

  // Check if any filters are active
  const hasActiveFilters = statusFilter !== "all" || dateFilter !== undefined || clientFilter !== "all";
  
  // Count active filters
  const activeFiltersCount = 
    (statusFilter !== "all" ? 1 : 0) + 
    (dateFilter ? 1 : 0) + 
    (clientFilter !== "all" ? 1 : 0);

  return (
    <div className="space-y-6">
      <JobOrderHeader onRefresh={handleRefresh} />
      
      <OrdersNavigation />
      
      <div className="space-y-4">
        <div className="flex flex-col md:flex-row justify-between gap-4">
          <div className="w-full md:w-2/3">
            <OrdersSearch 
              searchTerm={searchTerm}
              onSearch={setSearchTerm}
            />
          </div>
          <div className="flex gap-2">
            <div className="w-full">
              <Popover open={filtersOpen} onOpenChange={setFiltersOpen}>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="w-full flex justify-between items-center">
                    <div className="flex items-center">
                      <Filter className="h-4 w-4 mr-2" />
                      <span>Filters</span>
                    </div>
                    {hasActiveFilters && (
                      <span className="flex h-5 w-5 rounded-full bg-primary text-[0.625rem] text-primary-foreground items-center justify-center font-medium">
                        {activeFiltersCount}
                      </span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80 p-0" align="end">
                  <JobOrderFilters
                    searchTerm={searchTerm}
                    setSearchTerm={setSearchTerm}
                    statusFilter={statusFilter}
                    setStatusFilter={setStatusFilter}
                    dateFilter={dateFilter}
                    setDateFilter={setDateFilter}
                    clientFilter={clientFilter}
                    setClientFilter={setClientFilter}
                    resetFilters={resetFilters}
                  />
                </PopoverContent>
              </Popover>
            </div>
            <Button onClick={() => handleAddOrder("walk-in")} className="hidden md:flex items-center">
              <Plus className="h-4 w-4 mr-2" />
              New Order
            </Button>
          </div>
        </div>

        {/* Mobile add order button */}
        <div className="md:hidden">
          <Button onClick={() => handleAddOrder("walk-in")} className="w-full flex items-center justify-center">
            <Plus className="h-4 w-4 mr-2" />
            New Order
          </Button>
        </div>

        {isLoading ? (
          <LoadingSpinner message="Loading orders..." />
        ) : error ? (
          <EmptyState
            icon={<FileWarning className="h-12 w-12 text-muted-foreground" />}
            title="Failed to load orders"
            description="There was an error loading the job orders. Please try refreshing."
            action={{
              label: "Try Again",
              onClick: handleRefresh,
            }}
          />
        ) : (
          <div>
            <Tabs defaultValue="client" value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="bg-muted/40 w-full md:w-auto mb-4">
                <TabsTrigger value="client">Client Orders ({clientOrders.length})</TabsTrigger>
                <TabsTrigger value="walkin">Walk-in Orders ({walkInOrders.length})</TabsTrigger>
              </TabsList>
              
              {activeTab === "client" ? (
                <ClientOrdersList 
                  orders={clientOrders}
                  searchTerm={searchTerm} 
                  statusFilter={statusFilter} 
                />
              ) : (
                <ClientOrdersList 
                  orders={walkInOrders}
                  searchTerm={searchTerm} 
                  statusFilter={statusFilter} 
                />
              )}
            </Tabs>
          </div>
        )}
      </div>

      {/* Order Dialog */}
      <AddOrderDialog
        open={isAddOrderOpen}
        onOpenChange={setIsAddOrderOpen}
        onOrderAdded={refreshOrders}
        initialOrderType={orderType}
      />
    </div>
  );
}
