
import React from "react";
import { Row } from "@tanstack/react-table";
import { TableRow, TableCell } from "@/components/ui/table";
import { Order } from "@/types";
import { TableExpandedContent } from "../TableExpandedContent";

interface ExpandedRowProps {
  row: Row<Order>;
  onStatusChange: (orderId: string, newStatus: string) => Promise<void>;
  onPrintOrder: (order: Order) => void;
  onOrderDeleted?: () => void;
}

export function ExpandedRow({
  row,
  onStatusChange,
  onPrintOrder,
  onOrderDeleted
}: ExpandedRowProps) {
  const order = row.original;

  return (
    <TableRow>
      <TableCell colSpan={8} className="p-0">
        <div className="p-4 bg-muted/30">
          <TableExpandedContent 
            order={order} 
            onOrderDeleted={onOrderDeleted}
          />
        </div>
      </TableCell>
    </TableRow>
  );
}
