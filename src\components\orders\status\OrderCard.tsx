
import { memo, useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Order, Stage } from "./types";
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { updateOrderStatus } from "@/services/orders";
import { useToast } from "@/hooks/use-toast";

interface OrderCardProps {
  order: Order;
  currentStage: Stage;
  stages: Stage[];
  onStatusChange?: () => void;
}

export const OrderCard = memo(function OrderCard({ 
  order, 
  currentStage,
  stages,
  onStatusChange
}: OrderCardProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const { toast } = useToast();
  
  // Calculate the total here rather than re-computing it for each render
  const totalItems = order.items.length;
  
  const handleStatusUpdate = async (newStatus: string) => {
    try {
      setIsUpdating(true);
      const success = await updateOrderStatus(order.uuid, newStatus);
      
      if (success) {
        toast({
          title: "Status Updated",
          description: `Order ${order.id} status changed to ${newStatus}`,
        });
        setIsDialogOpen(false);
        if (onStatusChange) onStatusChange();
      } else {
        toast({
          title: "Update Failed",
          description: "Failed to update order status.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error updating status:", error);
      toast({
        title: "Error",
        description: "An error occurred while updating the status.",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };
  
  return (
    <>
      <Card 
        className="bg-white border-none shadow-sm hover:shadow-md transition-shadow cursor-pointer" 
        onClick={() => setIsDialogOpen(true)}
      >
        <CardHeader className="pb-2">
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="text-md font-medium">{order.reference_code || order.id}</CardTitle>
              <p className="text-sm text-muted-foreground">{order.customer}</p>
            </div>
            <div className={`px-3 py-1 rounded-full text-xs font-medium ${currentStage.color}`}>
              {currentStage.name}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div>
              <p className="text-xs text-muted-foreground">Items: {totalItems}</p>
              <ul className="text-sm">
                {order.items.slice(0, 3).map((item, idx) => (
                  <li key={`${order.id}-item-${idx}`} className="flex justify-between">
                    <span>{item.name} x{item.qty}</span>
                    <span>₱{item.price * item.qty}</span>
                  </li>
                ))}
                {order.items.length > 3 && (
                  <li className="text-xs text-muted-foreground italic">
                    +{order.items.length - 3} more items
                  </li>
                )}
              </ul>
            </div>
            
            <div className="pt-2 border-t">
              <div className="flex justify-between">
                <span className="text-sm font-medium">Total Amount:</span>
                <span className="text-sm font-bold">₱{order.totalAmount.toFixed(2)}</span>
              </div>
            </div>
            
            <div className="pt-2 border-t text-xs text-gray-500">
              <p>Last updated: {order.timestamp}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Update Order Status</DialogTitle>
          </DialogHeader>
          
          <div className="py-4">
            <h3 className="font-medium mb-2">Order: {order.reference_code || order.id}</h3>
            <p className="text-sm text-muted-foreground mb-4">{order.customer}</p>
            
            <div className="text-sm mb-4">
              Current Status: <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${currentStage.color}`}>
                {currentStage.name}
              </span>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium">Change status to:</h4>
              <div className="grid grid-cols-2 gap-2 mt-2">
                {stages.filter(stage => stage.id !== currentStage.id).map(stage => (
                  <Button 
                    key={stage.id}
                    variant="outline"
                    className={`${stage.color} justify-start`}
                    onClick={() => handleStatusUpdate(stage.id)}
                    disabled={isUpdating}
                  >
                    {stage.name}
                  </Button>
                ))}
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
});
