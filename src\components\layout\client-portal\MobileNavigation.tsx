
import { Link } from 'react-router-dom';
import { Package, FileText, User, LogOut, BarChart } from 'lucide-react';
import { User as UserType } from '@supabase/supabase-js';

interface MobileNavigationProps {
  isOpen: boolean;
  onClose: () => void;
  onLogout: () => void;
  pathname: string;
  user: UserType | null;
}

export function MobileNavigation({ isOpen, onClose, onLogout, pathname, user }: MobileNavigationProps) {
  if (!isOpen) return null;
  
  // Restricted navigation items - only show allowed pages
  const navItems = [
    { href: '/orders', icon: Package, text: 'Orders' },
    { href: '/items', icon: FileText, text: 'Service Items' },
    { href: '/reports', icon: BarChart, text: 'Reports' },
  ];
  
  return (
    <div className="md:hidden bg-[#1E293B] border-b border-gray-700">
      <nav className="px-2 pt-2 pb-3">
        {navItems.map((item) => (
          <Link
            key={item.href}
            to={item.href}
            className={`flex items-center px-3 py-2 rounded-md mb-1 ${
              pathname === item.href
                ? 'bg-[#2A2F3C] text-white'
                : 'text-gray-300 hover:bg-[#2A2F3C] hover:text-white'
            }`}
            onClick={onClose}
          >
            <item.icon className="mr-3 h-5 w-5" />
            {item.text}
          </Link>
        ))}
        
        {/* Mobile profile buttons */}
        <div className="mt-4 pt-4 border-t border-slate-700">
          <Link
            to="/profile"
            className="flex items-center px-3 py-2 rounded-md mb-1 text-gray-300 hover:bg-[#2A2F3C] hover:text-white"
            onClick={onClose}
          >
            <User className="mr-3 h-5 w-5" />
            Profile
          </Link>
          
          <button
            className="flex w-full items-center px-3 py-2 rounded-md mb-1 text-gray-300 hover:bg-[#2A2F3C] hover:text-white"
            onClick={() => {
              onClose();
              onLogout();
            }}
          >
            <LogOut className="mr-3 h-5 w-5" />
            Logout
          </button>
        </div>
        
        <p className="text-xs text-gray-400 text-center mt-4 pb-2">
          © 2025 Powered by PhilVirtualOffice
        </p>
      </nav>
    </div>
  );
}
