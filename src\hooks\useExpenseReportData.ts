
import { useState, useEffect } from 'react';
import { DateRange } from 'react-day-picker';
import { supabase } from '@/integrations/supabase/client';
import { ExpenseCategory } from '@/types/expense';
import { format } from 'date-fns';

export interface ExpenseReportData {
  id: string;
  date: string;
  description: string;
  amount: number;
  category: ExpenseCategory;
  paymentMethod: string;
}

export interface ExpenseSummaryData {
  totalExpenses: number;
  expensesByCategory: {
    name: string;
    value: number;
    percentage: number;
  }[];
}

export function useExpenseReportData(dateRange: DateRange | undefined) {
  const [isLoading, setIsLoading] = useState(true);
  const [expenseData, setExpenseData] = useState<ExpenseReportData[]>([]);
  const [summaryData, setSummaryData] = useState<ExpenseSummaryData>({
    totalExpenses: 0,
    expensesByCategory: []
  });

  // Format date for Supabase query
  const formatDateForQuery = (date: Date): string => {
    return format(date, 'yyyy-MM-dd');
  };

  // Calculate summary data
  const calculateSummary = (expenses: ExpenseReportData[]) => {
    const totalExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0);
    
    // Group expenses by category and calculate totals
    const categoryMap = new Map<string, number>();
    
    expenses.forEach(expense => {
      const currentAmount = categoryMap.get(expense.category) || 0;
      categoryMap.set(expense.category, currentAmount + expense.amount);
    });
    
    // Convert to array format needed for charts
    const expensesByCategory = Array.from(categoryMap.entries()).map(([name, value]) => ({
      name: name.charAt(0).toUpperCase() + name.slice(1), // Capitalize first letter
      value,
      percentage: totalExpenses > 0 ? (value / totalExpenses) * 100 : 0
    }));
    
    return {
      totalExpenses,
      expensesByCategory
    };
  };

  useEffect(() => {
    const fetchExpenseData = async () => {
      setIsLoading(true);
      
      try {
        let query = supabase
          .from('expenses')
          .select('*');
        
        // Apply date filters if provided
        if (dateRange?.from) {
          query = query.gte('date', formatDateForQuery(dateRange.from));
        }
        
        if (dateRange?.to) {
          query = query.lte('date', formatDateForQuery(dateRange.to));
        }
        
        const { data, error } = await query.order('date', { ascending: false });
        
        if (error) {
          console.error('Error fetching expense data:', error);
          throw error;
        }
        
        if (data) {
          const formattedData: ExpenseReportData[] = data.map(expense => ({
            id: expense.id,
            date: expense.date,
            description: expense.description,
            amount: expense.amount,
            category: expense.category as ExpenseCategory,
            paymentMethod: expense.payment_method
          }));
          
          setExpenseData(formattedData);
          setSummaryData(calculateSummary(formattedData));
        }
      } catch (error) {
        console.error('Error fetching expense data:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchExpenseData();
  }, [dateRange]);

  return { isLoading, expenseData, summaryData };
}
