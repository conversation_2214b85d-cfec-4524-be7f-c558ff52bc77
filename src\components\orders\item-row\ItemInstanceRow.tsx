
import React from "react";
import { TableRow, TableCell } from "@/components/ui/table";
import { ClientItemWithQuantity } from "../OrderFormTypes";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Copy, ChevronDown, ChevronUp, X, Plus, Minus } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { calculateItemTotalWithTreatments } from "@/utils/priceCalculations";
import { TreatmentOptions } from "../TreatmentOptions";
import { ItemQuantityControls } from "./ItemQuantityControls";
import { ItemActionButtons } from "./ItemActionButtons";

interface ItemInstanceRowProps {
  instance: ClientItemWithQuantity;
  isExpanded: boolean;
  toggleExpand: (instanceId: string) => void;
  onQuantityChange: (instanceId: string, quantity: number) => void;
  onTreatmentChange: (instanceId: string, treatmentUpdates: Partial<ClientItemWithQuantity['treatments']>) => void;
  onDuplicate: (instanceId: string) => void;
  onRemove: (instanceId: string) => void;
  isMobile: boolean;
}

export function ItemInstanceRow({
  instance,
  isExpanded,
  toggleExpand,
  onQuantityChange,
  onTreatmentChange,
  onDuplicate,
  onRemove,
  isMobile
}: ItemInstanceRowProps) {
  const itemTotal = calculateItemTotalWithTreatments(instance);
  
  // Get treatment summary for badge display
  const treatmentSummary = getTreatmentSummary(instance);
  const hasTreatments = treatmentSummary.length > 0;

  return (
    <TableRow key={instance.instanceId}>
      <TableCell className={isMobile ? "px-2" : ""}>
        {/* Indentation to show this is an instance */}
        <div className="pl-8"></div>
      </TableCell>
      <TableCell className={`relative ${isMobile ? "px-2" : ""}`}>
        <div className="flex items-center">
          <div className="flex-1">
            <span className="text-sm">{instance.name}</span>
            
            {/* Treatment badges - show only if treatments exist and not expanded */}
            {hasTreatments && !isExpanded && (
              <div className="mt-1 flex flex-wrap gap-1">
                {treatmentSummary.map((treatment, idx) => (
                  <Badge key={idx} variant="outline" className="text-xs">
                    {treatment}
                  </Badge>
                ))}
              </div>
            )}
          </div>
          
          {/* Expand/Collapse button */}
          <Button 
            variant="ghost" 
            size="sm" 
            type="button"
            onClick={(e) => {
              e.preventDefault();
              toggleExpand(instance.instanceId);
            }}
          >
            {isExpanded ? 
              <ChevronUp className="h-4 w-4" /> : 
              <ChevronDown className="h-4 w-4" />
            }
          </Button>
        </div>
        
        {/* Expanded view with treatment options */}
        {isExpanded && (
          <div className="mt-2 p-3 border rounded-md bg-muted/20 overflow-visible w-full max-w-[300px]">
            <TreatmentOptions
              treatments={instance.treatments}
              onTreatmentChange={(updates) => onTreatmentChange(instance.instanceId, updates)}
            />
          </div>
        )}
      </TableCell>
      <TableCell className={isMobile ? "w-[140px] px-1" : "w-[240px]"}>
        <ItemQuantityControls 
          instance={instance}
          onQuantityChange={onQuantityChange}
        />
        
        <ItemActionButtons 
          instanceId={instance.instanceId} 
          onDuplicate={onDuplicate}
          onRemove={onRemove}
        />
      </TableCell>
      <TableCell className={`text-right ${isMobile ? "px-2" : ""}`}>
        ₱{itemTotal.toFixed(2)}
      </TableCell>
    </TableRow>
  );
}

function getTreatmentSummary(instance: ClientItemWithQuantity): string[] {
  const treatmentSummary = [];
  const treatments = instance.treatments || {};
  
  if (treatments.useStainRemoval) treatmentSummary.push("Stain removal");
  if (treatments.useBeachTreatment) treatmentSummary.push("Beach treatment");
  if (treatments.detergentType !== "none") treatmentSummary.push(`${treatments.detergentType} detergent`);
  if (treatments.conditionerType !== "none") treatmentSummary.push(`${treatments.conditionerType} conditioner`);
  
  return treatmentSummary;
}
