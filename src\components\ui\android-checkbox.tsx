import React from 'react';
import { Check } from 'lucide-react';
import { cn } from '@/lib/utils';

interface AndroidCheckboxProps {
  checked: boolean;
  onCheckedChange: (checked: boolean) => void;
  disabled?: boolean;
  className?: string;
  id?: string;
}

export function AndroidCheckbox({
  checked,
  onCheckedChange,
  disabled = false,
  className,
  id
}: AndroidCheckboxProps) {
  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!disabled) {
      console.log('AndroidCheckbox clicked:', { checked, id });
      onCheckedChange(!checked);
    }
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    e.preventDefault();
    if (!disabled) {
      console.log('AndroidCheckbox touch end:', { checked, id });
      onCheckedChange(!checked);
    }
  };

  return (
    <button
      type="button"
      role="checkbox"
      aria-checked={checked}
      id={id}
      className={cn(
        // Base styles
        "relative flex h-6 w-6 shrink-0 items-center justify-center rounded border-2 transition-all duration-200",
        "focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2",
        "touch-action-manipulation",
        "active:scale-95",
        // Checked state
        checked
          ? "border-primary bg-primary text-primary-foreground"
          : "border-gray-300 bg-white hover:border-gray-400",
        // Disabled state
        disabled && "opacity-50 cursor-not-allowed",
        className
      )}
      onClick={handleClick}
      onTouchEnd={handleTouchEnd}
      disabled={disabled}
    >
      {checked && <Check className="h-4 w-4" />}
    </button>
  );
}
