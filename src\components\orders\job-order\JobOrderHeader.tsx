
import { But<PERSON> } from "@/components/ui/button";
import { RefreshCcw } from "lucide-react";

interface JobOrderHeaderProps {
  onRefresh: () => void;
}

export function JobOrderHeader({ onRefresh }: JobOrderHeaderProps) {
  return (
    <div className="flex flex-col md:flex-row md:items-center justify-between gap-2">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">Job Orders</h1>
        <p className="text-sm text-muted-foreground">
          Manage and process client laundry orders
        </p>
      </div>
      <Button onClick={onRefresh} variant="outline" className="ml-auto">
        <RefreshCcw className="mr-2 h-4 w-4" />
        Refresh
      </Button>
    </div>
  );
}
