
import { useRef } from 'react';
import { StatSummary } from './StatSummary';
import { ReportHeader } from './ReportHeader';
import { ReportTabs } from './ReportTabs';
import { PrintableContent } from './PrintableContent';
import { Card, CardContent } from '@/components/ui/card';
import { Loader2 } from 'lucide-react';
import { OrderSummary } from '@/hooks/orders/reports/types';

interface ReportContentProps {
  clientId: string;
  summary: {
    totalOrders: number;
    totalAmount: number;
    totalPaid: number;
    totalPayable: number;
    paidOrdersCount: number;
    unpaidOrdersCount: number;
    overdueOrdersCount: number;
    paidOrders: OrderSummary[];
    unpaidOrders: OrderSummary[];
    overdueOrders: OrderSummary[];
  };
  dateRange: any;
  updateDateFilter: (range: any) => void;
  overdueFilter: boolean | null;
  updateOverdueFilter: (value: boolean | null) => void;
  datePresets: {
    label: string;
    getValue: () => { from: Date; to: Date };
  }[];
  isPrinting: boolean;
  isLoading: boolean;
  selectedView: string;
  setSelectedView: (view: string) => void;
  handlePrint: () => void;
  handleExportCSV: () => void;
  reportRef: React.RefObject<HTMLDivElement>;
}

export function ReportContent({
  clientId,
  summary,
  dateRange,
  updateDateFilter,
  overdueFilter,
  updateOverdueFilter,
  datePresets,
  isPrinting,
  isLoading,
  selectedView,
  setSelectedView,
  handlePrint,
  handleExportCSV,
  reportRef
}: ReportContentProps) {
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-40">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  const orders = [
    ...summary.paidOrders, 
    ...summary.unpaidOrders
  ];

  return (
    <>
      <Card className="mb-6 print:hidden">
        <CardContent className="pt-6">
          <ReportHeader
            isPrinting={isPrinting}
            handlePrint={handlePrint}
            handleExportCSV={handleExportCSV}
            dateRange={dateRange}
            updateDateFilter={updateDateFilter}
            overdueFilter={overdueFilter}
            updateOverdueFilter={updateOverdueFilter}
            datePresets={datePresets}
          />
          
          <StatSummary 
            summary={summary} 
            isLoading={isLoading}
          />
          
          <ReportTabs 
            activeTab={selectedView} 
            setActiveTab={setSelectedView}
            summary={summary}
            orders={orders}
            isLoading={isLoading}
          />
        </CardContent>
      </Card>
      
      <PrintableContent 
        client={null}
        orders={orders}
        summary={summary}
        reportRef={reportRef}
      />
    </>
  );
}
