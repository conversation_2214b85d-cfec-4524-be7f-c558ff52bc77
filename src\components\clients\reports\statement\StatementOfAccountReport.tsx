
import { useRef, useState } from 'react';
import { ReportContent } from './ReportContent';
import { useClientReportsData } from '@/hooks/orders/useClientReportsData';
import { useReportActions } from './useReportActions';

interface StatementReportProps {
  clientId: string;
}

export function StatementOfAccountReport({ clientId }: StatementReportProps) {
  const reportRef = useRef<HTMLDivElement>(null);
  const [selectedView, setSelectedView] = useState<string>('summary');
  const { summary, dateRange, updateDateFilter, overdueFilter, updateOverdueFilter, datePresets, isLoading } = useClientReportsData(clientId);
  
  // Get report actions
  const { isPrinting, handlePrint, handleExportCSV } = useReportActions({ 
    reportRef, 
    summary 
  });

  return (
    <div>
      <ReportContent 
        clientId={clientId}
        summary={summary}
        dateRange={dateRange}
        updateDateFilter={updateDateFilter}
        overdueFilter={overdueFilter}
        updateOverdueFilter={updateOverdueFilter}
        datePresets={datePresets}
        isPrinting={isPrinting}
        isLoading={isLoading}
        selectedView={selectedView}
        setSelectedView={setSelectedView}
        handlePrint={handlePrint}
        handleExportCSV={handleExportCSV}
        reportRef={reportRef}
      />
    </div>
  );
}
