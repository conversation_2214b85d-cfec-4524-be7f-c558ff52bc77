import React, { useState, useEffect } from "react";
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table";
import { columns } from "./config/tableColumns";
import { Order } from "@/types";
import { flexRender, getCoreRowModel, useReactTable } from "@tanstack/react-table";
import { OrderTableRow } from "./OrderTableRow";
import { NoOrdersMessage } from "./components/NoOrdersMessage";
import { OrdersTableHeader } from "./components/TableHeader";
import { OrderStatusBadge } from "../OrderStatusBadge";
import { Checkbox } from "@/components/ui/checkbox";
import { ExpandedRow } from "./components/ExpandedRow";

interface OrdersDataTableProps {
  data: Order[];
  filterType: "all" | "client" | "walk-in"; 
  onStatusChange: (orderId: string, newStatus: string) => Promise<void>;
  onBatchStatusChange: (orderIds: string[], newStatus: string) => void;
  onPrintOrder: (order: Order) => void;
  onOrderDeleted?: () => void;
}

export function OrdersDataTable({ 
  data, 
  filterType,
  onStatusChange,
  onBatchStatusChange,
  onPrintOrder,
  onOrderDeleted
}: OrdersDataTableProps) {
  const [rowSelection, setRowSelection] = useState({});
  
  // Listen for order deletion events
  useEffect(() => {
    const handleOrderDeleted = () => {
      if (onOrderDeleted) {
        onOrderDeleted();
      }
    };
    
    window.addEventListener('order-deleted', handleOrderDeleted);
    return () => {
      window.removeEventListener('order-deleted', handleOrderDeleted);
    };
  }, [onOrderDeleted]);
  
  // Improved filtering logic for more accurate separation of walk-in vs client orders
  const filteredData = React.useMemo(() => {
    if (filterType === "all") return data;
    
    return data.filter(order => {
      if (filterType === "client") {
        // Client orders must have customer_type set to 'client'
        return order.customerType === "client";
      } else if (filterType === "walk-in") {
        // Walk-in orders must have customer_type set to 'walk-in'
        return order.customerType === "walk-in";
      }
      return true;
    });
  }, [data, filterType]);

  const table = useReactTable({
    data: filteredData,
    columns,
    getCoreRowModel: getCoreRowModel(),
    onRowSelectionChange: setRowSelection,
    state: {
      rowSelection,
    },
  });

  const selectedRows = table.getFilteredSelectedRowModel().rows;
  
  // Extract selected order IDs for batch operations
  const selectedOrderIds = selectedRows.map(row => row.original.id);
  
  const isAllSelected = table.getIsAllPageRowsSelected();
  const handleToggleSelectAll = () => table.toggleAllPageRowsSelected(!isAllSelected);
  
  return (
    <div className="space-y-4">
      <OrdersTableHeader 
        isAllSelected={isAllSelected}
        onToggleSelectAll={handleToggleSelectAll}
        filterType={filterType}
        selectedOrderIds={selectedOrderIds}
        onBatchStatusChange={onBatchStatusChange}
      />
      
      {filteredData.length === 0 ? (
        <NoOrdersMessage filterType={filterType} hasData={data.length > 0} />
      ) : (
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[40px]"></TableHead>
                <TableHead className="w-[40px]">
                  <Checkbox
                    checked={table.getIsAllPageRowsSelected()}
                    onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
                    aria-label="Select all"
                  />
                </TableHead>
                <TableHead>Order ID</TableHead>
                <TableHead>Customer</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Amount</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows.map((row) => (
                <React.Fragment key={row.id}>
                  <OrderTableRow
                    row={row}
                    onStatusChange={onStatusChange}
                    onPrintOrder={onPrintOrder}
                    onOrderDeleted={onOrderDeleted}
                  />
                  {row.getIsExpanded() && (
                    <ExpandedRow 
                      key={`expanded-${row.id}`}
                      order={row.original}
                      onStatusChange={onStatusChange}
                      onPrintOrder={onPrintOrder}
                      onOrderDeleted={onOrderDeleted}
                    />
                  )}
                </React.Fragment>
              ))}
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  );
}
