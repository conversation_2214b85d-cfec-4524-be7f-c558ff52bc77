
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { usePrinterContext } from "@/contexts/PrinterContext";
import { Button } from "@/components/ui/button";

export function usePrinterConnection() {
  const { printerStatus, showPrinterConnect } = usePrinterContext();
  const { toast } = useToast();
  
  const checkPrinterConnection = () => {
    const useBluetoothPrinter = localStorage.getItem('useBluetoothPrinter') === 'true';
    
    // If Bluetooth printing is enabled but printer is not connected
    if (useBluetoothPrinter && printerStatus !== 'connected') {
      toast({
        title: "Printer Not Connected",
        description: "Connect your Bluetooth printer in Settings",
        variant: "destructive",
        action: (
          <Button variant="outline" size="sm" onClick={showPrinterConnect}>
            Connect
          </Button>
        )
      });
      return false;
    }
    
    // If Bluetooth printing is enabled and the printer is connected
    if (useBluetoothPrinter && printerStatus === 'connected') {
      return true;
    }
    
    // Browser printing is the default fallback
    return false;
  };

  return {
    printerStatus,
    checkPrinterConnection,
    showPrinterConnect
  };
}
