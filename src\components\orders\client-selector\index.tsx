
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { ClientButtons } from "./components/ClientButtons";
import { cn } from "@/lib/utils";
import { useEffect } from "react";

export function ClientSelector({ 
  form, 
  initialOrderType = "client" 
}: { 
  form: any, 
  initialOrderType?: "walk-in" | "client" 
}) {
  // Set order type once on mount instead of every render
  useEffect(() => {
    form.setValue("orderType", initialOrderType || "walk-in");
  }, [form, initialOrderType]);
  
  // Handle client selection
  const handleClientChange = (clientId: string) => {
    console.log("ClientSelector: Setting clientId to:", clientId);
    form.setValue("clientId", clientId);
  };
  
  // Set client name for usage in the order
  const handleClientNameChange = (name: string) => {
    console.log("ClientSelector: Setting clientName to:", name);
    form.setValue("clientName", name);
  };
  
  // Set client prefix for order ID generation
  const handleClientPrefixChange = (prefix: string) => {
    console.log("ClientSelector: Setting clientPrefix to:", prefix);
    form.setValue("clientPrefix", prefix);
  };
  
  // Set client contact person for order 
  const handleClientContactPersonChange = (contactPerson: string) => {
    console.log("ClientSelector: Setting contactPerson to:", contactPerson);
    form.setValue("contactPerson", contactPerson);
  };
  
  // Set client phone for order
  const handleClientPhoneChange = (phone: string) => {
    console.log("ClientSelector: Setting clientPhone to:", phone);
    form.setValue("clientPhone", phone);
  };
  
  return (
    <div className="space-y-4">
      <FormField
        control={form.control}
        name="clientId"
        render={({ field }) => (
          <FormItem className="space-y-1.5">
            <FormLabel className={cn("text-base font-semibold")}>Select Client</FormLabel>
            <FormControl>
              <ClientButtons 
                clientId={field.value} 
                setClientId={handleClientChange}
                setClientName={handleClientNameChange}
                setClientPrefix={handleClientPrefixChange}
                setClientContactPerson={handleClientContactPersonChange}
                setClientPhone={handleClientPhoneChange}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}
