
import React from "react";
import { ClientItem } from "@/services/clientItemService";
import { ClientItemWithQuantity } from "../OrderFormTypes";
import { TableRow, TableCell } from "@/components/ui/table";
import { Check } from "lucide-react";
import { calculateItemTotalWithTreatments } from "@/utils/priceCalculations";
import { TouchCheckbox } from "@/components/ui/touch-checkbox";

interface MainItemRowProps {
  item: ClientItem;
  isSelected: boolean;
  selectedItems: ClientItemWithQuantity[];
  handleToggle: (e: React.MouseEvent<HTMLInputElement>) => void;
  isMobile: boolean;
}

export function MainItemRow({ item, isSelected, selectedItems, handleToggle, isMobile }: MainItemRowProps) {
  return (
    <TableRow>
      <TableCell className={isMobile ? "w-[40px] px-2" : "w-[60px]"}>
        <div className="flex items-center justify-center">
          <TouchCheckbox
            checked={isSelected}
            onCheckedChange={() => handleToggle({ preventDefault: () => {}, stopPropagation: () => {} } as React.MouseEvent<HTMLInputElement>)}
            className="h-6 w-6"
          />
        </div>
      </TableCell>
      <TableCell className={isMobile ? "px-2" : ""}>
        <span className="font-medium">{item.name}</span>
      </TableCell>
      <TableCell className={isMobile ? "w-[140px] px-1" : "w-[240px]"}>
        {isSelected ? (
          <div className="flex items-center">
            <Check className="h-4 w-4 text-green-500 mr-2" />
            <span>{selectedItems.length} selected</span>
          </div>
        ) : (
          <span className="text-muted-foreground">Not selected</span>
        )}
      </TableCell>
      <TableCell className={`text-right ${isMobile ? "px-2" : ""}`}>
        {isSelected && selectedItems.length > 0 ? (
          <span className="font-medium">
            ₱{selectedItems.reduce((sum, item) => sum + calculateItemTotalWithTreatments(item), 0).toFixed(2)}
          </span>
        ) : (
          <span className="text-muted-foreground">—</span>
        )}
      </TableCell>
    </TableRow>
  );
}
