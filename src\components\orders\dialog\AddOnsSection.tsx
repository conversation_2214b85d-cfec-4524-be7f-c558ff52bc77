
import { Order } from "@/types";

interface AddOnsSectionProps {
  order: Order;
  isStaff: boolean;
}

export function AddOnsSection({ order, isStaff }: AddOnsSectionProps) {
  // Show add-ons summary if available
  const addOnsInfo = [];
  
  if (order.useDetergent) {
    addOnsInfo.push(`Detergent (${order.detergentType !== 'none' ? order.detergentType : 'regular'}, qty: ${order.detergentQuantity || 1})`);
  }
  
  if (order.useFabricConditioner) {
    addOnsInfo.push(`Fabric conditioner (${order.conditionerType !== 'none' ? order.conditionerType : 'regular'}, qty: ${order.conditionerQuantity || 1})`);
  }
  
  if (order.useStainRemover) {
    addOnsInfo.push('Stain remover treatment');
  }
  
  if (order.useBleach) {
    addOnsInfo.push('Bleach treatment');
  }
  
  // Check if any line items have special treatments
  const hasSpecialItemTreatments = order.lineItems?.some(item => 
    item.treatments?.useStainRemoval || item.treatments?.useBeachTreatment
  );
  
  // If no add-ons and not a staff user and no special item treatments, don't show the section
  if (!isStaff && addOnsInfo.length === 0 && !hasSpecialItemTreatments) return null;
  
  return (
    <div className="space-y-2">
      <h3 className="text-lg font-medium">Add-ons & Treatments</h3>
      
      <div className={`text-sm ${addOnsInfo.length > 0 ? '' : 'text-muted-foreground'}`}>
        {addOnsInfo.length > 0 ? addOnsInfo.join(', ') : 'No global add-ons selected'}
      </div>
      
      {/* Display special item treatments if any */}
      {hasSpecialItemTreatments && (
        <div className="mt-2 text-sm">
          <h4 className="text-sm font-medium mb-1">Special Item Treatments:</h4>
          <ul className="list-disc pl-5">
            {order.lineItems?.filter(item => item.treatments?.useStainRemoval).length > 0 && (
              <li>Stain Removal for {order.lineItems?.filter(item => item.treatments?.useStainRemoval).length} items</li>
            )}
            {order.lineItems?.filter(item => item.treatments?.useBeachTreatment).length > 0 && (
              <li>Bleach Treatment for {order.lineItems?.filter(item => item.treatments?.useBeachTreatment).length} items</li>
            )}
          </ul>
        </div>
      )}
    </div>
  );
}
