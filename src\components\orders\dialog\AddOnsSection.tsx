
import { Order } from "@/types";

interface AddOnsSectionProps {
  order: Order;
  isStaff: boolean;
}

export function AddOnsSection({ order, isStaff }: AddOnsSectionProps) {
  if (!isStaff) return null;
  
  // Show add-ons summary if available
  const addOnsInfo = [];
  
  if (order.useDetergent) {
    addOnsInfo.push(`Detergent (${order.detergentQuantity || 1})`);
  }
  
  if (order.useFabricConditioner) {
    addOnsInfo.push(`Fabric conditioner (${order.conditionerQuantity || 1})`);
  }
  
  if (order.useStainRemover) {
    addOnsInfo.push('Stain remover');
  }
  
  if (order.useBleach) {
    addOnsInfo.push('Bleach');
  }
  
  return (
    <div className="space-y-2">
      <div className="mb-2">
        <h3 className="text-lg font-medium">Add-ons & Treatments</h3>
      </div>
      
      <div className="text-sm text-muted-foreground">
        {addOnsInfo.length > 0 ? addOnsInfo.join(', ') : 'No add-ons selected'}
      </div>
    </div>
  );
}
