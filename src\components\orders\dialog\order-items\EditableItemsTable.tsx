
import { LineItem } from "@/types";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { EditableItemRow } from "./EditableItemRow";

interface EditableItemsTableProps {
  editableItems: LineItem[];
  onItemChange: (updatedItem: LineItem, index: number) => void;
  onRemoveItem: (index: number) => void;
  onAddItem: () => void;
  onSaveChanges: () => Promise<void>;
  onCancelEdit: () => void;
  clientId?: string;
}

export function EditableItemsTable({
  editableItems,
  onItemChange,
  onRemoveItem,
  onAddItem,
  onSaveChanges,
  onCancelEdit,
  clientId
}: EditableItemsTableProps) {
  const totalAmount = editableItems.reduce((sum, item) => sum + (item.total || 0), 0).toFixed(2);

  return (
    <div className="space-y-3">
      <div className="flex justify-between items-center">
        <h3 className="text-sm font-medium">Edit Order Items</h3>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={onCancelEdit}>
            Cancel
          </Button>
          <Button size="sm" onClick={onSaveChanges}>
            Save Changes
          </Button>
        </div>
      </div>

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Item Name</TableHead>
            <TableHead>Quantity</TableHead>
            <TableHead>Unit Price (₱)</TableHead>
            <TableHead>Treatment</TableHead>
            <TableHead className="text-right">Total (₱)</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {editableItems.length === 0 ? (
            <TableRow>
              <TableCell colSpan={6} className="text-center py-4 text-muted-foreground">
                No items. Add an item to this order.
              </TableCell>
            </TableRow>
          ) : (
            editableItems.map((item, index) => (
              <EditableItemRow 
                key={`${item.id}-${index}`} 
                item={item} 
                clientId={clientId}
                onChange={updatedItem => onItemChange(updatedItem, index)} 
                onRemove={() => onRemoveItem(index)} 
              />
            ))
          )}
        </TableBody>
      </Table>

      <div className="flex justify-between items-center">
        <Button type="button" variant="outline" size="sm" onClick={onAddItem}>
          <Plus className="h-4 w-4 mr-1" /> Add Item
        </Button>
        
        <div className="text-right">
          <p className="font-semibold">
            Total: ₱{totalAmount}
          </p>
        </div>
      </div>
    </div>
  );
}
