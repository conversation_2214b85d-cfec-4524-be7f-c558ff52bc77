
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";

interface OrderTotalSummaryProps {
  total: number;
  onAddItem: () => void;
}

export function OrderTotalSummary({ total, onAddItem }: OrderTotalSummaryProps) {
  return (
    <div className="flex justify-between items-center">
      <Button
        type="button"
        variant="outline"
        size="sm"
        onClick={onAddItem}
      >
        <Plus className="h-4 w-4 mr-1" /> Add Item
      </Button>
      
      <div className="text-right">
        <p className="font-semibold">
          New Total: ₱{total.toFixed(2)}
        </p>
      </div>
    </div>
  );
}
