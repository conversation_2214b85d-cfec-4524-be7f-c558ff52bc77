
import { UseFormReturn } from "react-hook-form";
import { OrderFormValues } from "../OrderFormTypes";
import { Label } from "@/components/ui/label";
import {
  FormField,
  FormItem,
  FormControl
} from "@/components/ui/form";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

interface PricingMethodToggleProps {
  form: UseFormReturn<OrderFormValues>;
}

export function PricingMethodToggle({ form }: PricingMethodToggleProps) {
  // Handle the pricingMethod access in a type-safe way
  const handlePricingMethodChange = (value: string) => {
    // Use the setValue method for fields in the schema
    form.setValue("pricingMethod", value as "weight" | "client_item" | "dry_cleaning");
  };

  const getPricingMethod = () => {
    // Get the value using the watch method
    return form.watch("pricingMethod") || "weight";
  };
  
  const orderType = form.watch("orderType");
  const isWalkIn = orderType === "walk-in";
  
  return (
    <div>
      <h4 className="text-sm font-medium mb-3">Pricing Method</h4>
      
      <FormItem>
        <FormControl>
          <RadioGroup 
            defaultValue={getPricingMethod()}
            value={getPricingMethod()}
            onValueChange={handlePricingMethodChange}
            className="flex flex-col space-y-1"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="weight" id="weight-pricing" />
              <Label htmlFor="weight-pricing" className="cursor-pointer">Weight-based Pricing</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="client_item" id="item-pricing" />
              <Label htmlFor="item-pricing" className="cursor-pointer">Item-based Pricing</Label>
            </div>
          </RadioGroup>
        </FormControl>
      </FormItem>
    </div>
  );
}
