
import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Title } from "@/components/ui/dialog";
import { OrderForm } from "./order-form/OrderForm";
import { ClientOrderForm } from "./client-order-form/ClientOrderForm";
import { addOrder } from "./OrderData";
import { useToast } from "@/hooks/use-toast";

interface AddOrderDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onOrderAdded: () => void;
  initialOrderType: "walk-in" | "client";
}

export function AddOrderDialog({ 
  open, 
  onOpenChange, 
  onOrderAdded,
  initialOrderType
}: AddOrderDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const handleOrderSubmitted = async (formData: any) => {
    try {
      setIsSubmitting(true);
      console.log("AddOrderDialog: Submitting order with data:", formData);
      
      // Explicitly set the orderType based on initialOrderType
      formData.orderType = initialOrderType;
      formData.customerType = initialOrderType;
      
      // Make sure we have customer information
      if (!formData.customer && initialOrderType === "walk-in") {
        formData.customer = {
          name: formData.customerName || 'Unknown Customer',
          phone: formData.phoneNumber || ''
        };
      }
      
      // For client orders, ensure the client ID is properly set
      if (initialOrderType === 'client' && formData.clientId) {
        console.log("AddOrderDialog: Processing client order with clientId:", formData.clientId);
        formData.customerType = 'client';
      }
      
      // Add the order to the store
      const newOrder = await addOrder(formData);
      console.log("AddOrderDialog: New order created:", newOrder);
      
      // Close the dialog
      onOpenChange(false);
      
      // Show success toast
      toast({
        title: "Order Created",
        description: "New order has been created successfully",
      });
      
      // Allow the parent to refresh the order list
      onOrderAdded();
    } catch (error) {
      console.error("Error creating order:", error);
      toast({
        title: "Error",
        description: "Failed to create order. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Update dialog title based on order type
  const dialogTitle = initialOrderType === "walk-in" 
    ? "New Walk-in Order" 
    : "New Client Order";

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{dialogTitle}</DialogTitle>
        </DialogHeader>
        
        {initialOrderType === "walk-in" ? (
          <OrderForm 
            onSubmit={handleOrderSubmitted}
            initialOrderType="walk-in"
            isSubmitting={isSubmitting}
          />
        ) : (
          <ClientOrderForm 
            onSubmit={handleOrderSubmitted}
            initialOrderType="client"
            isSubmitting={isSubmitting}
          />
        )}
      </DialogContent>
    </Dialog>
  );
}
