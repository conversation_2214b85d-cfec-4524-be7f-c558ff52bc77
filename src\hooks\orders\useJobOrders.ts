
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Order, LineItem } from "@/types";
import { useToast } from "@/hooks/use-toast";

export function useJobOrders() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const fetchOrders = async () => {
    try {
      setIsLoading(true);
      
      const { data, error } = await supabase
        .from("orders")
        .select("*")
        .eq("status", "processing");
        
      if (error) {
        throw error;
      }
      
      if (data) {
        // Format the data to match our Order type
        const formattedOrders: Order[] = data.map(order => {
          // Parse items JSON if it's a string
          let lineItems: LineItem[] = [];
          try {
            if (order.items) {
              // Handle different types that might come from the database
              if (typeof order.items === 'string') {
                lineItems = JSON.parse(order.items);
              } else if (Array.isArray(order.items)) {
                // If it's already an array, make sure each item conforms to LineItem
                lineItems = order.items.map((item: any) => ({
                  id: item?.id || `item-${Math.random().toString(36).substring(2, 9)}`,
                  name: item?.name || '',
                  quantity: Number(item?.quantity) || 1,
                  unitPrice: Number(item?.unitPrice) || 0,
                  total: Number(item?.total) || 0,
                  treatmentDescription: item?.treatmentDescription || 'none'
                }));
              }
            }
          } catch (err) {
            console.error("Error parsing order items:", err, "Original items:", order.items);
            // Return empty array on error
            lineItems = [];
          }
          
          return {
            id: order.id,
            uuid: order.id,
            reference_code: order.reference_code || order.id.substring(0, 8),
            orderDate: order.created_at,
            deliveryDate: order.delivery_date || '',
            customer: {
              name: order.customer_name || '',
              phone: order.phone_number || '',
              contactPerson: order.contact_person || ''
            },
            clientId: order.client_id,
            customerType: (order.customer_type || 'client') as 'client' | 'walk-in',
            amount: order.amount || 0,
            paidAmount: order.paid_amount || 0,
            status: order.status || 'processing',
            serviceType: order.service_type || '',
            lineItems: lineItems,
            weightKilos: order.weight_kilos || 0,
            numberOfPieces: order.number_of_pieces || 0,
            useDetergent: order.use_detergent || false,
            useFabricConditioner: order.use_conditioner || false,
            useStainRemover: order.use_stain_remover || false,
            useBleach: order.use_bleach || false,
            detergentType: 'none',
            conditionerType: 'none',
            detergentQuantity: parseInt(order.detergent_quantity || '1', 10),
            conditionerQuantity: parseInt(order.conditioner_quantity || '1', 10),
            notes: order.notes || '',
            vatAmount: order.vat_amount || 0,
            subtotalBeforeVAT: order.subtotal_before_vat || 0,
            prefix: ''
          };
        });
        
        setOrders(formattedOrders);
      }
    } catch (err: any) {
      console.error("Error fetching job orders:", err);
      setError(err.message);
      toast({
        title: "Error",
        description: "Could not load job orders",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchOrders();
    
    // Set up real-time subscription to orders updates
    const channel = supabase
      .channel('orders-changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'orders' },
        (payload) => {
          console.log('Orders updated:', payload);
          fetchOrders();
        }
      )
      .subscribe();
      
    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  return { 
    orders, 
    isLoading, 
    error, 
    refreshOrders: fetchOrders 
  };
}
