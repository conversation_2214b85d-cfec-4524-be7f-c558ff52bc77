
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Order, LineItem, DryCleaningItem } from "@/types";
import { useToast } from "@/hooks/use-toast";

export function useJobOrders() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const fetchOrders = async () => {
    try {
      setIsLoading(true);
      
      const { data, error } = await supabase
        .from("orders")
        .select("*")
        .eq("status", "processing");
        
      if (error) {
        throw error;
      }
      
      if (data) {
        // Format the data to match our Order type
        const formattedOrders: Order[] = data.map(order => {
          // Parse items JSON if it's a string
          let lineItems: LineItem[] = [];
          try {
            if (order.items) {
              // Handle different types that might come from the database
              if (typeof order.items === 'string') {
                lineItems = JSON.parse(order.items);
              } else if (Array.isArray(order.items)) {
                // If it's already an array, make sure each item conforms to LineItem
                lineItems = order.items.map((item: any) => ({
                  id: item?.id || `item-${Math.random().toString(36).substring(2, 9)}`,
                  name: item?.name || '',
                  quantity: Number(item?.quantity) || 1,
                  unitPrice: Number(item?.unitPrice) || 0,
                  total: Number(item?.total) || 0,
                  treatmentDescription: item?.treatmentDescription || 'none'
                }));
              }
            }
          } catch (err) {
            console.error("Error parsing order items:", err, "Original items:", order.items);
            // Return empty array on error
            lineItems = [];
          }
          
          // Parse dry cleaning items if they exist
          let dryCleaningItems: DryCleaningItem[] = [];
          try {
            if (order.dry_cleaning_items) {
              if (typeof order.dry_cleaning_items === 'string') {
                dryCleaningItems = JSON.parse(order.dry_cleaning_items);
              } else if (Array.isArray(order.dry_cleaning_items)) {
                // Cast and map each item to ensure it has the correct structure
                dryCleaningItems = (order.dry_cleaning_items as any[]).map((item: any) => ({
                  type: item.type || '',
                  price: Number(item.price) || 0,
                  quantity: Number(item.quantity) || 1,
                  total: Number(item.total) || 0,
                  name: item.name || '',
                  id: item.id || `dry-${Math.random().toString(36).substring(2, 9)}`
                }));
              } else if (typeof order.dry_cleaning_items === 'object') {
                // If it's an object but not an array, convert it
                dryCleaningItems = Object.values(order.dry_cleaning_items).map((item: any) => ({
                  type: item.type || '',
                  price: Number(item.price) || 0,
                  quantity: Number(item.quantity) || 1,
                  total: Number(item.total) || 0,
                  name: item.name || '',
                  id: item.id || `dry-${Math.random().toString(36).substring(2, 9)}`
                }));
              }
            }
          } catch (err) {
            console.error("Error parsing dry cleaning items:", err, "Original data:", order.dry_cleaning_items);
            dryCleaningItems = [];
          }
          
          // Ensure customer type is correctly set
          const customerType = order.customer_type === 'walk-in' ? 'walk-in' : 'client';
          
          console.log(`Order ${order.id} - customerType: ${customerType}, reference_code: ${order.reference_code}`);
          
          return {
            id: order.id,
            uuid: order.id,
            reference_code: order.reference_code || order.id.substring(0, 8),
            orderDate: order.created_at,
            deliveryDate: order.delivery_date || '',
            customer: {
              name: order.customer_name || '',
              phone: order.phone_number || '',
              contactPerson: order.contact_person || ''
            },
            clientId: order.client_id,
            customerType: customerType as 'client' | 'walk-in',
            amount: order.amount || 0,
            paidAmount: order.paid_amount || 0,
            status: order.status || 'processing',
            serviceType: order.service_type || '',
            lineItems: lineItems,
            weightKilos: order.weight_kilos || 0,
            numberOfPieces: order.number_of_pieces || 0,
            useDetergent: order.use_detergent || false,
            useFabricConditioner: order.use_conditioner || false,
            useStainRemover: order.use_stain_remover || false,
            useBleach: order.use_bleach || false,
            detergentType: 'regular',
            conditionerType: 'regular',
            detergentQuantity: parseInt(order.detergent_quantity || '1', 10),
            conditionerQuantity: parseInt(order.conditioner_quantity || '1', 10),
            notes: order.notes || '',
            vatAmount: order.vat_amount || 0,
            subtotalBeforeVAT: order.subtotal_before_vat || 0,
            isDryCleaning: order.is_dry_cleaning || order.service_type === 'dry_cleaning',
            dryCleaningItems: dryCleaningItems,
            prefix: ''
          };
        });
        
        setOrders(formattedOrders);
      }
    } catch (err: any) {
      console.error("Error fetching job orders:", err);
      setError(err.message);
      toast({
        title: "Error",
        description: "Could not load job orders",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchOrders();
    
    // Set up real-time subscription to orders updates
    const channel = supabase
      .channel('orders-changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'orders' },
        (payload) => {
          console.log('Orders updated:', payload);
          fetchOrders();
        }
      )
      .subscribe();
      
    return () => {
      console.log("Cleaning up orders subscription");
      supabase.removeChannel(channel);
    };
  }, []);

  return { 
    orders, 
    isLoading, 
    error, 
    refreshOrders: fetchOrders 
  };
}
