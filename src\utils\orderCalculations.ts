
import { LineItem } from "@/types";

/**
 * Calculate treatment price based on treatment description
 */
export function getTreatmentPrice(treatment: string | undefined): number {
  if (!treatment) return 0;
  
  switch (treatment) {
    case 'Stain removal':
      return 15.00;
    case 'Bleaching':
      return 20.00;
    case 'Deep clean':
      return 25.00;
    case 'Delicate wash':
      return 18.00;
    default:
      return 0;
  }
}

/**
 * Calculates the total price for a line item including treatment costs
 */
export function calculateLineItemTotal(item: LineItem): number {
  const baseCost = item.unitPrice * item.quantity;
  const treatmentCost = getTreatmentPrice(item.treatmentDescription) * item.quantity;
  return baseCost + treatmentCost;
}

/**
 * Recalculates all totals for line items
 */
export function recalculateLineItemTotals(items: LineItem[]): LineItem[] {
  return items.map(item => ({
    ...item,
    total: calculateLineItemTotal(item)
  }));
}

/**
 * Calculates the order total from line items
 */
export function calculateOrderTotal(items: LineItem[]): number {
  return items.reduce((sum, item) => sum + item.total, 0);
}

/**
 * Formats the order's items for database storage
 * Ensures consistent format between string and object conversions
 */
export function formatItemsForDatabase(items: LineItem[]): LineItem[] {
  // Ensure all items have correct totals
  const itemsWithCorrectTotals = recalculateLineItemTotals(items);
  
  // Create a deep copy to avoid reference issues
  return JSON.parse(JSON.stringify(itemsWithCorrectTotals));
}

/**
 * Parses items from database format to LineItem[]
 */
export function parseItemsFromDatabase(dbItems: any): LineItem[] {
  if (!dbItems) return [];
  
  try {
    // Handle string or array format
    let parsedItems: LineItem[] = [];
    
    if (typeof dbItems === 'string') {
      parsedItems = JSON.parse(dbItems);
    } else if (Array.isArray(dbItems)) {
      parsedItems = dbItems;
    }
    
    // Ensure all items have correct totals
    return recalculateLineItemTotals(parsedItems);
  } catch (error) {
    console.error('Error parsing items from database:', error);
    return [];
  }
}
