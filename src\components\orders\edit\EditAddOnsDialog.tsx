
import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>laskConical, Loader2 } from "lucide-react";
import { Order } from "@/types";
import { useToast } from "@/hooks/use-toast";
import { updateOrderAddOns } from "@/services/orders";
import { DetergentSection } from "../add-ons/DetergentSection";
import { ConditionerSection } from "../add-ons/ConditionerSection";
import { SpecialTreatmentsSection } from "../add-ons/SpecialTreatmentsSection";
import { useForm } from "react-hook-form";
import { Form } from "@/components/ui/form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { DryCleaningIndicator } from "../job-order/add-ons/DryCleaningIndicator";

const addOnFormSchema = z.object({
  detergentType: z.enum(["none", "regular", "color"]).default("none"),
  detergentQuantity: z.string().default("1"),
  conditionerType: z.enum(["none", "regular", "fresh", "floral"]).default("none"),
  conditionerQuantity: z.string().default("1"),
  useStainRemover: z.boolean().default(false),
  useBleach: z.boolean().default(false),
  isDryCleaning: z.boolean().default(false),
});

type AddOnFormValues = z.infer<typeof addOnFormSchema>;

interface EditAddOnsDialogProps {
  order: Order | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onAddOnsUpdated: () => void;
}

export function EditAddOnsDialog({
  order,
  open,
  onOpenChange,
  onAddOnsUpdated
}: EditAddOnsDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  
  // Convert order add-ons to form values
  const defaultValues: AddOnFormValues = {
    detergentType: (order?.useDetergent ? order?.detergentType || "regular" : "none") as "none" | "regular" | "color",
    detergentQuantity: order?.detergentQuantity?.toString() || "1",
    conditionerType: (order?.useFabricConditioner ? order?.conditionerType || "regular" : "none") as "none" | "regular" | "fresh" | "floral",
    conditionerQuantity: order?.conditionerQuantity?.toString() || "1",
    useStainRemover: order?.useStainRemover || false,
    useBleach: order?.useBleach || false,
    isDryCleaning: order?.isDryCleaning || false
  };

  const form = useForm<AddOnFormValues>({
    resolver: zodResolver(addOnFormSchema),
    defaultValues
  });
  
  const isDryCleaning = form.watch("isDryCleaning");
  
  const handleSubmit = async (data: AddOnFormValues) => {
    if (!order) return;
    
    setIsSubmitting(true);
    
    try {
      // Translate form data to order add-on properties for database update
      const addOnUpdates = {
        useDetergent: data.detergentType !== "none",
        useFabricConditioner: data.conditionerType !== "none",
        useStainRemover: data.useStainRemover,
        useBleach: data.useBleach,
        isDryCleaning: data.isDryCleaning,
        detergentQuantity: parseInt(data.detergentQuantity),
        conditionerQuantity: parseInt(data.conditionerQuantity),
        detergentType: data.detergentType,
        conditionerType: data.conditionerType
      };
      
      await updateOrderAddOns(order.id, addOnUpdates);
      
      toast({
        title: "Add-ons Updated",
        description: "Order add-ons have been successfully updated",
      });
      
      onAddOnsUpdated();
      onOpenChange(false);
    } catch (error) {
      console.error("Failed to update add-ons:", error);
      toast({
        title: "Update Failed",
        description: "Could not update order add-ons",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  if (!order) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-lg max-h-[90vh] overflow-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <FlaskConical className="mr-2 h-5 w-5" />
            Edit Add-ons & Treatments
          </DialogTitle>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-5">
            <div className="flex items-center space-x-2 py-2">
              <Switch 
                id="dry-cleaning" 
                checked={isDryCleaning}
                onCheckedChange={(checked) => form.setValue("isDryCleaning", checked)}
              />
              <Label htmlFor="dry-cleaning" className="font-medium">
                Dry Cleaning Service
              </Label>
            </div>
            
            {isDryCleaning && (
              <DryCleaningIndicator isDryCleaning={isDryCleaning} />
            )}
            
            <DetergentSection form={form} />
            <ConditionerSection form={form} />
            <SpecialTreatmentsSection form={form} />
            
            <DialogFooter>
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => onOpenChange(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Updating...
                  </>
                ) : "Save Changes"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
