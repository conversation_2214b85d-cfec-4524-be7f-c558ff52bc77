
import React from "react";
import { LineItem } from "@/types";
import { TableCell, TableRow } from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Trash2 } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { calculateLineItemTotal } from "@/utils/orderCalculations";

interface ItemTableRowProps {
  item: LineItem;
  clientId?: string;
  onRemove: (id: string) => void;
  onChange: (field: keyof LineItem, value: any) => void;
}

export function ItemTableRow({
  item,
  clientId,
  onRemove,
  onChange
}: ItemTableRowProps) {
  const handleTextChange = (e: React.ChangeEvent<HTMLInputElement>, field: keyof LineItem) => {
    onChange(field, e.target.value);
  };

  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>, field: keyof LineItem) => {
    const value = parseFloat(e.target.value);
    if (!isNaN(value) && value >= 0) {
      onChange(field, value);
      
      // Recalculate total when quantity or unit price changes
      if (field === "quantity" || field === "unitPrice") {
        const updatedItem = {
          ...item,
          [field]: value
        };
        onChange("total", calculateLineItemTotal(updatedItem));
      }
    }
  };

  const handleTreatmentChange = (value: string) => {
    // Don't convert "none" to empty string anymore
    onChange("treatmentDescription", value);
    
    // Recalculate total with new treatment
    const updatedItem = {
      ...item,
      treatmentDescription: value
    };
    onChange("total", calculateLineItemTotal(updatedItem));
  };

  // Determine the correct value for the select component
  // If treatmentDescription is empty, use "none" as the value
  const treatmentValue = item.treatmentDescription || "none";

  return (
    <TableRow>
      <TableCell>
        <Input
          value={item.name}
          onChange={(e) => handleTextChange(e, "name")}
          className="w-full"
        />
      </TableCell>
      <TableCell>
        <Input
          type="number"
          min="1"
          value={item.quantity}
          onChange={(e) => handleNumberChange(e, "quantity")}
          className="w-20"
        />
      </TableCell>
      <TableCell>
        <Input
          type="number"
          min="0"
          step="0.01"
          value={item.unitPrice}
          onChange={(e) => handleNumberChange(e, "unitPrice")}
          className="w-24"
        />
      </TableCell>
      <TableCell>
        <Select 
          value={treatmentValue}
          onValueChange={handleTreatmentChange}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="No treatment" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="none">No treatment</SelectItem>
            <SelectItem value="Stain removal">Stain removal</SelectItem>
            <SelectItem value="Bleaching">Bleaching</SelectItem>
            <SelectItem value="Deep clean">Deep clean</SelectItem>
            <SelectItem value="Delicate wash">Delicate wash</SelectItem>
          </SelectContent>
        </Select>
      </TableCell>
      <TableCell className="text-right">
        ₱ {item.total.toFixed(2)}
      </TableCell>
      <TableCell>
        <Button
          variant="ghost" 
          size="icon"
          onClick={() => onRemove(item.id)}
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </TableCell>
    </TableRow>
  );
}
