
import React from "react";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";

interface ClientSelectProps {
  value: string;
  onChange: (value: string) => void;
  clients: Array<{ id: string; name: string }>;
}

export const ClientSelect: React.FC<ClientSelectProps> = ({ value, onChange, clients }) => {
  if (clients.length === 0) return null;
  
  return (
    <div>
      <Select
        value={value}
        onValueChange={onChange}
      >
        <SelectTrigger>
          <SelectValue placeholder="Filter by client" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Clients</SelectItem>
          {clients.map((client) => (
            <SelectItem key={client.id} value={client.id}>
              {client.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};
