
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export function useClientItems(clientId: string | null, open: boolean) {
  const [clientItems, setClientItems] = useState<{ id: string; name: string; item_type: string; unit_price: number; }[]>([]);
  const [clientPrefix, setClientPrefix] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    const fetchClientInfo = async () => {
      if (!clientId || !open) return;
      
      setIsLoading(true);
      try {
        const { data: clientData, error: clientError } = await supabase
          .from('clients')
          .select('prefix')
          .eq('id', clientId)
          .single();
        
        if (clientError) throw clientError;
        setClientPrefix(clientData?.prefix || null);
        
        // Make sure we're explicitly selecting unit_price
        const { data, error } = await supabase
          .from('client_items')
          .select('id, name, item_type, unit_price')
          .eq('client_id', clientId);
        
        if (error) throw error;
        
        // Use the raw price data from the database without modifying it
        const itemsWithAccuratePrice = data?.map(item => ({
          ...item,
          // No default values for unit_price, use exactly what's in the database
          unit_price: item.unit_price
        })) || [];
        
        console.log('Fetched client items with actual database prices:', itemsWithAccuratePrice);
        setClientItems(itemsWithAccuratePrice);
      } catch (error) {
        console.error('Error fetching client data:', error);
        toast({
          title: 'Error',
          description: 'Failed to load client information.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchClientInfo();
  }, [clientId, open, toast]);

  return {
    clientItems,
    clientPrefix,
    isLoading
  };
}
