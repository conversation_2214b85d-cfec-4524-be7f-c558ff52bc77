
import { useState } from "react";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { OrderFormValues, PriceBreakdown, ServiceWeight, DryCleaningItem, orderFormSchema } from "../../OrderFormTypes";
import { SERVICE_TYPES } from "../../pricing/constants";

export function useClientOrderFormState(
  onSubmit: (data: any) => void,
  isSubmitting = false
) {
  const [priceBreakdown, setPriceBreakdown] = useState<PriceBreakdown>({
    basePrice: 0,
    addOnPrice: 0,
    subtotal: 0,
    vatAmount: 0,
    totalPrice: 0
  });

  // Get today's date and tomorrow for default delivery date
  const today = new Date();
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);

  // Initialize form specifically for client orders
  const form = useForm<OrderFormValues>({
    resolver: zodResolver(orderFormSchema),
    defaultValues: {
      orderType: "client" as "walk-in" | "client",
      pricingMethod: "client_item",
      deliveryDate: tomorrow, // Use a Date object for deliveryDate
      weightKilos: "1.0",
      numberOfPieces: "1",
      detergentType: "none" as "none" | "regular" | "color",
      detergentQuantity: "1",
      conditionerType: "none" as "none" | "regular" | "fresh" | "floral",
      conditionerQuantity: "1",
      useStainRemover: false,
      useBleach: false,
      useDetergent: false,
      useFabricConditioner: false,
      paidAmount: "0",
      clientId: "",
      serviceType: SERVICE_TYPES.WASH_DRY_FOLD, // Default service type
      selectedClientItems: [],
      dryCleaningItems: [] as DryCleaningItem[], // Explicitly type as DryCleaningItem[]
      selectedServiceTypes: [SERVICE_TYPES.WASH_DRY_FOLD], // Initialize with default service type
      serviceWeights: [
        { 
          serviceType: SERVICE_TYPES.WASH_DRY_FOLD, 
          weightKilos: 3.0 
        }
      ]
    },
    mode: "onChange"
  });

  // Handle service type selection
  const handleServiceTypeToggle = (serviceType: string, checked: boolean) => {
    let currentSelectedTypes = form.watch("selectedServiceTypes") || [SERVICE_TYPES.WASH_DRY_FOLD];
    
    if (checked && !currentSelectedTypes.includes(serviceType)) {
      currentSelectedTypes = [...currentSelectedTypes, serviceType];
      
      // Initialize service weight for this service if it's a weight-based service
      if (serviceType !== SERVICE_TYPES.DRY_CLEANING) {
        const currentServiceWeights = form.watch("serviceWeights") || [];
        if (!currentServiceWeights.some(sw => sw.serviceType === serviceType)) {
          const updatedWeights: ServiceWeight[] = [
            ...currentServiceWeights.map(sw => ({ 
              serviceType: sw.serviceType, 
              weightKilos: sw.weightKilos 
            })),
            { serviceType: serviceType, weightKilos: 3.0 }
          ];
          form.setValue("serviceWeights", updatedWeights);
        }
      }
    } else if (!checked && currentSelectedTypes.includes(serviceType)) {
      currentSelectedTypes = currentSelectedTypes.filter(type => type !== serviceType);
      
      // Remove service weight for this service
      const currentServiceWeights = form.watch("serviceWeights") || [];
      const updatedWeights: ServiceWeight[] = currentServiceWeights
        .filter(sw => sw.serviceType !== serviceType)
        .map(sw => ({
          serviceType: sw.serviceType,
          weightKilos: sw.weightKilos
        }));
      form.setValue("serviceWeights", updatedWeights);
    }
    
    // Ensure we always have at least one service type selected
    if (currentSelectedTypes.length === 0) {
      currentSelectedTypes = [SERVICE_TYPES.WASH_DRY_FOLD];
      
      // Ensure we have weight for the default service
      const currentServiceWeights = form.watch("serviceWeights") || [];
      if (!currentServiceWeights.some(sw => sw.serviceType === SERVICE_TYPES.WASH_DRY_FOLD)) {
        const updatedWeights: ServiceWeight[] = [
          ...currentServiceWeights.map(sw => ({
            serviceType: sw.serviceType,
            weightKilos: sw.weightKilos
          })),
          { serviceType: SERVICE_TYPES.WASH_DRY_FOLD, weightKilos: 3.0 }
        ];
        form.setValue("serviceWeights", updatedWeights);
      }
    }
    
    // Update form values
    form.setValue("selectedServiceTypes", currentSelectedTypes);
    
    // Set primary service type for backward compatibility
    if (currentSelectedTypes.includes(SERVICE_TYPES.DRY_CLEANING)) {
      form.setValue("serviceType", SERVICE_TYPES.DRY_CLEANING);
    } else {
      form.setValue("serviceType", currentSelectedTypes[0]);
    }
  };

  // This is the function that will handle the submit event
  // It will only be called when the form is explicitly submitted via the submit button
  const handleSubmit = async (data: OrderFormValues) => {
    console.log("ClientOrderForm submit triggered", data);
    
    // Only validate client selection for client orders
    if (data.orderType === "client" && !data.clientId) {
      form.setError("clientId", { 
        type: "manual", 
        message: "Please select a client" 
      });
      return;
    }

    // Ensure delivery date is not empty
    if (!data.deliveryDate) {
      form.setError("deliveryDate", {
        type: "manual",
        message: "Please select a delivery date"
      });
      return;
    }

    const selectedServiceTypes = data.selectedServiceTypes || [data.serviceType];
    const errors: {[key: string]: {type: string, message: string}} = {};
    
    // Validate based on selected service types and order type
    if (data.orderType === "client") {
      if (selectedServiceTypes.includes(SERVICE_TYPES.WASH_DRY_FOLD) && 
          (!data.selectedClientItems || data.selectedClientItems.length === 0)) {
        errors.selectedClientItems = {
          type: "manual",
          message: "Please select at least one item for wash & fold service"
        };
      }
      
      if (selectedServiceTypes.includes(SERVICE_TYPES.DRY_CLEANING) && 
          (!data.dryCleaningItems || data.dryCleaningItems.length === 0)) {
        errors.dryCleaningItems = {
          type: "manual",
          message: "Please select at least one dry cleaning item"
        };
      }
    }
    
    // If there are validation errors, set them and return
    if (Object.keys(errors).length > 0) {
      Object.entries(errors).forEach(([field, error]) => {
        form.setError(field as any, error);
      });
      return;
    }

    // Only proceed with form submission when validation passes
    // Include isDryCleaning property derived from serviceType for backward compatibility
    onSubmit({
      ...data,
      orderAmount: priceBreakdown.totalPrice,
      vatAmount: priceBreakdown.vatAmount,
      subtotal: priceBreakdown.subtotal,
      isDryCleaning: selectedServiceTypes.includes(SERVICE_TYPES.DRY_CLEANING)
    });
  };

  return {
    form,
    priceBreakdown,
    setPriceBreakdown,
    handleServiceTypeToggle,
    handleSubmit,
    isSubmitting
  };
}
