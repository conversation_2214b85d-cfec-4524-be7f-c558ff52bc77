
import { useState, useEffect, useCallback } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { UserRole } from './types';
import { determineUserRole } from './userRoleUtils';
import { auditService } from '@/services/audit/auditService';

export function useAuthProvider() {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [userRole, setUserRole] = useState<UserRole>(null);
  const [isClientUser, setIsClientUser] = useState(false);
  const [roleRetries, setRoleRetries] = useState(0);

  // Determine user role with retries
  const determineAndSetUserRole = useCallback(async (currentUser: User) => {
    try {
      const role = await determineUserRole(currentUser);
      setUserRole(role);
      setIsClientUser(role === 'client');
      
      console.log("User role determined:", role, "isClient:", role === 'client');
      
      // Reset retries on success
      setRoleRetries(0);
      
      return role;
    } catch (error) {
      console.error("Error determining user role:", error);
      return null;
    }
  }, []);

  // Handle auth state change
  useEffect(() => {
    // Set up auth state listener FIRST - to prevent missing auth events
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, newSession) => {
        console.log("Auth state changed:", event, newSession?.user?.email);
        
        if (event === 'TOKEN_REFRESHED' || event === 'SIGNED_IN') {
          setSession(newSession);
          setUser(newSession?.user ?? null);
          
          if (newSession?.user) {
            // Important: Defer Supabase calls to prevent deadlocks
            setTimeout(async () => {
              try {
                const role = await determineAndSetUserRole(newSession.user);
                
                // Log authentication event if this is an actual sign-in (not token refresh)
                if (event === 'SIGNED_IN' && role) {
                  await auditService.logLogin(newSession.user, role || undefined);
                }
              } catch (error) {
                console.error("Error setting user role:", error);
              } finally {
                setIsLoading(false);
              }
            }, 0);
          } else {
            setIsLoading(false);
          }
        } else if (event === 'SIGNED_OUT') {
          setSession(null);
          setUser(null);
          setIsClientUser(false);
          setUserRole(null);
          setRoleRetries(0);
          // Clear autoLogin flag
          localStorage.removeItem("autoLogin");
          setIsLoading(false);
        } else {
          setIsLoading(false);
        }
      }
    );

    // THEN check for existing session
    const checkInitialSession = async () => {
      try {
        const { data: { session: currentSession }, error } = await supabase.auth.getSession();
        
        if (error) {
          console.error("Error getting initial session:", error);
          setIsLoading(false);
          return;
        }
        
        console.log("Initial session check:", currentSession?.user?.email);
        
        if (currentSession?.user) {
          setSession(currentSession);
          setUser(currentSession.user);
          
          try {
            await determineAndSetUserRole(currentSession.user);
          } catch (error) {
            console.error("Error setting initial user role:", error);
          } finally {
            setIsLoading(false);
          }
        } else {
          // Check if we have autoLogin flag and attempt to restore session
          const shouldAutoLogin = localStorage.getItem("autoLogin") === "true";
          if (shouldAutoLogin) {
            console.log("AutoLogin flag detected, attempting to refresh session...");
            try {
              const { data, error } = await supabase.auth.refreshSession();
              if (error) {
                console.warn("Session refresh failed, clearing autoLogin flag:", error.message);
                localStorage.removeItem("autoLogin");
              }
            } catch (err) {
              console.error("Error refreshing session:", err);
              localStorage.removeItem("autoLogin");
            }
          }
          
          setSession(null);
          setUser(null);
          setIsLoading(false);
        }
      } catch (err) {
        console.error("Unexpected error during session check:", err);
        setIsLoading(false);
      }
    };
    
    checkInitialSession();

    return () => {
      subscription.unsubscribe();
    };
  }, [determineAndSetUserRole]);

  // Sign in function
  const signIn = async (email: string, password: string) => {
    try {
      setIsLoading(true);
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });
      
      if (error) {
        console.error("Login error:", error.message);
        return { success: false, error: error.message };
      }
      
      if (data.session) {
        // Set autoLogin flag for persistence
        localStorage.setItem("autoLogin", "true");
        
        // Determine role immediately after login to prevent redirect issues
        if (data.user) {
          const role = await determineAndSetUserRole(data.user);
          
          if (role) {
            toast.success("Logged in successfully");
            // Audit log is handled by the auth state change listener
            return { success: true };
          } else {
            toast.error("Unable to determine user role. Please contact support.");
            await signOut(); // Sign out if role determination fails
            return { success: false, error: "Unable to determine user role" };
          }
        }
        
        return { success: true };
      }
      
      return { success: false, error: "Unknown login error" };
    } catch (error: any) {
      console.error("Exception during login:", error);
      return { success: false, error: error.message || "Unknown error occurred" };
    } finally {
      setIsLoading(false);
    }
  };

  // Sign out function
  const signOut = async () => {
    try {
      setIsLoading(true);
      
      // Log logout before actual logout
      if (user && userRole) {
        await auditService.logLogout(user, userRole);
      }
      
      // Clear the autoLogin flag before actual signout
      localStorage.removeItem("autoLogin");
      
      const { error } = await supabase.auth.signOut();
      
      if (error) {
        console.error("Logout error:", error);
        toast.error("Error during logout");
        return;
      }
      
      // Session clearing happens in onAuthStateChange listener
      toast.success("Logged out successfully");
    } catch (error: any) {
      console.error("Exception during logout:", error);
      toast.error(error.message || "Error during logout");
    } finally {
      setIsLoading(false);
    }
  };

  // Refresh session
  const refreshSession = async () => {
    try {
      setIsLoading(true);
      const { data, error } = await supabase.auth.refreshSession();
      
      if (error) {
        // Handle refresh token errors gracefully
        if (error.message.includes("Refresh Token") || error.message.includes("token")) {
          console.log("Refresh token issue, clearing session state");
          setSession(null);
          setUser(null);
          setUserRole(null);
          setIsClientUser(false);
          localStorage.removeItem("autoLogin");
        } else {
          console.error("Session refresh error:", error);
        }
      } else if (data.user) {
        // If we have a successful refresh, try to determine role again
        await determineAndSetUserRole(data.user);
      }
    } catch (error: any) {
      console.error("Exception during session refresh:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return {
    user,
    session,
    isLoading,
    isAuthenticated: !!session,
    userRole,
    isClientUser,
    signIn,
    signOut,
    refreshSession
  };
}
