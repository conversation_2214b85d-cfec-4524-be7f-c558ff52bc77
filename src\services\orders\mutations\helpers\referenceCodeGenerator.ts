
/**
 * Generates appropriate reference code for an order based on order data
 */
export function generateReferenceCode(orderData: {
  reference_code?: string;
  customerType?: string;
  prefix?: string;
  clientId?: string;
  clientPrefix?: string;
}): string {
  // Use reference code if provided
  if (orderData.reference_code) {
    return orderData.reference_code;
  }
  
  const timestamp = Date.now().toString().slice(-6);
  
  // Generate based on customer type
  if (orderData.customerType === 'client') {
    // Use client prefix if available, prioritizing clientPrefix over prefix
    const prefixToUse = orderData.clientPrefix || orderData.prefix;
    
    if (prefixToUse) {
      return `${prefixToUse.toUpperCase()}-${timestamp}`;
    } else {
      return `CLT-${timestamp}`; // Generic client prefix as fallback
    }
  } else {
    return `ORD-${timestamp}`; // Default to ORD for walk-in
  }
}
