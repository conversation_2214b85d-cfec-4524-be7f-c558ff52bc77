
/**
 * Generates appropriate reference code for an order based on order data
 */
export function generateReferenceCode(orderData: {
  reference_code?: string;
  customerType?: string;
  prefix?: string;
  clientId?: string;
}): string {
  // Use reference code if provided
  if (orderData.reference_code) {
    return orderData.reference_code;
  }
  
  const timestamp = Date.now().toString().slice(-6);
  
  // Generate based on customer type
  if (orderData.customerType === 'client' && orderData.prefix) {
    return `${orderData.prefix.toUpperCase()}-${timestamp}`;
  } else if (orderData.customerType === 'client') {
    return `CLT-${timestamp}`; // Generic client prefix
  } else {
    return `ORD-${timestamp}`; // Default to ORD for walk-in
  }
}
