
import { ClientItemWithQuantity, DryCleaningItem } from "../OrderFormTypes";
import { formatCurrency } from "@/lib/utils";

interface ClientItemsBreakdownProps {
  items: ClientItemWithQuantity[];
  basePrice: number;
}

export function ClientItemsBreakdown({ items, basePrice }: ClientItemsBreakdownProps) {
  if (!items || items.length === 0) {
    return null;
  }

  return (
    <div className="space-y-2">
      {items.map((item, index) => {
        // Use unitPrice instead of unit_price
        const price = item.unitPrice || 0;
        const quantity = item.quantity || 1;
        const total = price * quantity;
        
        return (
          <div key={item.instanceId || index} className="flex justify-between text-sm">
            <span>
              {item.name} × {quantity}
              {item.treatments?.useStainRemoval && ' (Stain)'}
              {item.treatments?.useBeachTreatment && ' (Bleach)'}
            </span>
            <span>{formatCurrency(total)}</span>
          </div>
        );
      })}
    </div>
  );
}

interface DryCleaningItemsBreakdownProps {
  items: DryCleaningItem[];
  basePrice: number;
}

export function DryCleaningItemsBreakdown({ items, basePrice }: DryCleaningItemsBreakdownProps) {
  if (!items || items.length === 0) {
    return null;
  }

  return (
    <div className="space-y-2">
      {items.map((item, index) => {
        const total = item.price * item.quantity;
        const displayName = item.name || formatItemType(item.type);
        
        return (
          <div key={index} className="flex justify-between text-sm">
            <span>{displayName} × {item.quantity}</span>
            <span>{formatCurrency(total)}</span>
          </div>
        );
      })}
    </div>
  );
}

// Helper function to format item type
function formatItemType(type: string): string {
  return type.replace(/_/g, ' ').replace(/\w\S*/g, txt => 
    txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
  );
}
