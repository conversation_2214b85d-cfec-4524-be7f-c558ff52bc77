
import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Minus, Plus } from "lucide-react";

// Define the dry cleaning item prices
const DRY_CLEANING_PRICES = {
  BARONG_TAGALOG_JUSI: 250.00,
  BARONG_TAGALOG_PINA: 300.00,
  COAT: 250.00,
  LONG_PANTS: 170.00,
  SIMPLE_GOWN: 300.00,
  BEADED_GOWN: 400.00,
  WEDDING_GOWN: 1500.00,
  SHOES: 350.00,
  BLOUSE: 150.00,
  SKIRT: 180.00,
  POLO: 150.00,
  SLACKS_TROUSERS: 170.00,
}

interface DryCleaningItem {
  type: string;
  name: string;
  price: number;
  quantity: number;
}

interface DryCleaningItemsSectionProps {
  isDryCleaning: boolean;
  items?: DryCleaningItem[];
  onItemsChange?: (items: DryCleaningItem[]) => void;
}

export function DryCleaningItemsSection({ 
  isDryCleaning,
  items = [],
  onItemsChange = () => {}
}: DryCleaningItemsSectionProps) {
  const [dryCleaningItems, setDryCleaningItems] = useState<DryCleaningItem[]>(items);
  
  if (!isDryCleaning) return null;
  
  const updateItemQuantity = (itemType: string, itemName: string, price: number, amount: number) => {
    const existingItemIndex = dryCleaningItems.findIndex(item => item.type === itemType);
    
    if (existingItemIndex >= 0) {
      // Update existing item
      const updatedItems = [...dryCleaningItems];
      const newQuantity = Math.max(0, updatedItems[existingItemIndex].quantity + amount);
      
      if (newQuantity === 0) {
        // Remove item if quantity is zero
        updatedItems.splice(existingItemIndex, 1);
      } else {
        // Update quantity
        updatedItems[existingItemIndex] = {
          ...updatedItems[existingItemIndex],
          quantity: newQuantity
        };
      }
      
      setDryCleaningItems(updatedItems);
      onItemsChange(updatedItems);
    } else if (amount > 0) {
      // Add new item
      const newItem: DryCleaningItem = {
        type: itemType,
        name: itemName,
        price,
        quantity: 1
      };
      
      const updatedItems = [...dryCleaningItems, newItem];
      setDryCleaningItems(updatedItems);
      onItemsChange(updatedItems);
    }
  };
  
  const getItemQuantity = (itemType: string): number => {
    const item = dryCleaningItems.find(item => item.type === itemType);
    return item ? item.quantity : 0;
  };
  
  const formatPrice = (price: number): string => {
    return `₱${price.toFixed(2)}`;
  };
  
  return (
    <div className="mt-6">
      <h4 className="font-medium mb-3">Select Dry Cleaning Items</h4>
      
      <div className="grid grid-cols-2 gap-3 mb-3">
        {/* Barong Tagalog (Jusi) */}
        <div className="border rounded-md p-3 bg-white">
          <div className="flex justify-between items-center">
            <div>
              <h5 className="font-medium text-sm">Barong Tagalog (Jusi)</h5>
              <p className="text-xs text-gray-500">{formatPrice(DRY_CLEANING_PRICES.BARONG_TAGALOG_JUSI)}</p>
            </div>
            <div className="flex items-center space-x-2">
              <Button 
                type="button" 
                size="sm" 
                variant="outline" 
                className="h-7 w-7 p-0"
                onClick={() => updateItemQuantity('BARONG_TAGALOG_JUSI', 'Barong Tagalog (Jusi)', DRY_CLEANING_PRICES.BARONG_TAGALOG_JUSI, -1)}
                disabled={getItemQuantity('BARONG_TAGALOG_JUSI') === 0}
              >
                <Minus className="h-3 w-3" />
              </Button>
              <span className="w-4 text-center text-sm">{getItemQuantity('BARONG_TAGALOG_JUSI')}</span>
              <Button
                type="button"
                size="sm"
                variant="outline"
                className="h-7 w-7 p-0"
                onClick={() => updateItemQuantity('BARONG_TAGALOG_JUSI', 'Barong Tagalog (Jusi)', DRY_CLEANING_PRICES.BARONG_TAGALOG_JUSI, 1)}
              >
                <Plus className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </div>
        
        {/* Barong Tagalog (Piña) */}
        <div className="border rounded-md p-3 bg-white">
          <div className="flex justify-between items-center">
            <div>
              <h5 className="font-medium text-sm">Barong Tagalog (Piña)</h5>
              <p className="text-xs text-gray-500">{formatPrice(DRY_CLEANING_PRICES.BARONG_TAGALOG_PINA)}</p>
            </div>
            <div className="flex items-center space-x-2">
              <Button 
                type="button" 
                size="sm" 
                variant="outline" 
                className="h-7 w-7 p-0"
                onClick={() => updateItemQuantity('BARONG_TAGALOG_PINA', 'Barong Tagalog (Piña)', DRY_CLEANING_PRICES.BARONG_TAGALOG_PINA, -1)}
                disabled={getItemQuantity('BARONG_TAGALOG_PINA') === 0}
              >
                <Minus className="h-3 w-3" />
              </Button>
              <span className="w-4 text-center text-sm">{getItemQuantity('BARONG_TAGALOG_PINA')}</span>
              <Button
                type="button"
                size="sm"
                variant="outline"
                className="h-7 w-7 p-0"
                onClick={() => updateItemQuantity('BARONG_TAGALOG_PINA', 'Barong Tagalog (Piña)', DRY_CLEANING_PRICES.BARONG_TAGALOG_PINA, 1)}
              >
                <Plus className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </div>
        
        {/* Coat */}
        <div className="border rounded-md p-3 bg-white">
          <div className="flex justify-between items-center">
            <div>
              <h5 className="font-medium text-sm">Coat</h5>
              <p className="text-xs text-gray-500">{formatPrice(DRY_CLEANING_PRICES.COAT)}</p>
            </div>
            <div className="flex items-center space-x-2">
              <Button 
                type="button" 
                size="sm" 
                variant="outline" 
                className="h-7 w-7 p-0"
                onClick={() => updateItemQuantity('COAT', 'Coat', DRY_CLEANING_PRICES.COAT, -1)}
                disabled={getItemQuantity('COAT') === 0}
              >
                <Minus className="h-3 w-3" />
              </Button>
              <span className="w-4 text-center text-sm">{getItemQuantity('COAT')}</span>
              <Button
                type="button"
                size="sm"
                variant="outline"
                className="h-7 w-7 p-0"
                onClick={() => updateItemQuantity('COAT', 'Coat', DRY_CLEANING_PRICES.COAT, 1)}
              >
                <Plus className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </div>
        
        {/* Long Pants */}
        <div className="border rounded-md p-3 bg-white">
          <div className="flex justify-between items-center">
            <div>
              <h5 className="font-medium text-sm">Long Pants</h5>
              <p className="text-xs text-gray-500">{formatPrice(DRY_CLEANING_PRICES.LONG_PANTS)}</p>
            </div>
            <div className="flex items-center space-x-2">
              <Button 
                type="button" 
                size="sm" 
                variant="outline" 
                className="h-7 w-7 p-0"
                onClick={() => updateItemQuantity('LONG_PANTS', 'Long Pants', DRY_CLEANING_PRICES.LONG_PANTS, -1)}
                disabled={getItemQuantity('LONG_PANTS') === 0}
              >
                <Minus className="h-3 w-3" />
              </Button>
              <span className="w-4 text-center text-sm">{getItemQuantity('LONG_PANTS')}</span>
              <Button
                type="button"
                size="sm"
                variant="outline"
                className="h-7 w-7 p-0"
                onClick={() => updateItemQuantity('LONG_PANTS', 'Long Pants', DRY_CLEANING_PRICES.LONG_PANTS, 1)}
              >
                <Plus className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </div>
        
        {/* Simple Gown */}
        <div className="border rounded-md p-3 bg-white">
          <div className="flex justify-between items-center">
            <div>
              <h5 className="font-medium text-sm">Simple Gown</h5>
              <p className="text-xs text-gray-500">{formatPrice(DRY_CLEANING_PRICES.SIMPLE_GOWN)}</p>
            </div>
            <div className="flex items-center space-x-2">
              <Button 
                type="button" 
                size="sm" 
                variant="outline" 
                className="h-7 w-7 p-0"
                onClick={() => updateItemQuantity('SIMPLE_GOWN', 'Simple Gown', DRY_CLEANING_PRICES.SIMPLE_GOWN, -1)}
                disabled={getItemQuantity('SIMPLE_GOWN') === 0}
              >
                <Minus className="h-3 w-3" />
              </Button>
              <span className="w-4 text-center text-sm">{getItemQuantity('SIMPLE_GOWN')}</span>
              <Button
                type="button"
                size="sm"
                variant="outline"
                className="h-7 w-7 p-0"
                onClick={() => updateItemQuantity('SIMPLE_GOWN', 'Simple Gown', DRY_CLEANING_PRICES.SIMPLE_GOWN, 1)}
              >
                <Plus className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </div>
        
        {/* Beaded Gown */}
        <div className="border rounded-md p-3 bg-white">
          <div className="flex justify-between items-center">
            <div>
              <h5 className="font-medium text-sm">Beaded Gown</h5>
              <p className="text-xs text-gray-500">{formatPrice(DRY_CLEANING_PRICES.BEADED_GOWN)}</p>
            </div>
            <div className="flex items-center space-x-2">
              <Button 
                type="button" 
                size="sm" 
                variant="outline" 
                className="h-7 w-7 p-0"
                onClick={() => updateItemQuantity('BEADED_GOWN', 'Beaded Gown', DRY_CLEANING_PRICES.BEADED_GOWN, -1)}
                disabled={getItemQuantity('BEADED_GOWN') === 0}
              >
                <Minus className="h-3 w-3" />
              </Button>
              <span className="w-4 text-center text-sm">{getItemQuantity('BEADED_GOWN')}</span>
              <Button
                type="button"
                size="sm"
                variant="outline"
                className="h-7 w-7 p-0"
                onClick={() => updateItemQuantity('BEADED_GOWN', 'Beaded Gown', DRY_CLEANING_PRICES.BEADED_GOWN, 1)}
              >
                <Plus className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </div>
      </div>
      
      {dryCleaningItems.length > 0 && (
        <div className="mt-4 p-3 border rounded-md bg-amber-50">
          <h5 className="font-medium">Selected Items</h5>
          <div className="space-y-1 mt-2">
            {dryCleaningItems.map((item, index) => (
              <div key={index} className="flex justify-between text-sm">
                <span>{item.name} x{item.quantity}</span>
                <span>{formatPrice(item.price * item.quantity)}</span>
              </div>
            ))}
            <div className="border-t pt-2 mt-2 font-medium flex justify-between">
              <span>Total</span>
              <span>{formatPrice(dryCleaningItems.reduce((sum, item) => sum + (item.price * item.quantity), 0))}</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
