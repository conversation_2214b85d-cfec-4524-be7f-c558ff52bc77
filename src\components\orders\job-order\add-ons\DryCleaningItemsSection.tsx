
import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Minus, Plus, Loader2 } from "lucide-react";
import { getDryCleaningPrices } from "@/services/pricing/pricingService";
import { DryCleaningItem } from "@/types";

interface DryCleaningItemsSectionProps {
  isDryCleaning: boolean;
  items?: DryCleaningItem[];
  onItemsChange?: (items: DryCleaningItem[]) => void;
}

export function DryCleaningItemsSection({ 
  isDryCleaning,
  items = [],
  onItemsChange = () => {}
}: DryCleaningItemsSectionProps) {
  const [dryCleaningItems, setDryCleaningItems] = useState<DryCleaningItem[]>([]);
  const [prices, setPrices] = useState<Record<string, number>>({});
  const [isLoading, setIsLoading] = useState(true);
  
  // Load dry cleaning prices from the service
  useEffect(() => {
    async function loadPrices() {
      try {
        setIsLoading(true);
        const currentPrices = await getDryCleaningPrices();
        console.log("Loaded dry cleaning prices in DryCleaningItemsSection:", currentPrices);
        setPrices(currentPrices);
      } catch (err) {
        console.error("Error loading dry cleaning prices:", err);
      } finally {
        setIsLoading(false);
      }
    }
    
    if (isDryCleaning) {
      loadPrices();
    }
  }, [isDryCleaning]);
  
  // Initialize with items from props when they change
  useEffect(() => {
    if (items && items.length > 0) {
      console.log("Setting dry cleaning items from props:", items);
      
      // Map the items from props and ensure all required properties are present
      const mappedItems = items.map(item => ({
        type: item.type,
        quantity: item.quantity || 1, // Ensure quantity is at least 1
        price: item.price || prices[item.type] || 0,
        total: item.total || (item.quantity || 1) * (item.price || prices[item.type] || 0)
      }));
      
      setDryCleaningItems(mappedItems);
    } else {
      // Reset to empty array if no items
      setDryCleaningItems([]);
    }
  }, [items, prices]);
  
  // Notify parent when items change
  useEffect(() => {
    onItemsChange(dryCleaningItems);
  }, [dryCleaningItems, onItemsChange]);
  
  if (!isDryCleaning) return null;
  
  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-6">
        <Loader2 className="h-6 w-6 animate-spin mr-2" />
        <span>Loading pricing information...</span>
      </div>
    );
  }
  
  const updateItemQuantity = (itemType: string, price: number, amount: number) => {
    const existingItemIndex = dryCleaningItems.findIndex(item => item.type === itemType);
    
    if (existingItemIndex >= 0) {
      // Update existing item
      const updatedItems = [...dryCleaningItems];
      const newQuantity = Math.max(0, updatedItems[existingItemIndex].quantity + amount);
      
      if (newQuantity === 0) {
        // Remove item if quantity is zero
        updatedItems.splice(existingItemIndex, 1);
      } else {
        // Update quantity
        updatedItems[existingItemIndex] = {
          ...updatedItems[existingItemIndex],
          quantity: newQuantity,
          total: price * newQuantity
        };
      }
      
      setDryCleaningItems(updatedItems);
    } else if (amount > 0) {
      // Add new item with a minimum quantity of 1
      const newItem: DryCleaningItem = {
        type: itemType,
        price,
        quantity: 1,
        total: price
      };
      
      setDryCleaningItems([...dryCleaningItems, newItem]);
    }
  };
  
  const getItemQuantity = (itemType: string): number => {
    const item = dryCleaningItems.find(item => item.type === itemType);
    return item ? item.quantity : 0;
  };
  
  const formatPrice = (price: number): string => {
    return `₱${price.toFixed(2)}`;
  };
  
  // Define the items with pricing from the service
  const itemsData = [
    { type: "BARONG_TAGALOG_JUSI", displayName: "Barong Tagalog (Jusi)", priceKey: "BARONG_TAGALOG_JUSI" },
    { type: "BARONG_TAGALOG_PINA", displayName: "Barong Tagalog (Piña)", priceKey: "BARONG_TAGALOG_PINA" },
    { type: "COAT", displayName: "Coat", priceKey: "COAT" },
    { type: "LONG_PANTS", displayName: "Long Pants", priceKey: "LONG_PANTS" },
    { type: "SIMPLE_GOWN", displayName: "Simple Gown", priceKey: "SIMPLE_GOWN" },
    { type: "BEADED_GOWN", displayName: "Beaded Gown", priceKey: "BEADED_GOWN" }
  ];
  
  return (
    <div className="mt-6">
      <h4 className="font-medium mb-3">Select Dry Cleaning Items</h4>
      
      <div className="grid grid-cols-2 gap-3 mb-3">
        {itemsData.map(item => {
          const price = prices[item.priceKey] || 0;
          const quantity = getItemQuantity(item.type);
          
          return (
            <div key={item.type} className={`border rounded-md p-3 ${quantity > 0 ? 'bg-amber-50 border-amber-200' : 'bg-white'}`}>
              <div className="flex justify-between items-center">
                <div>
                  <h5 className={`font-medium text-sm ${quantity > 0 ? 'text-amber-800' : ''}`}>{item.displayName}</h5>
                  <p className="text-xs text-gray-500">{formatPrice(price)}</p>
                </div>
                <div className="flex items-center space-x-2">
                  <Button 
                    type="button" 
                    size="sm" 
                    variant="outline" 
                    className="h-7 w-7 p-0"
                    onClick={() => updateItemQuantity(item.type, price, -1)}
                    disabled={quantity === 0}
                  >
                    <Minus className="h-3 w-3" />
                  </Button>
                  
                  <span className="w-8 text-center">{quantity}</span>
                  
                  <Button 
                    type="button"
                    size="sm"
                    variant="outline"
                    className="h-7 w-7 p-0"
                    onClick={() => updateItemQuantity(item.type, price, 1)}
                  >
                    <Plus className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            </div>
          );
        })}
      </div>
      
      {dryCleaningItems.length > 0 && (
        <div className="mt-4 p-3 border-t">
          <div className="flex justify-between font-medium">
            <span>Total Dry Cleaning Items:</span>
            <span>
              {dryCleaningItems.reduce((sum, item) => sum + item.quantity, 0)} items - 
              {formatPrice(dryCleaningItems.reduce((sum, item) => sum + (item.total || 0), 0))}
            </span>
          </div>
        </div>
      )}
    </div>
  );
}
