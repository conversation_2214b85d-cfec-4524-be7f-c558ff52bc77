
import { useState, useMemo, useCallback } from "react";
import { Tabs, TabsContent } from "@/components/ui/tabs";
import { OrderStagesList } from "@/components/orders/status/OrderStagesList";
import { OrderTypeTabs } from "@/components/orders/status/OrderTypeTabs";
import { OrderCard } from "@/components/orders/status/OrderCard";
import { Order, OrderType, Stage } from "@/components/orders/status/types";
import { OrdersNavigation } from "@/components/orders/page/OrdersNavigation";
import { useOrderStatus } from "@/hooks/orders/useOrderStatus";
import { Loader2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

// Order stage types aligned with our actual status values in the system
const stages: Stage[] = [
  { id: "pending", name: "Pending", color: "text-gray-700" },
  { id: "processing", name: "Processing", color: "text-indigo-700" },
  { id: "for_pickup", name: "For Pickup", color: "text-blue-700" },
  { id: "pickup_complete", name: "Pickup Complete", color: "text-green-700" },
  { id: "for_treatment", name: "For Treatment", color: "text-orange-700" },
  { id: "hard_stain", name: "Hard Stain", color: "text-red-700" },
  { id: "partial_delivery", name: "Partial Delivery", color: "text-amber-700" },
  { id: "fulfilled", name: "Delivery Complete", color: "text-purple-700" },
  { id: "cancelled", name: "Cancelled", color: "text-rose-700" }
];

export default function OrderStatus() {
  const [selectedStage, setSelectedStage] = useState<string>("all");
  const [orderType, setOrderType] = useState<OrderType>("client");
  const { orders, isLoading, error, refreshOrders } = useOrderStatus();
  const { toast } = useToast();
  
  // Event handlers defined with useCallback to preserve reference equality
  const handleStageChange = useCallback((stageId: string) => {
    setSelectedStage(stageId);
  }, []);
  
  const handleOrderTypeChange = useCallback((type: OrderType) => {
    setOrderType(type);
  }, []);
  
  const handleOrderStatusChange = useCallback(() => {
    refreshOrders();
    toast({
      title: "Orders Updated",
      description: "The order status has been updated successfully."
    });
  }, [refreshOrders, toast]);
  
  // Memoize filtered orders to prevent unnecessary calculations on re-renders
  const filteredOrders = useMemo(() => {
    return orders.filter(order => {
      const matchesStage = selectedStage === "all" || order.stage === selectedStage;
      const matchesType = orderType === "client" 
        ? order.isClientOrder
        : !order.isClientOrder;
      return matchesStage && matchesType;
    });
  }, [selectedStage, orderType, orders]);

  // Get stage info for each order - memoized to prevent recalculation
  const getStageInfo = useCallback((stageId: string): Stage => {
    return stages.find(s => s.id === stageId) || stages[0];
  }, []);

  return (
    <div className="space-y-6">
      <OrdersNavigation />
      
      <h1 className="text-2xl font-bold tracking-tight">Order Status Track</h1>
      
      <Tabs 
        value={orderType} 
        onValueChange={handleOrderTypeChange}
        className="w-full"
      >
        <OrderTypeTabs value={orderType} onValueChange={handleOrderTypeChange} />
      </Tabs>

      <Tabs defaultValue="all" className="w-full" onValueChange={handleStageChange}>
        <OrderStagesList 
          stages={stages} 
          selectedStage={selectedStage} 
          onStageChange={handleStageChange} 
        />

        <TabsContent value={selectedStage} className="mt-0">
          {isLoading ? (
            <div className="flex justify-center items-center h-40">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2 text-lg">Loading orders...</span>
            </div>
          ) : error ? (
            <div className="flex flex-col items-center justify-center h-40 bg-white rounded-md border p-6">
              <p className="text-red-500">Error loading orders: {error}</p>
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredOrders.map((order) => {
                  const currentStage = getStageInfo(order.stage);
                  
                  return (
                    <OrderCard
                      key={order.id}
                      order={order}
                      currentStage={currentStage}
                      stages={stages}
                      onStatusChange={handleOrderStatusChange}
                    />
                  );
                })}
              </div>
              
              {filteredOrders.length === 0 && (
                <div className="flex flex-col items-center justify-center h-40 bg-white rounded-md border p-6">
                  <p className="text-muted-foreground">No orders in this stage</p>
                </div>
              )}
            </>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
