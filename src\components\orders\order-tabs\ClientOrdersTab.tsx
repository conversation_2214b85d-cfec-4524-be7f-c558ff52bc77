
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { AddOrderDialog } from "@/components/orders/AddOrderDialog";
import { Order } from "@/types";
import { useIsMobile } from "@/hooks/use-mobile";
import { OrdersDataTable } from "@/components/orders/table/OrdersDataTable";
import { useToast } from "@/hooks/use-toast";
import { useOrdersManager } from "@/hooks/orders/useOrdersManager";
import { OrderFilters } from "../filters/OrderFilters";
import { useOrderFilters } from "@/hooks/orders/useOrderFilters";

interface ClientOrdersTabProps {
  orders: Order[];
  onOrderAdded: () => void;
}

export function ClientOrdersTab({
  orders,
  onOrderAdded
}: ClientOrdersTabProps) {
  const [isAddOrderOpen, setIsAddOrderOpen] = useState(false);
  const isMobile = useIsMobile();
  const { toast } = useToast();
  
  // Filter for client orders only - improved filtering logic
  const clientOrders = orders.filter(order => order.customerType === "client");
  
  console.log(`ClientOrdersTab: Filtered ${orders.length} orders to ${clientOrders.length} client orders`);
  
  // Use the shared hook to handle order actions
  const {
    handleStatusChange,
    handleBatchStatusUpdate,
    handlePrintOrder
  } = useOrdersManager(clientOrders);
  
  // Use the filter hook
  const {
    searchTerm,
    setSearchTerm,
    statusFilter,
    setStatusFilter,
    dateFilter,
    setDateFilter,
    clientFilter,
    setClientFilter,
    filteredOrders,
    getClientOptions
  } = useOrderFilters(clientOrders);
  
  // Get client options for the filter
  const clientOptions = getClientOptions(clientOrders);
  
  return (
    <>
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-4">
        <h2 className={`${isMobile ? "text-base" : "text-lg"} font-semibold`}>Client Orders</h2>
        <Button 
          className="bg-laundry-blue hover:bg-laundry-darkBlue w-full md:w-auto"
          onClick={() => setIsAddOrderOpen(true)}
        >
          <Plus className="mr-2 h-4 w-4" /> {isMobile ? "New Client Order" : "Add New Client Order"}
        </Button>
      </div>
      
      <div className="mb-6">
        <OrderFilters
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          statusFilter={statusFilter}
          setStatusFilter={setStatusFilter}
          dateFilter={dateFilter}
          setDateFilter={setDateFilter}
          clientFilter={clientFilter}
          setClientFilter={setClientFilter}
          clientOptions={clientOptions}
        />
      </div>
      
      <OrdersDataTable 
        data={filteredOrders} 
        filterType="client"
        onStatusChange={handleStatusChange}
        onBatchStatusChange={handleBatchStatusUpdate}
        onPrintOrder={handlePrintOrder}
      />
      
      <AddOrderDialog 
        open={isAddOrderOpen} 
        onOpenChange={setIsAddOrderOpen} 
        onOrderAdded={onOrderAdded}
        initialOrderType="client"
      />
    </>
  );
}
