
import { ClientItem } from "@/services/clientItemService";
import { ClientItemWithQuantity } from "./OrderFormTypes";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ClientItemRow } from "./ClientItemRow";
import { useIsMobile } from "@/hooks/use-mobile";

interface ClientItemsTableProps {
  items: ClientItem[];
  selectedItems: ClientItemWithQuantity[];
  onItemToggle: (item: ClientItem, isSelected: boolean) => void;
  onQuantityChange: (itemId: string, newQuantity: number) => void;
  unitPrice: number;
}

export function ClientItemsTable({ 
  items, 
  selectedItems, 
  onItemToggle, 
  onQuantityChange,
  unitPrice
}: ClientItemsTableProps) {
  const isMobile = useIsMobile();

  if (items.length === 0) {
    return (
      <div className="text-center p-8 border rounded-md bg-muted/20">
        <p className="text-muted-foreground">No items available for this client.</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg border shadow-sm overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className={isMobile ? "w-[40px] px-2" : "w-[60px]"}></TableHead>
            <TableHead className={isMobile ? "px-2" : ""}>Item</TableHead>
            <TableHead className={isMobile ? "w-[100px] px-1" : "w-[160px]"}>Quantity</TableHead>
            <TableHead className={`text-right ${isMobile ? "px-2" : ""}`}>Total</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {items.map((item) => {
            const selectedItem = selectedItems.find(i => i.id === item.id);
            const isSelected = !!selectedItem;
            
            return (
              <ClientItemRow
                key={item.id}
                item={item}
                selectedItem={selectedItem}
                isSelected={isSelected}
                onToggle={onItemToggle}
                onQuantityChange={onQuantityChange}
                unitPrice={unitPrice}
                isMobile={isMobile}
              />
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
}
