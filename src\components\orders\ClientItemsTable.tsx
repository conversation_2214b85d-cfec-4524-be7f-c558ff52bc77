
import React from "react";
import { ClientItem } from "@/services/clientItemService";
import { ClientItemWithQuantity } from "./OrderFormTypes";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ClientItemRow } from "./item-row";
import { useIsMobile } from "@/hooks/use-mobile";
import { Checkbox } from "@/components/ui/checkbox";

interface ClientItemsTableProps {
  items: ClientItem[];
  selectedItems: ClientItemWithQuantity[];
  onItemToggle: (item: ClientItem, isSelected: boolean) => void;
  onQuantityChange: (instanceId: string, quantity: number) => void;
  onTreatmentChange: (instanceId: string, treatmentUpdates: Partial<ClientItemWithQuantity['treatments']>) => void;
  onDuplicate: (instanceId: string) => void;
  onRemove: (instanceId: string) => void;
  unitPrice: number;
}

export function ClientItemsTable({ 
  items, 
  selectedItems, 
  onItemToggle, 
  onQuantityChange,
  onTreatmentChange,
  onDuplicate,
  onRemove,
  unitPrice
}: ClientItemsTableProps) {
  const isMobile = useIsMobile();

  if (items.length === 0) {
    return (
      <div className="text-center p-8 border rounded-md bg-muted/20">
        <p className="text-muted-foreground">No items available for this client.</p>
      </div>
    );
  }

  // Group items by type for better organization
  const itemsByType = items.reduce((acc: Record<string, ClientItem[]>, item) => {
    const type = item.item_type || 'standard';
    if (!acc[type]) {
      acc[type] = [];
    }
    acc[type].push(item);
    return acc;
  }, {});

  return (
    <div className="bg-white rounded-lg border shadow-sm overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className={isMobile ? "w-[40px] px-2" : "w-[60px]"}></TableHead>
            <TableHead className={isMobile ? "px-2" : ""}>Item</TableHead>
            <TableHead className={isMobile ? "w-[140px] px-1" : "w-[240px]"}>Quantity</TableHead>
            <TableHead className={`text-right ${isMobile ? "px-2" : ""}`}>Total</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {Object.entries(itemsByType).map(([type, typeItems]) => (
            <React.Fragment key={type}>
              <TableRow className="bg-muted/20 hover:bg-muted/30">
                <TableCell colSpan={4} className="font-medium text-sm py-2 px-4 border-t">
                  {type.charAt(0).toUpperCase() + type.slice(1)} Items
                </TableCell>
              </TableRow>
              {typeItems.map((item) => {
                const isSelected = selectedItems.some(i => i.id === item.id);
                const itemInstances = selectedItems.filter(i => i.id === item.id);
                
                return (
                  <ClientItemRow
                    key={item.id}
                    item={item}
                    selectedItems={itemInstances}
                    isSelected={isSelected}
                    onToggle={onItemToggle}
                    onQuantityChange={onQuantityChange}
                    onTreatmentChange={onTreatmentChange}
                    onDuplicate={onDuplicate}
                    onRemove={onRemove}
                    unitPrice={unitPrice}
                    isMobile={isMobile}
                  />
                );
              })}
            </React.Fragment>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
