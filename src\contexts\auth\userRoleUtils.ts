
import { User } from '@supabase/supabase-js';
import { supabase } from "@/integrations/supabase/client";
import { UserRole } from './types';

// Unified role and client status determination
export const determineUserRole = async (user: User | null): Promise<UserRole> => {
  if (!user) return null;
  
  try {
    console.log("Determining role for user:", user.email);
    
    // First check for specific admin emails
    if (user.email === '<EMAIL>' || 
        user.email === '<EMAIL>') {
      console.log(`Default admin role assigned to ${user.email}`);
      return 'admin';
    }
    
    // Special <NAME_EMAIL>
    if (user.email === '<EMAIL>') {
      console.log(`Default staff role assigned to ${user.email}`);
      return 'staff';
    }
    
    // Special <NAME_EMAIL> or <EMAIL> - assigning client role
    if (user.email === '<EMAIL>' || 
        user.email === '<EMAIL>') {
      console.log(`Client role assigned to ${user.email}`);
      return 'client';
    }
    
    // Check for client association
    const { data: userClient, error: clientError } = await supabase
      .from('users_clients')
      .select('client_id')
      .eq('user_id', user.id)
      .maybeSingle();
    
    if (clientError) {
      console.error("Error checking client association:", clientError);
    }
    else if (userClient?.client_id) {
      console.log("User is associated with client:", userClient.client_id);
      return 'client';
    }
    
    // If not a client user, then check for explicit roles in user_roles table
    const { data: userRoles, error: rolesError } = await supabase
      .from('user_roles')
      .select('role')
      .eq('user_id', user.id);
    
    if (rolesError) {
      console.error("Error fetching user roles:", rolesError);
    } 
    else if (userRoles && userRoles.length > 0) {
      console.log("User roles found:", userRoles);
      
      // Check for admin role
      const adminRole = userRoles.find(r => r.role === 'admin');
      if (adminRole) {
        console.log("User has admin role");
        return 'admin';
      }
      
      // Check for staff role
      const staffRole = userRoles.find(r => r.role === 'staff');
      if (staffRole) {
        console.log("User has staff role");
        return 'staff';
      }
      
      // Check for client role
      const clientRole = userRoles.find(r => r.role === 'client');
      if (clientRole) {
        console.log("User has explicit client role");
        return 'client';
      }
    }
    
    // If no role could be determined, default to a null role
    console.log("No role found for user, defaulting to null");
    return null;
    
  } catch (error) {
    console.error("Error determining user role:", error);
    return null;
  }
};
