
import { supabase } from "@/integrations/supabase/client";
import { getAuthSession } from "../auth/utils";
import { User } from "@supabase/supabase-js";

// Ensure all audit action types are properly typed
export type AuditAction = 
  | 'create'
  | 'update'
  | 'delete'
  | 'login'
  | 'logout'
  | 'view'
  | 'update_status'
  | 'update_payment'
  | 'update_items'
  | 'update_addons';

export const auditService = {
  async logOrderAction(
    action: AuditAction,
    orderId: string,
    details: Record<string, any>
  ): Promise<boolean> {
    try {
      const session = await getAuthSession();
      
      // Handle action type discrepancy by mapping to base types
      let baseAction: 'create' | 'update' | 'delete' | 'login' | 'logout' | 'view' = 'update';
      
      if (action === 'create') baseAction = 'create';
      else if (action === 'delete') baseAction = 'delete';
      else if (action === 'login') baseAction = 'login';
      else if (action === 'logout') baseAction = 'logout';
      else if (action === 'view') baseAction = 'view';
      else baseAction = 'update'; // All other actions map to update
      
      // Log the action in the audit_logs table
      const { error } = await supabase.from('audit_logs').insert({
        entity_type: 'order',
        entity_id: orderId,
        action: baseAction,
        user_id: session?.user?.id,
        user_email: session?.user?.email,
        user_role: session?.user?.role || 'unknown',
        details: {
          ...details,
          original_action: action // Store the original action in details
        }
      });
      
      if (error) {
        console.error('Error logging audit action:', error);
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('Failed to log audit action:', error);
      return false;
    }
  },

  // Added method for client-related audit logs
  async logClientAction(
    action: 'create' | 'update' | 'delete',
    clientId: string,
    details: Record<string, any>
  ): Promise<boolean> {
    try {
      const session = await getAuthSession();
      
      // Log the client action in the audit_logs table
      const { error } = await supabase.from('audit_logs').insert({
        entity_type: 'client',
        entity_id: clientId,
        action: action,
        user_id: session?.user?.id,
        user_email: session?.user?.email,
        user_role: session?.user?.role || 'unknown',
        details: details
      });
      
      if (error) {
        console.error('Error logging client audit action:', error);
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('Failed to log client audit action:', error);
      return false;
    }
  },

  // Generic action logger for other entities
  async logAction(values: {
    entity_type: string;
    entity_id: string;
    action: 'create' | 'update' | 'delete' | 'login' | 'logout' | 'view';
    details: Record<string, any>;
  }): Promise<boolean> {
    try {
      const session = await getAuthSession();
      
      const { error } = await supabase.from('audit_logs').insert({
        entity_type: values.entity_type,
        entity_id: values.entity_id,
        action: values.action,
        user_id: session?.user?.id,
        user_email: session?.user?.email,
        user_role: session?.user?.role || 'unknown',
        details: values.details
      });
      
      if (error) {
        console.error('Error logging generic audit action:', error);
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('Failed to log generic audit action:', error);
      return false;
    }
  },

  // Authentication-related audit methods
  async logLogin(
    user: User,
    role: string = 'unknown'
  ): Promise<boolean> {
    try {
      const { error } = await supabase.from('audit_logs').insert({
        entity_type: 'authentication',
        entity_id: user.id,
        action: 'login',
        user_id: user.id,
        user_email: user.email,
        user_role: role,
        details: {
          method: 'password',
          timestamp: new Date().toISOString()
        }
      });
      
      if (error) {
        console.error('Error logging login action:', error);
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('Failed to log login action:', error);
      return false;
    }
  },

  async logLogout(
    user: User,
    role: string = 'unknown'
  ): Promise<boolean> {
    try {
      const { error } = await supabase.from('audit_logs').insert({
        entity_type: 'authentication',
        entity_id: user.id,
        action: 'logout',
        user_id: user.id,
        user_email: user.email,
        user_role: role,
        details: {
          method: 'explicit',
          timestamp: new Date().toISOString()
        }
      });
      
      if (error) {
        console.error('Error logging logout action:', error);
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('Failed to log logout action:', error);
      return false;
    }
  }
};
