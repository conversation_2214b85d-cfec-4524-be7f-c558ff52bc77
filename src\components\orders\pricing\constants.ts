
// Base price per kilogram
export const BASE_PRICE_PER_KILO = 85;

// VAT rate (12%)
export const VAT_RATE = 0.12;

// Add-on prices
export const ADDON_PRICES = {
  detergent: {
    regular: 30,
    color: 35
  },
  fabricConditioner: {
    regular: 30,
    fresh: 35,
    floral: 40
  },
  stainRemover: 15,
  bleach: 10
};

// Define different service types
export const SERVICE_TYPES = {
  WASH_DRY_FOLD: "wash_dry_fold",
  WASH_DRY_PRESS: "wash_dry_press",
  DRY_CLEANING: "dry_cleaning",
  IRONING_ONLY: "ironing_only",
  WASH_DRY_FOLD_SPECIAL: "wash_dry_fold_special",
  COMFORTERS: "comforters",
  TOWELS_CURTAINS_LINENS: "towels_curtains_linens"
};

// Prices for dry cleaning items (per piece)
export const DRY_CLEANING_PRICES: Record<string, number> = {
  // Formal wear
  BARONG_TAGALOG_JUSI: 250,
  BARONG_TAGALOG_PINA: 300,
  COAT: 250,
  LONG_PANTS: 170,
  SIMPLE_GOWN: 300,
  BEADED_GOWN: 400,
  WEDDING_GOWN: 1500,
  
  // Footwear
  SHOES: 350,
  
  // Casual wear
  BLOUSE: 150,
  SKIRT: 180,
  POLO: 150,
  SLACKS_TROUSERS: 170,
};

// Define item pricing for different categories
export const BEDDING_PRICES = {
  "Bedsheet (Single)": 80,
  "Bedsheet (Double)": 100,
  "Bedsheet (Queen)": 120,
  "Bedsheet (King)": 150,
  "Duvet Cover (Single)": 120,
  "Duvet Cover (Double)": 150,
  "Duvet Cover (Queen)": 180,
  "Duvet Cover (King)": 200,
  "Pillowcase": 25,
  "Blanket (Small)": 150,
  "Blanket (Medium)": 200,
  "Blanket (Large)": 250
};

export const TABLE_LINEN_PRICES = {
  "Tablecloth (Small)": 100,
  "Tablecloth (Medium)": 150,
  "Tablecloth (Large)": 200,
  "Table Runner": 70,
  "Placemats": 30,
  "Napkins": 15
};

export const BATHROOM_PRICES = {
  "Bath Towel": 50,
  "Hand Towel": 30,
  "Face Towel": 20,
  "Bath Mat": 60,
  "Shower Curtain": 120
};

export const OTHER_HOUSEHOLD_PRICES = {
  "Sofa Cover (Small)": 180,
  "Sofa Cover (Medium)": 250,
  "Sofa Cover (Large)": 350,
  "Cushion Cover": 40,
  "Mattress Protector (Single)": 120,
  "Mattress Protector (Double)": 150,
  "Mattress Protector (Queen)": 180,
  "Mattress Protector (King)": 220,
  "Curtains (per panel)": 100,
  "Rug (Small)": 150,
  "Rug (Medium)": 250,
  "Rug (Large)": 350
};

export const CLOTHING_PRICES = {
  "T-shirt": 35,
  "Polo Shirt": 45,
  "Dress Shirt": 50,
  "Blouse": 50,
  "Jeans": 60,
  "Pants/Trousers": 55,
  "Shorts": 40,
  "Skirt": 50,
  "Dress (Simple)": 70,
  "Dress (Formal)": 100,
  "Jacket (Light)": 80,
  "Jacket (Heavy)": 120,
  "Sweater": 70,
  "Hoodie": 65
};

export const UNIFORM_PRICES = {
  "School Uniform Set": 80,
  "Medical Scrubs Set": 70,
  "Office Uniform": 75,
  "Chef Uniform": 90,
  "Security Guard Uniform": 85
};

// Complete item price list combining all categories
export const ITEM_PRICE_LIST = {
  ...BEDDING_PRICES,
  ...TABLE_LINEN_PRICES,
  ...BATHROOM_PRICES,
  ...OTHER_HOUSEHOLD_PRICES,
  ...CLOTHING_PRICES,
  ...UNIFORM_PRICES
};

// Special services pricing (per kilo)
export const COMFORTERS_PRICE_PER_KILO = 150;
export const TOWELS_CURTAINS_LINENS_PRICE_PER_KILO = 120;

// Minimum weights
export const MINIMUM_WEIGHT_REGULAR = 3;
export const MINIMUM_WEIGHT_COMFORTERS = 3;
export const MINIMUM_WEIGHT_SPECIAL = 2;

// Default client item price if not specified
export const DEFAULT_CLIENT_ITEM_PRICE = 50;
