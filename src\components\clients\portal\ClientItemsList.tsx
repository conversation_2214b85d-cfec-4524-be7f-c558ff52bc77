
import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { formatCurrency } from '@/lib/formatters';
import { AlertCircle } from 'lucide-react';

interface ClientItem {
  id: string;
  name: string;
  unit_price: number;
  item_type: string;
}

interface ClientItemRequest {
  id: string;
  name: string;
  status: string;
  created_at: string;
  description?: string;
}

interface ClientItemsListProps {
  clientId: string;
}

export function ClientItemsList({ clientId }: ClientItemsListProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [items, setItems] = useState<ClientItem[]>([]);
  const [requests, setRequests] = useState<ClientItemRequest[]>([]);
  const [activeTab, setActiveTab] = useState('items');
  const { toast } = useToast();

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        if (!clientId) {
          throw new Error('Client ID is not provided');
        }

        console.log('Fetching items for client ID:', clientId);
        
        // Fetch client items
        const { data: clientItems, error: itemsError } = await supabase
          .from('client_items')
          .select('id, name, unit_price, item_type')
          .eq('client_id', clientId);
        
        if (itemsError) throw itemsError;
        console.log('Fetched client items:', clientItems);
        setItems(clientItems || []);

        // Fetch client item requests
        const { data: clientRequests, error: requestsError } = await supabase
          .from('client_item_requests')
          .select('id, name, status, created_at, description')
          .eq('client_id', clientId)
          .order('created_at', { ascending: false });
        
        if (requestsError) throw requestsError;
        console.log('Fetched client requests:', clientRequests);
        setRequests(clientRequests || []);
      } catch (error: any) {
        console.error('Error fetching client items data:', error);
        setError(error.message || 'Failed to load client items data');
        toast({
          title: 'Error',
          description: 'Failed to load client items data.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (clientId) {
      fetchData();
    }
  }, [clientId, toast]);

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-4 w-72" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-8 w-full mb-4" />
          {[1, 2, 3].map((i) => (
            <Skeleton key={i} className="h-16 w-full mb-2" />
          ))}
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-red-500">
            <AlertCircle className="h-5 w-5 mr-2" />
            Error Loading Items
          </CardTitle>
          <CardDescription className="text-red-400">
            {error}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-center py-8 text-gray-500">
            Please try refreshing the page or contact support.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Service Items</CardTitle>
        <CardDescription>
          Items available for your orders and requested items pending approval.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-2 mb-4">
            <TabsTrigger value="items">Available Items</TabsTrigger>
            <TabsTrigger value="requests">Requested Items</TabsTrigger>
          </TabsList>
          
          <TabsContent value="items">
            {items.length > 0 ? (
              <div className="border rounded-md overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Item Name</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead className="text-right">Price</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {items.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell className="font-medium">{item.name}</TableCell>
                        <TableCell>{item.item_type || 'Standard'}</TableCell>
                        <TableCell className="text-right">
                          {formatCurrency(item.unit_price)}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                No service items available.
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="requests">
            {requests.length > 0 ? (
              <div className="border rounded-md overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Item Name</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Requested On</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {requests.map((request) => (
                      <TableRow key={request.id}>
                        <TableCell className="font-medium">
                          {request.name}
                          {request.description && (
                            <p className="text-xs text-gray-500 mt-1">{request.description}</p>
                          )}
                        </TableCell>
                        <TableCell>
                          <span className={`px-2 py-1 rounded-full text-xs ${
                            request.status === 'approved' 
                              ? 'bg-green-100 text-green-800' 
                              : request.status === 'rejected'
                              ? 'bg-red-100 text-red-800'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}>
                            {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
                          </span>
                        </TableCell>
                        <TableCell>
                          {new Date(request.created_at).toLocaleDateString()}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                No item requests found.
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
