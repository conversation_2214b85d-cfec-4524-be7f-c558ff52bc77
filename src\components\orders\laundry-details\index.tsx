
import React from "react";
import { UseFormReturn } from "react-hook-form";
import { OrderFormValues } from "../OrderFormTypes";
import { DeliveryDateInput } from "./DeliveryDateInput";
import { WeightInput } from "./WeightInput";
import { PiecesInput } from "./PiecesInput";
import { ServiceSelection } from "./ServiceSelection";
import { DryCleaningItems } from "./DryCleaningItems";
import { SERVICE_TYPES } from "../pricing/constants";
import { PricingMethodToggle } from "./PricingMethodToggle";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";

interface LaundryDetailsProps {
  form: UseFormReturn<OrderFormValues>;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

export function LaundryDetails({ form, onChange }: LaundryDetailsProps) {
  const serviceType = form.watch("serviceType");
  const weightKilos = parseFloat(form.watch("weightKilos") || "0");
  const pricingMethod = form.watch("pricingMethod") as "weight" | "client_item" | "dry_cleaning";
  const orderType = form.watch("orderType");

  // Show minimum weight notice
  const showMinimumWeightNotice = () => {
    if (pricingMethod !== "weight") return null;

    if ((serviceType === SERVICE_TYPES.WASH_DRY_FOLD || 
         serviceType === SERVICE_TYPES.WASH_DRY_PRESS || 
         serviceType === SERVICE_TYPES.WASH_DRY_FOLD_SPECIAL) && 
         weightKilos < 3) {
      return (
        <Alert variant="default" className="mt-2">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Minimum weight for this service is 3kg. You will be charged for 3kg.
          </AlertDescription>
        </Alert>
      );
    }

    if ((serviceType === SERVICE_TYPES.COMFORTERS || 
         serviceType === SERVICE_TYPES.TOWELS_CURTAINS_LINENS) && 
         weightKilos < 2) {
      return (
        <Alert variant="default" className="mt-2">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Minimum weight for this service is 2kg. You will be charged for 2kg.
          </AlertDescription>
        </Alert>
      );
    }

    return null;
  };

  const isDryCleaningService = serviceType === SERVICE_TYPES.DRY_CLEANING;

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Laundry Details</h3>
      
      <div className="space-y-6">
        <DeliveryDateInput form={form} onChange={onChange} />
        
        {/* Only show service selection for walk-in orders */}
        {orderType === "walk-in" && (
          <ServiceSelection form={form} />
        )}
        
        {/* Weight input for weight-based pricing */}
        {pricingMethod === "weight" && !isDryCleaningService && (
          <>
            <WeightInput form={form} />
            {showMinimumWeightNotice()}
          </>
        )}
        
        {/* Pieces input for all orders (data collection and dry cleaning) */}
        <PiecesInput form={form} />
        
        {/* Only show pricing method toggle for walk-in orders, not client orders */}
        {orderType === "walk-in" && (
          <PricingMethodToggle form={form} />
        )}
        
        {/* Dry cleaning items selector (only for dry cleaning service) */}
        {isDryCleaningService && (
          <DryCleaningItems form={form} />
        )}
      </div>
    </div>
  );
}
