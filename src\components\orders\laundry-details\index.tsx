
import React from "react";
import { UseFormReturn } from "react-hook-form";
import { OrderFormValues } from "../OrderFormTypes";
import { DeliveryDateInput } from "./DeliveryDateInput";
import { WeightInput } from "./WeightInput";
import { PiecesInput } from "./PiecesInput";
import { ServiceSelection } from "./ServiceSelection";
import { DryCleaningItems } from "./DryCleaningItems";
import { SERVICE_TYPES } from "../pricing/constants";
import { PricingMethodToggle } from "./PricingMethodToggle";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { ServiceWeightsSection } from "./ServiceWeightsSection";

interface LaundryDetailsProps {
  form: UseFormReturn<OrderFormValues>;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

export function LaundryDetails({ form, onChange }: LaundryDetailsProps) {
  const serviceType = form.watch("serviceType");
  const pricingMethod = form.watch("pricingMethod") as "weight" | "client_item" | "dry_cleaning";
  const orderType = form.watch("orderType");
  const selectedServiceTypes = form.watch("selectedServiceTypes") || [SERVICE_TYPES.WASH_DRY_FOLD];
  const hasMultipleServices = selectedServiceTypes.length > 1;

  // Show minimum weight notice
  const showMinimumWeightNotice = () => {
    // We're now handling weight notices in the ServiceWeightInput component
    return null;
  };

  const isDryCleaningService = serviceType === SERVICE_TYPES.DRY_CLEANING;

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Laundry Details</h3>
      
      <div className="space-y-6">
        <DeliveryDateInput form={form} onChange={onChange} />
        
        {/* Only show service selection for walk-in orders */}
        {orderType === "walk-in" && (
          <ServiceSelection form={form} />
        )}
        
        {/* Weight input for weight-based pricing - only show for single service or legacy mode */}
        {pricingMethod === "weight" && !isDryCleaningService && !hasMultipleServices && (
          <>
            <WeightInput form={form} />
            {showMinimumWeightNotice()}
          </>
        )}
        
        {/* For multiple services, show weight inputs for each service */}
        {pricingMethod === "weight" && hasMultipleServices && (
          <ServiceWeightsSection form={form} />
        )}
        
        {/* Pieces input for all orders (data collection and dry cleaning) */}
        <PiecesInput form={form} />
        
        {/* Replace pricing method toggle with notes for walk-in orders */}
        <PricingMethodToggle form={form} />
        
        {/* Dry cleaning items selector (only for dry cleaning service) */}
        {isDryCleaningService && (
          <DryCleaningItems form={form} />
        )}
      </div>
    </div>
  );
}
