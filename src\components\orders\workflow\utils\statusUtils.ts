
/**
 * Utility functions for managing order statuses
 */

// Get status display name for UI
export const getStatusDisplayName = (status: string): string => {
  switch (status) {
    case 'ready_for_pickup': return 'Ready For Pickup';
    case 'for_pickup': return 'For Pickup';
    case 'pickup_complete': return 'Pickup Complete';
    case 'processing': return 'Order Processing';
    case 'for_treatment': return 'For Treatment';
    case 'hard_stain': return 'With Hard Stain';
    case 'partial_delivery': return 'Partial Delivery';
    case 'fulfilled': return 'Delivery Complete';
    case 'cancelled': return 'Cancelled';
    case 'pending': return 'Pending';
    default: return status.charAt(0).toUpperCase() + status.slice(1).replace(/_/g, ' ');
  }
};

// Get badge color based on status
export const getStatusBadgeStyle = (status: string): string => {
  switch (status) {
    case 'ready_for_pickup':
      return "bg-green-100 text-green-800 border-green-300";
    case 'for_pickup':
      return "bg-blue-100 text-blue-800 border-blue-300";
    case 'pickup_complete':
      return "bg-green-100 text-green-800 border-green-300";
    case 'processing':
      return "bg-indigo-100 text-indigo-800 border-indigo-300";
    case 'for_treatment':
      return "bg-orange-100 text-orange-800 border-orange-300";
    case 'hard_stain':
      return "bg-red-100 text-red-800 border-red-300";
    case 'partial_delivery':
      return "bg-amber-100 text-amber-800 border-amber-300";
    case 'fulfilled':
      return "bg-purple-100 text-purple-800 border-purple-300";
    case 'cancelled':
      return "bg-gray-100 text-gray-800 border-gray-300";
    default:
      return "bg-gray-100 text-gray-800 border-gray-300";
  }
};

// Define available status transitions based on order type and current status
export const getNextStatuses = (status: string, isClientOrder: boolean): string[] => {
  // Common statuses for both client and walk-in orders
  const commonStatuses = ['processing', 'fulfilled', 'cancelled'];
  
  // Client order statuses
  if (isClientOrder) {
    return [
      ...commonStatuses,
      'ready_for_pickup',
      'for_pickup',
      'pickup_complete',
      'for_treatment',
      'hard_stain',
      'partial_delivery'
    ];
  } 
  // Walk-in order statuses
  else {
    return [
      ...commonStatuses, 
      'for_treatment',
      'hard_stain'
    ];
  }
};
