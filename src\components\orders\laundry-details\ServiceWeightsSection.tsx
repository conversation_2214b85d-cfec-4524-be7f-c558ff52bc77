
import React from "react";
import { UseFormReturn } from "react-hook-form";
import { OrderFormValues, ServiceWeight } from "../OrderFormTypes";
import { Card, CardContent } from "@/components/ui/card";
import { ServiceWeightInput } from "./ServiceWeightInput";
import { SERVICE_TYPES } from "../pricing/constants";

interface ServiceWeightsSectionProps {
  form: UseFormReturn<OrderFormValues>;
}

export function ServiceWeightsSection({ form }: ServiceWeightsSectionProps) {
  const selectedServiceTypes = form.watch("selectedServiceTypes") || [SERVICE_TYPES.WASH_DRY_FOLD];
  
  // Filter out dry cleaning - it doesn't need weight input
  const weightBasedServices = selectedServiceTypes.filter(
    type => type !== SERVICE_TYPES.DRY_CLEANING
  );
  
  if (weightBasedServices.length === 0) {
    return null;
  }

  return (
    <Card className="border-border">
      <CardContent className="pt-4">
        <div className="space-y-2">
          <h3 className="font-medium">Service Weights</h3>
          <p className="text-sm text-muted-foreground">Enter weight for each service</p>
          
          <div className="space-y-2 mt-3">
            {weightBasedServices.map((serviceType) => (
              <ServiceWeightInput 
                key={serviceType}
                form={form}
                serviceType={serviceType}
              />
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
