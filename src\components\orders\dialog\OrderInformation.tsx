
import { Order } from "@/types";
import { Badge } from "@/components/ui/badge";

interface OrderInformationProps {
  order: Order;
}

export function OrderInformation({ order }: OrderInformationProps) {
  const isClientOrder = order.customerType === 'client';
  
  return (
    <div className="space-y-3">
      <h3 className="text-lg font-semibold">Order Information</h3>
      <div className="grid grid-cols-2 gap-3">
        <div>
          <p className="text-sm font-medium text-muted-foreground">Order Date</p>
          <p>{order.orderDate}</p>
        </div>
        <div>
          <p className="text-sm font-medium text-muted-foreground">Delivery Date</p>
          <p>{order.deliveryDate}</p>
        </div>
        {order.customerType && (
          <div>
            <p className="text-sm font-medium text-muted-foreground">Customer Type</p>
            <Badge variant="outline" className="capitalize">{order.customerType}</Badge>
          </div>
        )}
        {/* Only show weight for walk-in orders */}
        {!isClientOrder && order.weightKilos && (
          <div>
            <p className="text-sm font-medium text-muted-foreground">Weight</p>
            <p>{order.weightKilos} kg</p>
          </div>
        )}
        {order.numberOfPieces && (
          <div>
            <p className="text-sm font-medium text-muted-foreground">Pieces</p>
            <p>{order.numberOfPieces}</p>
          </div>
        )}
      </div>
    </div>
  );
}
