# Google Play Store Deployment Guide

This guide explains how to deploy the CMC Laundry POS app to the Google Play Store.

## Prerequisites

Before you begin, make sure you have:

1. A Google Play Developer account ($25 one-time registration fee)
2. A properly signed AAB (Android App Bundle) file
3. App graphics and promotional materials
4. Privacy policy URL

## Step 1: Create a Production Keystore

For security reasons, you should create a proper keystore for signing your app. The current keystore is just a placeholder.

```bash
# Install JDK if you don't have it already
# Then run:
keytool -genkey -v -keystore ./keystore/cmc-laundry-pos.keystore -alias cmc-laundry-pos -keyalg RSA -keysize 2048 -validity 9125
```

When prompted:
- Enter a secure password (remember this!)
- Provide information about your organization
- Use the same password for the key when prompted

**IMPORTANT**: Keep this keystore file secure! If you lose it, you won't be able to update your app on Google Play.

## Step 2: Update Capacitor Config

Update the `capacitor.config.ts` file with your keystore information:

```typescript
android: {
  buildOptions: {
    keystorePath: "./keystore/cmc-laundry-pos.keystore",
    keystorePassword: "your-secure-password",
    keystoreAlias: "cmc-laundry-pos",
    keystoreAliasPassword: "your-secure-password",
    releaseType: 'AAB'
  }
}
```

## Step 3: Build the Release AAB

Run the following command to build the release AAB:

```bash
npm run android:release
```

This will create a file called `cmc-laundry-pos.aab` in the project root directory.

## Step 4: Create a Google Play Developer Account

1. Go to [Google Play Console](https://play.google.com/console/signup)
2. Sign in with your Google account
3. Pay the $25 one-time registration fee
4. Complete the account details

## Step 5: Create a New App

1. In the Google Play Console, click "Create app"
2. Enter the app details:
   - App name: CMC Laundry POS
   - Default language: English (or your preferred language)
   - App or game: App
   - Free or paid: Free (or Paid if applicable)
   - Declare app meets Play guidelines

## Step 6: Complete the Store Listing

Prepare and upload the following:

1. **App description**:
   - Short description (80 characters max)
   - Full description (4000 characters max)

2. **Graphics**:
   - App icon (512x512 PNG)
   - Feature graphic (1024x500 PNG)
   - At least 2 screenshots for each device type (phone, tablet)

3. **Categorization**:
   - Category: Business
   - Tags: POS, Laundry, Business Management

4. **Contact details**:
   - Email address
   - Website
   - Phone number (optional)

5. **Privacy policy**:
   - URL to your privacy policy (required)

## Step 7: Set Up Content Rating

1. Complete the content rating questionnaire
2. Submit for rating

## Step 8: Set Up Pricing & Distribution

1. Choose countries where your app will be available
2. Decide if your app is free or paid
3. Select "Contains ads" if applicable
4. Confirm compliance with US export laws
5. Confirm app content guidelines compliance

## Step 9: Release Management

1. Go to "Production" track
2. Click "Create new release"
3. Upload your AAB file (`cmc-laundry-pos.aab`)
4. Add release notes
5. Review and submit for approval

## Step 10: App Review Process

1. Wait for Google to review your app (typically 1-3 days)
2. Address any issues if the app is rejected
3. Once approved, your app will be published to the Google Play Store

## Important Notes

1. **Keep your keystore safe**: If you lose it, you won't be able to update your app
2. **Version management**: Increment `versionCode` and `versionName` in `android/app/build.gradle` for each update
3. **Testing**: Consider using internal testing or closed testing tracks before full production release
4. **App Bundle benefits**: Google Play will optimize APK delivery based on user device
5. **Play Console features**: Explore crash reports, user feedback, and performance metrics

## Updating Your App

When you need to update your app:

1. Increment the version code and version name in `android/app/build.gradle`
2. Make your code changes
3. Build a new AAB with `npm run android:release`
4. Create a new release in the Google Play Console
5. Upload the new AAB and add release notes
6. Submit for review

## Resources

- [Google Play Console Help](https://support.google.com/googleplay/android-developer/)
- [Play Console](https://play.google.com/console/)
- [App signing by Google Play](https://developer.android.com/studio/publish/app-signing#app-signing-google-play)
- [Android App Bundle documentation](https://developer.android.com/guide/app-bundle)
