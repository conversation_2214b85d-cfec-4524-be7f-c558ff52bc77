
import { Order } from "@/types";
import { useToast } from "@/hooks/use-toast";

export function useBrowserPrinting() {
  const { toast } = useToast();

  const printInBrowser = (order: Order, type: 'receipt' | 'jobOrder') => {
    try {
      // Get add-on quantities from order data
      const detergentQty = order.detergentQuantity || 1;
      const conditionerQty = order.conditionerQuantity || 1;
      const isClientOrder = order.customerType === 'client';
      const hasDryCleaningItems = order.dryCleaningItems && order.dryCleaningItems.length > 0;
      
      // Format item type for display
      const formatItemType = (type: string) => {
        return type
          .replace(/_/g, ' ')
          .replace(/\w\S*/g, txt => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase());
      };
      
      // Format the order data for printing
      let printContent = '';
      let title = '';
      
      if (type === 'receipt') {
        title = 'Customer Receipt';
        printContent = `
CMC LAUNDRY
-----------
ORDER SLIP
----------
Order ID: ${order.id}
Date: ${order.orderDate}
Customer: ${order.customer.name}
Phone: ${order.customer.phone}

Items:`;

        // Add regular line items with proper treatment info
        if (order.lineItems && order.lineItems.length > 0) {
          printContent += '\n';
          order.lineItems.forEach(item => {
            const treatment = item.treatmentDescription ? ` (${item.treatmentDescription})` : '';
            printContent += `${item.name}${treatment} x${item.quantity} - ₱${item.total ? item.total.toFixed(2) : (item.unitPrice * item.quantity).toFixed(2)}\n`;
          });
        } else {
          printContent += '\nNo items';
        }

        // Add dry cleaning items if present
        if (hasDryCleaningItems) {
          printContent += `\nDry Cleaning Items:\n`;
          order.dryCleaningItems.forEach(item => {
            const itemTotal = (item.price || 0) * (item.quantity || 1);
            printContent += `${formatItemType(item.type)} x${item.quantity || 1} - ₱${itemTotal.toFixed(2)}\n`;
          });
        }

        printContent += `
${!isClientOrder && order.weightKilos ? `Weight: ${order.weightKilos} kg\n` : ''}
Pieces: ${order.numberOfPieces || 0}

Add-ons:
${order.useDetergent ? `- Detergent (${order.detergentType || 'Standard'}) x${detergentQty}` : ''}
${order.useFabricConditioner ? `- Fabric Conditioner (${order.conditionerType || 'Standard'}) x${conditionerQty}` : ''}
${order.useStainRemover ? '- Stain Remover' : ''}
${order.useBleach ? '- Bleach Treatment' : ''}

Subtotal: ₱${order.subtotalBeforeVAT?.toFixed(2) || '0.00'}
VAT: ₱${order.vatAmount?.toFixed(2) || '0.00'}
Total Amount: ₱${order.amount.toFixed(2)}
Paid Amount: ₱${order.paidAmount.toFixed(2)}
Balance: ₱${(order.amount - order.paidAmount).toFixed(2)}

Status: ${order.status.toUpperCase()}`;
        
      } else {
        title = 'Job Order';
        printContent = `
CMC LAUNDRY
-----------
JOB ORDER
----------
Order ID: ${order.id}
Date: ${order.orderDate}
Service: ${order.serviceType?.toUpperCase() || "REGULAR SERVICE"}
Customer: ${order.customer.name}
Phone: ${order.customer.phone}

ITEMS TO PROCESS:`;

        // Add regular line items with treatment info
        if (order.lineItems && order.lineItems.length > 0) {
          printContent += '\n';
          order.lineItems.forEach(item => {
            const treatment = item.treatmentDescription ? ` (${item.treatmentDescription})` : '';
            printContent += `- ${item.name}${treatment} x${item.quantity}\n`;
          });
        } else {
          printContent += '\nNo specific items';
        }

        // Add dry cleaning items if present
        if (hasDryCleaningItems) {
          printContent += `\nDRY CLEANING ITEMS:\n`;
          order.dryCleaningItems.forEach(item => {
            printContent += `- ${formatItemType(item.type)} x${item.quantity || 1}\n`;
          });
        }

        printContent += `
SPECIFICATIONS:
${!isClientOrder && order.weightKilos ? `Weight: ${order.weightKilos} kg\n` : ''}
Pieces: ${order.numberOfPieces || 0}

PROCESSING INSTRUCTIONS:
${order.useDetergent ? `☐ Use Detergent: ${order.detergentType || "Standard"}\n  Quantity: ${detergentQty}` : ''}
${order.useFabricConditioner ? `☐ Use Fabric Conditioner: ${order.conditionerType || "Standard"}\n  Quantity: ${conditionerQty}` : ''}
${order.useStainRemover ? '☐ Apply Stain Remover Treatment' : ''}
${order.useBleach ? '☐ Apply Bleach Treatment' : ''}

${order.notes ? `SPECIAL NOTES:\n${order.notes}` : ''}

STAFF PROCESSING CHECKLIST:
☐ Sorting Complete
☐ Pre-treatment Applied
☐ Washing Complete
☐ Drying Complete
☐ Folding Complete
☐ Quality Check
☐ Ready for Pickup

Assigned Staff: ________________

Status: ${order.status.toUpperCase()}`;
        
      }

      // Send to browser printer
      const printWindow = window.open('', '', 'width=600,height=600');
      if (!printWindow) {
        throw new Error('Could not open print window');
      }

      printWindow.document.open();
      printWindow.document.write(`
        <html>
          <head>
            <title>${title} - ${order.id}</title>
            <style>
              body {
                font-family: monospace;
                font-size: 12px;
                white-space: pre;
                margin: 0;
                padding: 20px;
              }
              @media print {
                body { margin: 0; }
              }
            </style>
          </head>
          <body>${printContent}</body>
        </html>
      `);
      printWindow.document.close();
      printWindow.print();
      printWindow.close();

      toast({
        title: "Print Initiated",
        description: `${title} for ${order.id} has been sent to printer`,
      });
      
      return true;
    } catch (error) {
      console.error('Print error:', error);
      toast({
        title: "Print Failed",
        description: "Could not print the document. Please try again.",
        variant: "destructive",
      });
      
      return false;
    }
  };

  return { printInBrowser };
}
