
import { Order, DryCleaningItem } from "@/types";
import { SERVICE_TYPES } from "../../pricing/constants";
import { DryCleaningIndicator } from "../add-ons/DryCleaningIndicator";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

interface DryCleaningDetailsProps {
  order: Order;
}

export function DryCleaningDetails({
  order
}: DryCleaningDetailsProps) {
  // Check if this is a dry cleaning service by either isDryCleaning flag OR serviceType
  const isDryCleaningService = order?.serviceType === SERVICE_TYPES.DRY_CLEANING || order?.isDryCleaning;
  
  if (!isDryCleaningService) return null;

  // Format currency
  const formatCurrency = (amount: number) => {
    return `₱${amount.toFixed(2)}`;
  };

  // Format item type for display
  const formatItemType = (type: string) => {
    return type.replace(/_/g, ' ').replace(/\w\S*/g, txt => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase());
  };

  // Process dryCleaningItems to ensure they have the correct format
  const processedDryCleaningItems: DryCleaningItem[] = order.dryCleaningItems ? 
    order.dryCleaningItems.map(item => ({
      id: item.id || '',
      type: item.type || item.name || 'unknown',
      price: typeof item.price === 'number' ? item.price : parseFloat(String(item.price || 0)),
      quantity: typeof item.quantity === 'number' ? item.quantity : parseFloat(String(item.quantity || 1)),
      total: typeof item.total === 'number' ? item.total : parseFloat(String(item.price || 0)) * parseFloat(String(item.quantity || 1)),
      name: item.name || formatItemType(item.type || 'unknown')
    })) : [];

  // Count total dry cleaning items
  const totalDryCleaningItems = processedDryCleaningItems.reduce((sum, item) => sum + (item.quantity || 0), 0);
  
  return (
    <div className="space-y-4">
      <DryCleaningIndicator 
        isDryCleaning={true} 
        serviceType={order.serviceType} 
        dryCleaningItems={processedDryCleaningItems} 
      />
      
      {/* Display dry cleaning items table if they exist */}
      {processedDryCleaningItems.length > 0 && (
        <div className="border rounded-md overflow-hidden">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Item Type</TableHead>
                <TableHead>Quantity</TableHead>
                <TableHead>Price</TableHead>
                <TableHead className="text-right">Total</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {processedDryCleaningItems.map((item, index) => (
                <TableRow key={item.id || `dry-clean-${index}`}>
                  <TableCell>{item.name || formatItemType(item.type)}</TableCell>
                  <TableCell>{item.quantity}</TableCell>
                  <TableCell>{formatCurrency(item.price)}</TableCell>
                  <TableCell className="text-right">{formatCurrency(item.total || (item.price * item.quantity))}</TableCell>
                </TableRow>
              ))}
              <TableRow className="bg-muted/50">
                <TableCell colSpan={3} className="font-medium">Total Items</TableCell>
                <TableCell className="text-right font-medium">{totalDryCleaningItems}</TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  );
}
