
import { useState } from "react";
import { Order } from "@/types";
import { parseServiceItems } from "./utils";
import { Edit } from "lucide-react";
import { Button } from "@/components/ui/button";
import { EditAddOnsDialog } from "../../edit/EditAddOnsDialog";
import { useAuth } from "@/contexts/auth";

interface ClientServiceDetailsProps {
  order: Order;
  onOrderUpdated?: () => void;
}

export function ClientServiceDetails({ order, onOrderUpdated }: ClientServiceDetailsProps) {
  const [isAddOnsDialogOpen, setIsAddOnsDialogOpen] = useState(false);
  const { userRole } = useAuth();
  
  const serviceItems = parseServiceItems(order);
  const canEdit = userRole === 'admin' || userRole === 'staff';

  // Generate a default service item if none exists
  const hasServiceItems = serviceItems && serviceItems.length > 0;
  
  // Handle add-ons updated callback
  const handleAddOnsUpdated = () => {
    if (onOrderUpdated) {
      onOrderUpdated();
    }
  };

  console.log("ClientServiceDetails rendering:", order.id);
  console.log("Service items found:", serviceItems);

  return (
    <div className="space-y-2">
      <div className="flex justify-between items-center">
        <h4 className="text-sm font-medium">Client Service Details</h4>
        
        {canEdit && (
          <Button 
            variant="outline" 
            size="sm"
            className="h-8 text-xs"
            onClick={() => setIsAddOnsDialogOpen(true)}
          >
            <Edit className="h-3.5 w-3.5 mr-1" />
            Edit Add-ons
          </Button>
        )}
      </div>
      
      <div className="text-sm border rounded-md p-3 bg-purple-50">
        <div className="flex justify-between py-1">
          <span className="font-medium">CLIENT SERVICE</span>
          <span>₱{order.subtotalBeforeVAT?.toFixed(2) || order.amount.toFixed(2)}</span>
        </div>
        
        {/* Show weight/pieces information */}
        <div className="mt-2 text-sm text-muted-foreground">
          {order.weightKilos ? `Weight: ${order.weightKilos} kg` : ''}
          {order.weightKilos && order.numberOfPieces ? ' · ' : ''}
          {order.numberOfPieces ? `Pieces: ${order.numberOfPieces}` : ''}
        </div>
        
        {/* Display service items with improved handling */}
        <div className="mt-3 border-t pt-2">
          <p className="font-medium mb-1">Service Items:</p>
          {hasServiceItems ? (
            <ul className="space-y-1 pl-2">
              {serviceItems.map((item, idx) => (
                <li key={idx} className="text-sm flex justify-between">
                  <span>
                    {item.name} 
                    {item.quantity > 1 ? ` × ${item.quantity}` : ''}
                    {item.treatmentDescription && (
                      <span className="text-xs text-muted-foreground ml-1">
                        ({item.treatmentDescription})
                      </span>
                    )}
                  </span>
                  <span className="text-sm ml-4">
                    {item.unitPrice ? `₱${(item.unitPrice * (item.quantity || 1)).toFixed(2)}` : ''}
                  </span>
                </li>
              ))}
            </ul>
          ) : (
            <div className="text-sm text-muted-foreground">
              <p>{order.serviceType?.replace(/_/g, ' ').toUpperCase() || 'Laundry Service'}</p>
            </div>
          )}
        </div>
        
        {/* Show add-ons section if any are enabled */}
        {(order.useDetergent || order.useFabricConditioner || order.useStainRemover || order.useBleach) && (
          <div className="mt-3 border-t pt-2">
            <p className="font-medium mb-1">Add-ons:</p>
            <ul className="text-sm text-muted-foreground pl-2">
              {order.useDetergent && (
                <li>Detergent {order.detergentQuantity ? `× ${order.detergentQuantity}` : ''}</li>
              )}
              {order.useFabricConditioner && (
                <li>Fabric Conditioner {order.conditionerQuantity ? `× ${order.conditionerQuantity}` : ''}</li>
              )}
              {order.useStainRemover && <li>Stain Removal Treatment</li>}
              {order.useBleach && <li>Bleach Treatment</li>}
            </ul>
          </div>
        )}
        
        {/* Show VAT information if present */}
        {order.vatAmount > 0 && (
          <div className="flex justify-between pt-2 mt-2 border-t">
            <span>VAT ({(order.vatAmount / (order.subtotalBeforeVAT || 1) * 100).toFixed(0)}%):</span>
            <span>₱{order.vatAmount.toFixed(2)}</span>
          </div>
        )}
        
        {/* Show total */}
        <div className="flex justify-between pt-2 mt-2 border-t font-medium">
          <span>Total:</span>
          <span>₱{order.amount.toFixed(2)}</span>
        </div>
      </div>
      
      {/* Add-ons Dialog */}
      {order && (
        <EditAddOnsDialog
          order={order}
          open={isAddOnsDialogOpen}
          onOpenChange={setIsAddOnsDialogOpen}
          onAddOnsUpdated={handleAddOnsUpdated}
        />
      )}
    </div>
  );
}
