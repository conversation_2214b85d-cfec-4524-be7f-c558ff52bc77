import type { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'com.philvirtualoffice.pvosyncpos',
  appName: 'CMC Laundry POS',
  webDir: 'dist',
  bundledWebRuntime: false,
  server: {
    androidScheme: 'https'
  },
  android: {
    buildOptions: {
      keystorePath: ".\keystore\cmc-laundry-pos.keystore",
      keystorePassword: "your-secure-password",
      keystoreAlias: "cmc-laundry-pos",
      keystoreAliasPassword: "your-secure-password",
      releaseType: 'AAB'
    }
  }
};

export default config;

