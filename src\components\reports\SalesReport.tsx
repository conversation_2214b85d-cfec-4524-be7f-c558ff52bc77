
import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Calendar, Download, FileText, Printer } from 'lucide-react';
import { DateRange } from 'react-day-picker';
import { format, subMonths } from 'date-fns';
import { DateRangeFilter } from '@/components/accounting/dashboard/DateRangeFilter';
import { useReactToPrint } from '@/hooks/use-print';
import { useRef } from 'react';
import { toast } from 'sonner';
import { Bar, BarChart, CartesianGrid, Legend, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { ClientSelect } from '@/components/orders/filters/components/ClientSelect';
import { supabase } from '@/integrations/supabase/client';
import { Skeleton } from '@/components/ui/skeleton';
import { useClientsData } from '@/hooks/useClientsData';

export function SalesReport() {
  const printRef = useRef<HTMLDivElement>(null);
  const [isPrinting, setIsPrinting] = useState(false);
  const [dateRange, setDateRange] = useState<DateRange>({
    from: subMonths(new Date(), 1),
    to: new Date(),
  });
  const [clientFilter, setClientFilter] = useState<string>("all");
  const [orderTypeFilter, setOrderTypeFilter] = useState<string>("all");
  const [isLoading, setIsLoading] = useState(true);
  const [salesData, setSalesData] = useState<any[]>([]);
  const [summaryData, setSummaryData] = useState({
    totalSales: 0,
    totalOrders: 0,
    averageSale: 0,
  });
  const [ordersDetails, setOrdersDetails] = useState<any[]>([]);
  const { clients, loading: loadingClients } = useClientsData();
  
  // Use our custom hook for printing
  const handlePrint = useReactToPrint({
    content: () => printRef.current,
    documentTitle: `Sales Report - ${format(new Date(), 'yyyy-MM-dd')}`,
    onBeforePrint: () => setIsPrinting(true),
    onAfterPrint: () => {
      setIsPrinting(false);
      toast.success("Report printed successfully");
    },
    onPrintError: () => {
      setIsPrinting(false);
      toast.error("Printing failed. Please try again");
    }
  });

  // Function to format date for Supabase query
  const formatDateForQuery = (date: Date): string => {
    return date.toISOString().split('T')[0];
  };
  
  // Fetch orders data
  useEffect(() => {
    const fetchSalesData = async () => {
      setIsLoading(true);
      try {
        let query = supabase
          .from('orders')
          .select('id, created_at, status, amount, paid_amount, client_id, items, reference_code, delivery_date')
          .gte('created_at', dateRange.from ? formatDateForQuery(dateRange.from) : undefined)
          .lte('created_at', dateRange.to ? formatDateForQuery(dateRange.to) : undefined);
        
        // Apply client filter if not "all"
        if (clientFilter && clientFilter !== 'all') {
          query = query.eq('client_id', clientFilter);
        }
        
        // Apply order type filter
        if (orderTypeFilter === 'client') {
          query = query.eq('customer_type', 'client');
        } else if (orderTypeFilter === 'walk-in') {
          query = query.eq('customer_type', 'walk_in');
        }
        
        const { data, error } = await query.order('created_at', { ascending: false });
        
        if (error) {
          throw error;
        }
        
        // Process orders data
        if (data) {
          setOrdersDetails(data.map(order => ({
            id: order.reference_code || order.id,
            date: format(new Date(order.created_at), 'MMM dd, yyyy'),
            deliveryDate: order.delivery_date ? format(new Date(order.delivery_date), 'MMM dd, yyyy') : 'N/A',
            status: order.status,
            amount: order.amount,
            paidAmount: order.paid_amount,
            balance: order.amount - order.paid_amount,
            clientId: order.client_id,
            clientName: clients.find(c => c.id === order.client_id)?.name || 'Unknown',
            items: Array.isArray(order.items) ? order.items : []
          })));
          
          // Group data by week for the chart
          const groupedData = groupDataByWeek(data);
          setSalesData(groupedData);
          
          // Calculate summary data
          const totalSales = data.reduce((sum, order) => sum + order.amount, 0);
          const totalOrders = data.length;
          
          setSummaryData({
            totalSales,
            totalOrders,
            averageSale: totalOrders > 0 ? totalSales / totalOrders : 0
          });
        }
      } catch (error) {
        console.error('Error fetching sales data:', error);
        toast.error('Failed to load sales data');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchSalesData();
  }, [dateRange, clientFilter, orderTypeFilter, clients]);
  
  // Function to group data by week
  const groupDataByWeek = (orders: any[]) => {
    const weeks: { [key: string]: { name: string; sales: number; orders: number } } = {};
    
    orders.forEach(order => {
      const date = new Date(order.created_at);
      // Get week number and year
      const weekNum = getWeekNumber(date);
      const year = date.getFullYear();
      const weekKey = `${year}-W${weekNum}`;
      
      if (!weeks[weekKey]) {
        weeks[weekKey] = {
          name: `Week ${weekNum}`,
          sales: 0,
          orders: 0
        };
      }
      
      weeks[weekKey].sales += order.amount;
      weeks[weekKey].orders += 1;
    });
    
    return Object.values(weeks);
  };
  
  // Function to get week number
  const getWeekNumber = (date: Date) => {
    const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
    const pastDaysOfYear = (date.getTime() - firstDayOfYear.getTime()) / 86400000;
    return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
  };
  
  // Date range presets
  const datePresets = [
    {
      label: 'This Month',
      getValue: () => ({
        from: new Date(new Date().setDate(1)),
        to: new Date(),
      }),
    },
    {
      label: 'Last Month',
      getValue: () => ({
        from: subMonths(new Date(), 1),
        to: new Date(),
      }),
    },
    {
      label: 'Last 3 Months',
      getValue: () => ({
        from: subMonths(new Date(), 3),
        to: new Date(),
      }),
    }
  ];

  const handleExportCSV = () => {
    // Create CSV content
    let csvContent = 'data:text/csv;charset=utf-8,';
    
    // Add headers
    csvContent += 'Order ID,Date,Client,Status,Amount,Paid Amount,Balance\n';
    
    // Add data rows
    ordersDetails.forEach(order => {
      csvContent += `${order.id},${order.date},${order.clientName},${order.status},${order.amount},${order.paidAmount},${order.balance}\n`;
    });
    
    // Create download link
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement('a');
    link.setAttribute('href', encodedUri);
    link.setAttribute('download', `Sales_Report_${format(new Date(), 'yyyy-MM-dd')}.csv`);
    document.body.appendChild(link);
    
    // Trigger download
    link.click();
    document.body.removeChild(link);
    
    toast.success('CSV file downloaded successfully');
  };

  return (
    <div className="space-y-6" ref={printRef}>
      <Card className="print:hidden">
        <CardHeader className="pb-3">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
              <CardTitle>Sales Report</CardTitle>
              <CardDescription>Overview of sales performance</CardDescription>
            </div>
            <div className="flex flex-col md:flex-row items-start md:items-center gap-4">
              <DateRangeFilter 
                dateRange={dateRange}
                onDateRangeChange={setDateRange}
                presets={datePresets}
              />
              <div className="flex gap-2">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={handlePrint}
                  disabled={isPrinting}
                >
                  <Printer className="mr-1 h-4 w-4" />
                  Print
                </Button>
                <Button variant="outline" size="sm" onClick={() => handleExportCSV()}>
                  <Download className="mr-1 h-4 w-4" />
                  CSV
                </Button>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>Client</Label>
                {loadingClients ? (
                  <Skeleton className="h-10 w-full" />
                ) : (
                  <ClientSelect 
                    value={clientFilter}
                    onChange={setClientFilter}
                    clients={clients.map(client => ({ id: client.id, name: client.name }))}
                  />
                )}
              </div>
              <div className="space-y-2">
                <Label>Order Type</Label>
                <Select 
                  value={orderTypeFilter}
                  onValueChange={setOrderTypeFilter}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by order type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Orders</SelectItem>
                    <SelectItem value="client">Client Orders</SelectItem>
                    <SelectItem value="walk-in">Walk-in Orders</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* Summary Stats */}
      <div className="grid md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Total Sales</CardDescription>
            {isLoading ? (
              <Skeleton className="h-9 w-36" />
            ) : (
              <CardTitle className="text-3xl font-bold">
                ₱{summaryData.totalSales.toLocaleString()}
              </CardTitle>
            )}
          </CardHeader>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Total Orders</CardDescription>
            {isLoading ? (
              <Skeleton className="h-9 w-24" />
            ) : (
              <CardTitle className="text-3xl font-bold">
                {summaryData.totalOrders}
              </CardTitle>
            )}
          </CardHeader>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Average Order Value</CardDescription>
            {isLoading ? (
              <Skeleton className="h-9 w-32" />
            ) : (
              <CardTitle className="text-3xl font-bold">
                ₱{summaryData.averageSale.toLocaleString(undefined, {maximumFractionDigits: 2})}
              </CardTitle>
            )}
          </CardHeader>
        </Card>
      </div>
      
      {/* Sales Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Sales Trend</CardTitle>
          <CardDescription>
            {dateRange.from && dateRange.to ? (
              <>
                From {format(dateRange.from, 'MMMM d, yyyy')} to {format(dateRange.to, 'MMMM d, yyyy')}
              </>
            ) : (
              'Select a date range to filter data'
            )}
            {clientFilter !== 'all' && clients.find(c => c.id === clientFilter) && (
              <> • {clients.find(c => c.id === clientFilter)?.name}</>
            )}
            {orderTypeFilter !== 'all' && (
              <> • {orderTypeFilter === 'client' ? 'Client' : 'Walk-in'} orders</>
            )}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="h-80">
              <Skeleton className="h-full w-full" />
            </div>
          ) : salesData.length > 0 ? (
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={salesData}
                  margin={{
                    top: 20,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip 
                    formatter={(value) => [`₱${Number(value).toLocaleString()}`, 'Sales']}
                    labelFormatter={(label) => `Period: ${label}`}
                  />
                  <Legend />
                  <Bar dataKey="sales" name="Sales (₱)" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          ) : (
            <div className="flex items-center justify-center h-80 border rounded text-muted-foreground">
              No data available for the selected filters
            </div>
          )}
        </CardContent>
      </Card>
      
      {/* Orders Data Table */}
      <Card>
        <CardHeader>
          <CardTitle>Order Details</CardTitle>
          <CardDescription>
            {clientFilter !== 'all' && clients.find(c => c.id === clientFilter) && (
              <>Orders for {clients.find(c => c.id === clientFilter)?.name}</>
            )}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="space-y-4">
              {Array.from({ length: 5 }).map((_, i) => (
                <Skeleton key={i} className="h-12 w-full" />
              ))}
            </div>
          ) : ordersDetails.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4">Order ID</th>
                    <th className="text-left py-3 px-4">Date</th>
                    <th className="text-left py-3 px-4">Client</th>
                    <th className="text-left py-3 px-4">Status</th>
                    <th className="text-right py-3 px-4">Amount</th>
                    <th className="text-right py-3 px-4">Paid</th>
                    <th className="text-right py-3 px-4">Balance</th>
                  </tr>
                </thead>
                <tbody>
                  {ordersDetails.map((order, index) => (
                    <tr key={index} className="border-b hover:bg-slate-50">
                      <td className="py-3 px-4">{order.id}</td>
                      <td className="py-3 px-4">{order.date}</td>
                      <td className="py-3 px-4">{order.clientName}</td>
                      <td className="py-3 px-4 capitalize">{order.status}</td>
                      <td className="py-3 px-4 text-right">₱{order.amount.toLocaleString()}</td>
                      <td className="py-3 px-4 text-right">₱{order.paidAmount.toLocaleString()}</td>
                      <td className="py-3 px-4 text-right">₱{order.balance.toLocaleString()}</td>
                    </tr>
                  ))}
                </tbody>
                <tfoot>
                  <tr className="font-semibold bg-slate-50">
                    <td className="py-3 px-4" colSpan={4}>Total</td>
                    <td className="py-3 px-4 text-right">
                      ₱{ordersDetails.reduce((sum, order) => sum + order.amount, 0).toLocaleString()}
                    </td>
                    <td className="py-3 px-4 text-right">
                      ₱{ordersDetails.reduce((sum, order) => sum + order.paidAmount, 0).toLocaleString()}
                    </td>
                    <td className="py-3 px-4 text-right">
                      ₱{ordersDetails.reduce((sum, order) => sum + order.balance, 0).toLocaleString()}
                    </td>
                  </tr>
                </tfoot>
              </table>
            </div>
          ) : (
            <div className="text-center py-8 border rounded">
              <p className="text-muted-foreground">No orders found for the selected filters.</p>
            </div>
          )}
        </CardContent>
      </Card>
      
      {/* Detailed items breakdown for selected client */}
      {clientFilter !== 'all' && ordersDetails.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Order Items Breakdown</CardTitle>
            <CardDescription>
              Detailed breakdown of items ordered by {clients.find(c => c.id === clientFilter)?.name || 'selected client'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4">Order ID</th>
                    <th className="text-left py-3 px-4">Date</th>
                    <th className="text-left py-3 px-4">Item</th>
                    <th className="text-right py-3 px-4">Quantity</th>
                    <th className="text-right py-3 px-4">Unit Price</th>
                    <th className="text-right py-3 px-4">Total</th>
                  </tr>
                </thead>
                <tbody>
                  {ordersDetails.flatMap((order, orderIdx) => 
                    order.items && Array.isArray(order.items) ? 
                      order.items.map((item: any, itemIdx: number) => (
                        <tr key={`${orderIdx}-${itemIdx}`} className="border-b hover:bg-slate-50">
                          <td className="py-3 px-4">{order.id}</td>
                          <td className="py-3 px-4">{order.date}</td>
                          <td className="py-3 px-4">{item.name}</td>
                          <td className="py-3 px-4 text-right">{item.quantity}</td>
                          <td className="py-3 px-4 text-right">₱{Number(item.unitPrice || 0).toLocaleString()}</td>
                          <td className="py-3 px-4 text-right">₱{Number(item.total || (item.quantity * item.unitPrice) || 0).toLocaleString()}</td>
                        </tr>
                      )) 
                    : (
                      <tr key={`${orderIdx}-empty`} className="border-b hover:bg-slate-50">
                        <td className="py-3 px-4">{order.id}</td>
                        <td className="py-3 px-4">{order.date}</td>
                        <td className="py-3 px-4 text-muted-foreground" colSpan={4}>No item details available</td>
                      </tr>
                    )
                  )}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
