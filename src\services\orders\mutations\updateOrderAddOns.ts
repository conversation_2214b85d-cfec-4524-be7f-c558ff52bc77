import { supabase } from "@/integrations/supabase/client";
import { auditService } from '@/services/audit/auditService';
import { DryCleaningItem } from "@/types";

interface OrderAddOns {
  useDetergent?: boolean;
  useFabricConditioner?: boolean;
  useStainRemover?: boolean;
  useBleach?: boolean;
  detergentQuantity?: number;
  conditionerQuantity?: number;
  detergentType?: 'none' | 'regular' | 'color';
  conditionerType?: 'none' | 'regular' | 'fresh' | 'floral';
  dryCleaningItems?: DryCleaningItem[];
  isDryCleaning?: boolean;
}

/**
 * Update order add-ons and special treatments
 */
export async function updateOrderAddOns(orderId: string, addOns: OrderAddOns): Promise<void> {
  try {
    console.log("Updating add-ons for order:", orderId, addOns);
    
    // First check if the orderId is a UUID or a reference code
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    const isUuid = uuidRegex.test(orderId);
    
    // Find the order by its reference ID or UUID using appropriate query syntax
    let query = supabase.from('orders').select('id, reference_code');
    
    if (isUuid) {
      query = query.eq('id', orderId);
    } else {
      query = query.eq('reference_code', orderId);
    }
    
    const { data: orderData, error: findError } = await query.maybeSingle();
      
    if (findError) {
      console.error("Error finding order:", findError);
      throw findError;
    }
    
    if (!orderData) {
      throw new Error(`Order not found: ${orderId}`);
    }
    
    const dbId = orderData.id; // The actual UUID in the database
    
    // Prepare the data for update
    const updateData: Record<string, any> = {
      use_detergent: addOns.useDetergent,
      use_conditioner: addOns.useFabricConditioner,
      use_stain_remover: addOns.useStainRemover,
      use_bleach: addOns.useBleach,
      detergent_quantity: addOns.detergentQuantity?.toString(),
      conditioner_quantity: addOns.conditionerQuantity?.toString(),
      is_dry_cleaning: addOns.isDryCleaning,
      updated_at: new Date().toISOString()
    };
    
    // Add dry cleaning items if present
    if (addOns.dryCleaningItems && addOns.dryCleaningItems.length > 0) {
      updateData['dry_cleaning_items'] = JSON.stringify(addOns.dryCleaningItems);
    }
    
    // Update the order in the database
    const { error: updateError } = await supabase
      .from('orders')
      .update(updateData)
      .eq('id', dbId);
      
    if (updateError) {
      console.error("Error updating order add-ons:", updateError);
      throw updateError;
    }
    
    // Log the action in audit logs
    await auditService.logOrderAction('update_addons', orderId, {
      useDetergent: addOns.useDetergent,
      useFabricConditioner: addOns.useFabricConditioner,
      useStainRemover: addOns.useStainRemover,
      useBleach: addOns.useBleach,
      isDryCleaning: addOns.isDryCleaning,
      hasDryCleaningItems: (addOns.dryCleaningItems && addOns.dryCleaningItems.length > 0)
    });
    
    console.log("Order add-ons updated successfully");
  } catch (error) {
    console.error("Error in updateOrderAddOns:", error);
    throw error;
  }
}
