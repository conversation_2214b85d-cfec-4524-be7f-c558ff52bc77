import { useState, useEffect } from "react";
import { UsersList } from "./UsersList";
import { UserForm } from "./UserForm";
import { UserProfile, getUsers, createUser, updateUser, CreateUserData, UpdateUserData } from "@/services/userService";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { UserPlus, RefreshCcw, AlertCircle } from "lucide-react";
import { toast } from "sonner";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
export function UsersManagement() {
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [formOpen, setFormOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserProfile | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [retryCount, setRetryCount] = useState(0);

  // Fetch users on component mount
  useEffect(() => {
    loadUsers();
  }, [retryCount]);
  const loadUsers = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log("Fetching users data...");
      const fetchedUsers = await getUsers();
      if (fetchedUsers.length === 0) {
        console.log("No users returned from API");
        setError("No users found in the system. This might be due to missing data or permission issues.");
      } else {
        console.log(`Successfully loaded ${fetchedUsers.length} users`);
        setUsers(fetchedUsers);
      }
    } catch (err) {
      console.error("Error loading users:", err);
      setError("Failed to load users. Please try again or check your network connection.");
    } finally {
      setLoading(false);
    }
  };
  const handleCreateUser = async (data: CreateUserData) => {
    try {
      setIsSubmitting(true);
      const result = await createUser(data);
      if (result.success) {
        toast.success(result.message);
        setFormOpen(false);
        loadUsers(); // Reload users to show the new one
      } else {
        toast.error(result.message);
      }
    } catch (err) {
      console.error("Error creating user:", err);
      toast.error("An unexpected error occurred");
    } finally {
      setIsSubmitting(false);
    }
  };
  const handleUpdateUser = async (data: UpdateUserData) => {
    try {
      setIsSubmitting(true);
      const result = await updateUser(data);
      if (result.success) {
        toast.success(result.message);
        setFormOpen(false);
        loadUsers(); // Reload users to show the updated one
      } else {
        toast.error(result.message);
      }
    } catch (err) {
      console.error("Error updating user:", err);
      toast.error("An unexpected error occurred");
    } finally {
      setIsSubmitting(false);
    }
  };
  const handleEditUser = (user: UserProfile) => {
    setSelectedUser(user);
    setFormOpen(true);
  };
  const handleAddNewClick = () => {
    setSelectedUser(null);
    setFormOpen(true);
  };
  const handleRetry = () => {
    setRetryCount(prev => prev + 1);
  };
  return <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          {/* Removed the redundant "User Management" heading */}
          
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={handleRetry} disabled={loading} className="flex items-center gap-1">
            <RefreshCcw className="h-4 w-4" />
            Refresh
          </Button>
          <Button size="sm" onClick={handleAddNewClick} className="flex items-center gap-1">
            <UserPlus className="h-4 w-4" />
            Add New User
          </Button>
        </div>
      </div>

      {error && <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="ml-2">
            {error}
          </AlertDescription>
        </Alert>}

      {loading ? <div className="space-y-4">
          <div className="rounded-md border">
            <div className="p-4">
              <Skeleton className="h-8 w-full mb-4" />
              <div className="space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-5/6" />
              </div>
            </div>
          </div>
        </div> : <UsersList users={users} onEdit={handleEditUser} onUserDeleted={loadUsers} />}

      {/* User Dialog (Create/Edit) */}
      <Dialog open={formOpen} onOpenChange={setFormOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>{selectedUser ? "Edit User" : "Create New User"}</DialogTitle>
            <DialogDescription>
              {selectedUser ? "Update user details and permissions." : "Fill in the information to create a new user."}
            </DialogDescription>
          </DialogHeader>
          <UserForm user={selectedUser || undefined} onSubmit={selectedUser ? handleUpdateUser : handleCreateUser} isLoading={isSubmitting} />
        </DialogContent>
      </Dialog>
    </div>;
}