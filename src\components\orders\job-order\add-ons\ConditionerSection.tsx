
import React from "react";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";

interface ConditionerSectionProps {
  useFabricConditioner: boolean;
  conditionerType: 'none' | 'regular' | 'fresh' | 'floral';
  conditionerQuantity: string;
  onConditionerChange: (checked: boolean) => void;
  onConditionerTypeChange: (value: 'none' | 'regular' | 'fresh' | 'floral') => void;
  onConditionerQuantityChange: (value: string) => void;
}

export function ConditionerSection({
  useFabricConditioner,
  conditionerType,
  conditionerQuantity,
  onConditionerChange,
  onConditionerTypeChange,
  onConditionerQuantityChange
}: ConditionerSectionProps) {
  return (
    <div className="border rounded-md p-4 bg-white">
      <div className="flex items-center gap-2">
        <Checkbox 
          id="use-conditioner" 
          checked={useFabricConditioner}
          onCheckedChange={onConditionerChange}
        />
        <Label htmlFor="use-conditioner" className="font-medium">Use Fabric Conditioner</Label>
      </div>
      
      {useFabricConditioner && (
        <div className="mt-3 grid grid-cols-2 gap-3">
          <div>
            <Label htmlFor="conditioner-type">Conditioner Type</Label>
            <Select 
              value={conditionerType} 
              onValueChange={onConditionerTypeChange}
              disabled={!useFabricConditioner}
            >
              <SelectTrigger id="conditioner-type">
                <SelectValue placeholder="Select conditioner type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="regular">Regular Conditioner</SelectItem>
                <SelectItem value="fresh">Fresh Scent</SelectItem>
                <SelectItem value="floral">Floral Scent</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="conditioner-qty">Quantity</Label>
            <Input
              id="conditioner-qty"
              type="number"
              min="1"
              value={conditionerQuantity}
              onChange={(e) => onConditionerQuantityChange(e.target.value)}
              disabled={!useFabricConditioner}
            />
          </div>
        </div>
      )}
    </div>
  );
}
