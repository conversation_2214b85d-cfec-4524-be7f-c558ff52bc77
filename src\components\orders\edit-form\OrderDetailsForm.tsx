
import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { DryCleaningIndicator } from "../job-order/add-ons/DryCleaningIndicator";

interface OrderDetailsFormProps {
  formData: {
    customerName: string;
    customerPhone: string;
    notes: string;
    weightKilos: number;
    numberOfPieces: number;
    customerType: "client" | "walk-in";
    isDryCleaning: boolean;
  };
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  handleNumberChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleCustomerTypeChange: (value: "client" | "walk-in") => void;
  handleDryCleaningChange: (checked: boolean) => void;
}

export function OrderDetailsForm({
  formData,
  handleInputChange,
  handleNumberChange,
  handleCustomerTypeChange,
  handleDryCleaningChange
}: OrderDetailsFormProps) {
  return (
    <div className="grid gap-4 py-4">
      <div className="flex items-center space-x-2 py-2">
        <Switch 
          id="dry-cleaning" 
          checked={formData.isDryCleaning}
          onCheckedChange={handleDryCleaningChange}
        />
        <Label htmlFor="dry-cleaning" className="font-medium">
          Dry Cleaning Service
        </Label>
      </div>

      {formData.isDryCleaning && (
        <DryCleaningIndicator isDryCleaning={formData.isDryCleaning} />
      )}
    
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="customerName">Customer Name</Label>
          <Input
            id="customerName"
            name="customerName"
            value={formData.customerName}
            onChange={handleInputChange}
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="customerPhone">Phone Number</Label>
          <Input
            id="customerPhone"
            name="customerPhone"
            value={formData.customerPhone}
            onChange={handleInputChange}
          />
        </div>
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="customerType">Customer Type</Label>
        <Select
          value={formData.customerType}
          onValueChange={handleCustomerTypeChange as any}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select customer type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="client">Client</SelectItem>
            <SelectItem value="walk-in">Walk-in</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="weightKilos">Weight (kg)</Label>
          <Input
            id="weightKilos"
            name="weightKilos"
            type="number"
            step="0.1"
            value={formData.weightKilos}
            onChange={handleNumberChange}
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="numberOfPieces">Number of Pieces</Label>
          <Input
            id="numberOfPieces"
            name="numberOfPieces"
            type="number"
            value={formData.numberOfPieces}
            onChange={handleNumberChange}
          />
        </div>
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="notes">Notes</Label>
        <Textarea
          id="notes"
          name="notes"
          value={formData.notes}
          onChange={handleInputChange}
          rows={3}
        />
      </div>
    </div>
  );
}
