
import { useState, useEffect } from 'react';
import { TreatmentHeader } from './treatments/TreatmentHeader';
import { StainRemovalToggle } from './treatments/StainRemovalToggle';
import { DetergentTypeSelect } from './treatments/DetergentTypeSelect';
import { ConditionerTypeSelect } from './treatments/ConditionerTypeSelect';

export interface ItemTreatmentOptionsProps {
  itemId: string;
  itemName: string;
  onTreatmentChange: (itemId: string, treatments: ItemTreatments) => void;
  initialTreatments?: ItemTreatments;
}

export interface ItemTreatments {
  useStainRemoval: boolean;
  detergentType: 'none' | 'regular' | 'color';
  conditionerType: 'none' | 'regular' | 'fresh' | 'floral';
}

export function ItemTreatmentOptions({ 
  itemId, 
  itemName, 
  onTreatmentChange,
  initialTreatments 
}: ItemTreatmentOptionsProps) {
  const [treatments, setTreatments] = useState<ItemTreatments>(initialTreatments || {
    useStainRemoval: false,
    detergentType: 'none',
    conditionerType: 'none'
  });

  // Update treatments when initialTreatments changes (e.g. when a new item is selected)
  useEffect(() => {
    if (initialTreatments) {
      setTreatments(initialTreatments);
    }
  }, [initialTreatments]);

  const handleTreatmentChange = (key: keyof ItemTreatments, value: any) => {
    const updatedTreatments = { ...treatments, [key]: value };
    setTreatments(updatedTreatments);
    onTreatmentChange(itemId, updatedTreatments);
  };

  return (
    <div className="p-4 border rounded-lg bg-gray-50 mt-2 space-y-4">
      <TreatmentHeader itemName={itemName} />
      
      <StainRemovalToggle 
        itemId={itemId}
        checked={treatments.useStainRemoval}
        onCheckedChange={(checked) => handleTreatmentChange('useStainRemoval', checked)}
      />

      <DetergentTypeSelect 
        itemId={itemId}
        value={treatments.detergentType}
        onChange={(value) => handleTreatmentChange('detergentType', value)}
      />

      <ConditionerTypeSelect 
        itemId={itemId}
        value={treatments.conditionerType}
        onChange={(value) => handleTreatmentChange('conditionerType', value)}
      />
    </div>
  );
}
