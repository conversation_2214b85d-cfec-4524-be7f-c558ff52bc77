
import { useState, useEffect } from 'react';
import { TreatmentHeader } from './treatments/TreatmentHeader';
import { StainRemovalToggle } from './treatments/StainRemovalToggle';
import { BeachTreatmentToggle } from './treatments/BeachTreatmentToggle';
import { DetergentTypeSelect } from './treatments/DetergentTypeSelect';
import { ConditionerTypeSelect } from './treatments/ConditionerTypeSelect';
import { ItemTreatments } from '../order-form/types';

export interface ItemTreatmentOptionsProps {
  itemId: string;
  itemName: string;
  onTreatmentChange: (itemId: string, treatments: ItemTreatments) => void;
  initialTreatments?: ItemTreatments;
}

export { type ItemTreatments };

export function ItemTreatmentOptions({ 
  itemId, 
  itemName, 
  onTreatmentChange,
  initialTreatments 
}: ItemTreatmentOptionsProps) {
  const [treatments, setTreatments] = useState<ItemTreatments>(initialTreatments || {
    useStainRemoval: false,
    useBeachTreatment: false,
    detergentType: 'none',
    conditionerType: 'none'
  });

  // Update treatments when initialTreatments changes (e.g. when a new item is selected)
  useEffect(() => {
    if (initialTreatments) {
      setTreatments(initialTreatments);
    }
  }, [initialTreatments]);

  const handleTreatmentChange = (key: keyof ItemTreatments, value: any) => {
    console.log(`Changing ${key} to ${value} for item ${itemId}`);
    const updatedTreatments = { ...treatments, [key]: value };
    setTreatments(updatedTreatments);
    onTreatmentChange(itemId, updatedTreatments);
  };

  return (
    <div className="p-4 border rounded-lg bg-gray-50 mt-2 space-y-4">
      <TreatmentHeader itemName={itemName} />
      
      <div className="grid gap-4">
        <div className="space-y-4">
          <h4 className="text-sm font-semibold">Special Treatments</h4>
          <div className="pl-2 space-y-3">
            <StainRemovalToggle 
              itemId={itemId}
              checked={treatments.useStainRemoval}
              onCheckedChange={(checked) => handleTreatmentChange('useStainRemoval', checked)}
            />

            <BeachTreatmentToggle 
              itemId={itemId}
              checked={treatments.useBeachTreatment}
              onCheckedChange={(checked) => handleTreatmentChange('useBeachTreatment', checked)}
            />
          </div>
        </div>
      
        <div className="space-y-4">
          <h4 className="text-sm font-semibold">Laundry Preferences</h4>
          <div className="pl-2 space-y-3">
            <DetergentTypeSelect 
              itemId={itemId}
              value={treatments.detergentType}
              onChange={(value) => handleTreatmentChange('detergentType', value)}
            />

            <ConditionerTypeSelect 
              itemId={itemId}
              value={treatments.conditionerType}
              onChange={(value) => handleTreatmentChange('conditionerType', value)}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
