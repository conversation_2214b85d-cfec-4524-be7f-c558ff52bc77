import React from 'react';
import { cn } from '@/lib/utils';

interface ToggleSelectionButtonProps {
  selected: boolean;
  onToggle: () => void;
  children: React.ReactNode;
  className?: string;
  disabled?: boolean;
}

export function ToggleSelectionButton({
  selected,
  onToggle,
  children,
  className,
  disabled = false
}: ToggleSelectionButtonProps) {
  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!disabled) {
      console.log('ToggleSelectionButton clicked:', { selected });
      onToggle();
    }
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    e.preventDefault();
    if (!disabled) {
      console.log('ToggleSelectionButton touch end:', { selected });
      onToggle();
    }
  };

  return (
    <button
      type="button"
      className={cn(
        // Base styles
        "w-full p-4 rounded-lg border-2 transition-all duration-200",
        "focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2",
        "touch-action-manipulation",
        "active:scale-[0.98]",
        "text-left",
        // Selected state
        selected
          ? "border-primary bg-primary/10 text-primary font-medium"
          : "border-gray-200 bg-white text-gray-700 hover:border-gray-300 hover:bg-gray-50",
        // Disabled state
        disabled && "opacity-50 cursor-not-allowed",
        className
      )}
      onClick={handleClick}
      onTouchEnd={handleTouchEnd}
      disabled={disabled}
      aria-pressed={selected}
    >
      {children}
    </button>
  );
}
