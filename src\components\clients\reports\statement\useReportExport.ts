
import { useState } from 'react';
import { useToast } from '@/hooks/use-toast';
import { OrderSummary } from '@/hooks/orders/reports/types';
import { useReactToPrint } from '@/hooks/use-print';

interface UseReportExportProps {
  clientId: string;
  reportRef: React.RefObject<HTMLDivElement>;
  client: any;
}

export function useReportExport({ clientId, reportRef, client }: UseReportExportProps) {
  const [isPrinting, setIsPrinting] = useState(false);
  const { toast } = useToast();
  
  // Handle printing
  const handlePrint = useReactToPrint({
    content: () => reportRef.current,
    documentTitle: `Statement - ${client?.name || 'Client'}`,
    onBeforePrint: () => setIsPrinting(true),
    onAfterPrint: () => {
      setIsPrinting(false);
      toast({ title: "Report printed successfully" });
    },
    onPrintError: () => {
      setIsPrinting(false);
      toast({ 
        title: "Print Failed", 
        description: "Unable to print document",
        variant: "destructive"
      });
    }
  });
  
  // Handle CSV export
  const handleExportCSV = (orders: OrderSummary[]) => {
    try {
      if (orders.length === 0) {
        toast({ 
          title: "Nothing to export", 
          description: "No orders available for export",
          variant: "destructive"
        });
        return;
      }
      
      // Prepare CSV data
      const headers = [
        'Order ID', 
        'Date', 
        'Due Date', 
        'Total Amount', 
        'Paid Amount', 
        'Status'
      ];
      
      const rows = orders.map(order => [
        order.referenceCode || order.id,
        order.orderDate,
        order.dueDate || '',
        order.amount.toFixed(2),
        order.paidAmount.toFixed(2),
        order.paidAmount >= order.amount ? 'Paid' : order.isOverdue ? 'Overdue' : 'Unpaid'
      ]);
      
      // Convert to CSV format
      const csvContent = [
        headers.join(','),
        ...rows.map(row => row.join(','))
      ].join('\n');
      
      // Create download link
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', `statement-${client?.name || 'client'}.csv`);
      link.style.visibility = 'hidden';
      
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      toast({ title: "CSV exported successfully" });
      
    } catch (error) {
      console.error('CSV export error:', error);
      toast({ 
        title: "Export Failed", 
        description: "Unable to export to CSV",
        variant: "destructive"
      });
    }
  };
  
  return {
    isPrinting,
    handlePrint,
    handleExportCSV
  };
}
