
import React from 'react';

interface AddOnsBreakdownProps {
  detergentType: 'none' | 'regular' | 'color';
  conditionerType: 'none' | 'regular' | 'fresh' | 'floral';
  detergentQuantity: number;
  conditionerQuantity: number;
  useStainRemover: boolean;
  useBleach: boolean;
  addOnPrice: number;
}

export function AddOnsBreakdown({
  detergentType,
  conditionerType,
  detergentQuantity,
  conditionerQuantity,
  useStainRemover,
  useBleach,
  addOnPrice
}: AddOnsBreakdownProps) {
  const hasAddOns = 
    detergentType !== "none" || 
    conditionerType !== "none" || 
    useStainRemover || 
    useBleach;

  if (!hasAddOns) return null;

  return (
    <div className="border rounded-md p-3">
      <h4 className="font-medium mb-2">Add-Ons</h4>
      <div className="space-y-1 text-sm">
        {detergentType !== "none" && (
          <div className="flex justify-between">
            <span>{detergentType} Detergent x{detergentQuantity}</span>
          </div>
        )}
        
        {conditionerType !== "none" && (
          <div className="flex justify-between">
            <span>{conditionerType} Conditioner x{conditionerQuantity}</span>
          </div>
        )}
        
        {useStainRemover && (
          <div className="flex justify-between">
            <span>Stain Remover</span>
          </div>
        )}
        
        {useBleach && (
          <div className="flex justify-between">
            <span>Bleach</span>
          </div>
        )}
        
        <div className="flex justify-between font-medium pt-1 border-t">
          <span>Add-Ons Total:</span>
          <span>₱{addOnPrice.toFixed(2)}</span>
        </div>
      </div>
    </div>
  );
}

export function DryCleaningItemsBreakdown({ 
  items, 
  basePrice 
}: { 
  items: any[];
  basePrice: number;
}) {
  if (!items || items.length === 0) return null;
  
  return (
    <div className="border rounded-md p-3">
      <h4 className="font-medium mb-2">Dry Cleaning Items</h4>
      <div className="space-y-1 text-sm">
        {items.map((item, index) => (
          <div key={index} className="flex justify-between">
            <span>
              {item.name} x{item.quantity}
            </span>
            <span>₱{((item.price || 0) * (item.quantity || 1)).toFixed(2)}</span>
          </div>
        ))}
        
        <div className="flex justify-between font-medium pt-1 border-t">
          <span>Subtotal:</span>
          <span>₱{basePrice.toFixed(2)}</span>
        </div>
      </div>
    </div>
  );
}

export function ClientItemsBreakdown({ 
  items, 
  basePrice 
}: { 
  items: any[];
  basePrice: number;
}) {
  if (!items || items.length === 0) return null;
  
  return (
    <div className="border rounded-md p-3">
      <h4 className="font-medium mb-2">Items</h4>
      <div className="space-y-1 text-sm">
        {items.map((item, index) => (
          <div key={index} className="flex justify-between">
            <span>
              {item.name} x{item.quantity}
            </span>
            <span>₱{((item.unitPrice || item.unit_price || 0) * (item.quantity || 1)).toFixed(2)}</span>
          </div>
        ))}
        
        <div className="flex justify-between font-medium pt-1 border-t">
          <span>Subtotal:</span>
          <span>₱{basePrice.toFixed(2)}</span>
        </div>
      </div>
    </div>
  );
}
