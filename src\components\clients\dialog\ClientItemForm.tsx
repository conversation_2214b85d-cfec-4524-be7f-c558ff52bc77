
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState, useEffect, useRef } from "react";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { FormHeader } from "./form/FormHeader";
import { z } from "zod";
import { Input } from "@/components/ui/input";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { CommandInput, CommandList, CommandItem, Command } from "@/components/ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";

// Simplified schema without category
const itemFormSchema = z.object({
  name: z.string().min(2, { message: "Item name is required" }),
  unitPrice: z.number().min(0, { message: "Price must be a positive number" })
});

type ItemFormValues = z.infer<typeof itemFormSchema>;

interface ClientItemFormProps {
  setIsAddingItem: (value: boolean) => void;
  clientItems: Array<{name: string, unitPrice: number}>;
  setClientItems: (items: Array<{name: string, unitPrice: number}>) => void;
}

export function ClientItemForm({ setIsAddingItem, clientItems, setClientItems }: ClientItemFormProps) {
  const { toast } = useToast();
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [nameError, setNameError] = useState("");
  const inputRef = useRef<HTMLInputElement>(null);

  // Get unique item names from existing client items
  useEffect(() => {
    const existingNames = clientItems.map(item => item.name);
    setSuggestions(existingNames);
  }, [clientItems]);

  const itemForm = useForm<ItemFormValues>({
    resolver: zodResolver(itemFormSchema),
    defaultValues: {
      name: "",
      unitPrice: 100 // Default price
    }
  });

  const validateItemName = (name: string): boolean => {
    const normalizedName = name.trim().toLowerCase();
    const isDuplicate = clientItems.some(
      item => item.name.toLowerCase() === normalizedName
    );
    
    if (isDuplicate) {
      setNameError(`"${name}" already exists in the items list`);
      return false;
    }
    
    setNameError("");
    return true;
  };

  const handleAddItem = (data: ItemFormValues) => {
    const newItem = {
      name: data.name.trim(),
      unitPrice: data.unitPrice
    };
    
    if (!validateItemName(newItem.name)) {
      return;
    }
    
    setClientItems([...clientItems, newItem]);
    
    itemForm.reset({
      name: "",
      unitPrice: 100
    });
    
    toast({
      title: "Item added",
      description: `"${newItem.name}" has been added to the list`
    });
  };

  const filteredSuggestions = suggestions.filter(
    item => item.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleSelectSuggestion = (value: string) => {
    itemForm.setValue("name", value);
    setShowSuggestions(false);
    setTimeout(() => {
      // Focus on the price input after selecting a suggestion
      const priceInput = document.querySelector('input[name="unitPrice"]');
      if (priceInput instanceof HTMLInputElement) {
        priceInput.focus();
      }
    }, 0);
  };

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newName = e.target.value;
    itemForm.setValue("name", newName);
    setSearchQuery(newName);
    setShowSuggestions(newName.length >= 2);
    
    if (newName.length > 0) {
      validateItemName(newName);
    } else {
      setNameError("");
    }
  };

  return (
    <Form {...itemForm}>
      <form onSubmit={itemForm.handleSubmit(handleAddItem)} className="space-y-4 border rounded-md p-4 bg-card">
        <FormHeader onCancel={() => setIsAddingItem(false)} />
        
        {/* Item Name Field with Auto-suggest */}
        <FormField
          control={itemForm.control}
          name="name"
          render={({ field }) => (
            <FormItem className="relative">
              <FormLabel>Item Name</FormLabel>
              <FormControl>
                <Popover open={showSuggestions && filteredSuggestions.length > 0} onOpenChange={setShowSuggestions}>
                  <PopoverTrigger asChild>
                    <div className="relative">
                      <Input 
                        placeholder="Enter item name" 
                        {...field} 
                        ref={inputRef}
                        onChange={handleNameChange}
                        onClick={() => {
                          if (field.value && field.value.length >= 2) {
                            setShowSuggestions(true);
                          }
                        }}
                        className={nameError ? "border-red-500" : ""}
                      />
                    </div>
                  </PopoverTrigger>
                  <PopoverContent className="p-0 w-[300px]" align="start">
                    <Command>
                      <CommandList>
                        {filteredSuggestions.map((suggestion) => (
                          <CommandItem 
                            key={suggestion} 
                            value={suggestion}
                            onSelect={handleSelectSuggestion}
                          >
                            {suggestion}
                          </CommandItem>
                        ))}
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
              </FormControl>
              {nameError && <p className="text-sm text-red-500 mt-1">{nameError}</p>}
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Unit Price Field */}
        <FormField
          control={itemForm.control}
          name="unitPrice"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Price (₱)</FormLabel>
              <FormControl>
                <Input 
                  type="number" 
                  placeholder="100.00" 
                  {...field} 
                  onChange={e => field.onChange(parseFloat(e.target.value) || 0)}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit" size="sm" disabled={!!nameError}>
          Add Item
        </Button>
      </form>
    </Form>
  );
}
