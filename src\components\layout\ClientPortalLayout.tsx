
import { useState, useEffect } from 'react';
import { useLocation, Outlet } from 'react-router-dom';
import { Menu } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/auth';
import { TopBar } from '@/components/layout/TopBar';
import { signOut } from '@/lib/auth';
import { ClientSidebar } from './client-portal/ClientSidebar';
import { MobileNavigation } from './client-portal/MobileNavigation';
import { ContentHeader } from './client-portal/ContentHeader';

interface ClientPortalLayoutProps {
  children?: React.ReactNode; // Make children optional
}

export function ClientPortalLayout({ children }: ClientPortalLayoutProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [clientName, setClientName] = useState('');
  const location = useLocation();
  const { toast } = useToast();
  const { user } = useAuth();

  useEffect(() => {
    const fetchClientInfo = async () => {
      try {
        if (!user) return;

        console.log("Fetching client info for user:", user.email);

        // First try the users_clients association
        const { data: userClientData, error: userClientError } = await supabase
          .from('users_clients')
          .select('client_id')
          .eq('user_id', user.id)
          .maybeSingle();

        if (userClientError) {
          console.error("Error fetching client ID from users_clients:", userClientError);
        }

        if (userClientData?.client_id) {
          console.log("Found client association in users_clients:", userClientData.client_id);
          
          const { data: client, error: clientError } = await supabase
            .from('clients')
            .select('name')
            .eq('id', userClientData.client_id)
            .single();

          if (clientError) {
            console.error("Error fetching client details:", clientError);
          } else if (client?.name) {
            setClientName(client.name);
            return;
          }
        } else {
          console.log("No client association found in users_clients, trying email match");
          
          // Try to find a client by email match for specific cases
          if (user.email === '<EMAIL>' || user.email === '<EMAIL>') {
            const { data: clientsByEmail, error: clientsError } = await supabase
              .from('clients')
              .select('id, name, email')
              .ilike('email', user.email)
              .maybeSingle();
              
            if (!clientsError && clientsByEmail?.name) {
              console.log('Found client by email match:', clientsByEmail);
              setClientName(clientsByEmail.name);
              
              // Create the user-client association for future use using edge function
              try {
                const { error: fnError } = await supabase.functions.invoke('associate-client-user', {
                  body: {
                    userId: user.id,
                    clientId: clientsByEmail.id
                  }
                });
                
                if (fnError) {
                  console.error("Error creating user-client association:", fnError);
                } else {
                  console.log("Created user-client association for future use");
                }
              } catch (err) {
                console.error("Exception while creating user-client association:", err);
              }
              
              return;
            }
          }
          
          // Default client name if nothing is found
          setClientName(user.email?.split('@')[0] || 'Client');
        }
      } catch (error) {
        console.error('Error fetching client info:', error);
      }
    };

    fetchClientInfo();
  }, [user]);

  const handleLogout = async () => {
    try {
      await signOut();
      toast({
        title: 'Logged out',
        description: 'You have been successfully logged out.',
      });
    } catch (error) {
      console.error('Error logging out:', error);
      toast({
        title: 'Error',
        description: 'Failed to log out. Please try again.',
        variant: 'destructive',
      });
    }
  };

  return (
    <div className="flex min-h-screen bg-[#1E293B]">
      <ClientSidebar 
        clientName={clientName} 
        user={user} 
        onLogout={handleLogout} 
      />

      <div className="flex flex-col flex-1">
        <TopBar onMenuClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)} />

        <MobileNavigation 
          isOpen={isMobileMenuOpen} 
          onClose={() => setIsMobileMenuOpen(false)} 
          onLogout={handleLogout}
          pathname={location.pathname}
          user={user}
        />

        <main className="flex-1 overflow-y-auto p-4 md:p-6 bg-gray-50">
          <ContentHeader clientName={clientName} />
          {children || <Outlet />}
        </main>
      </div>
    </div>
  );
}
