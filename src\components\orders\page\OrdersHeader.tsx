import { But<PERSON> } from "@/components/ui/button";
import { RefreshCcw, Plus, UserRound, User } from "lucide-react";
interface OrdersHeaderProps {
  onRefresh: () => void;
  onAddOrder: (orderType: "walk-in" | "client") => void;
  isLoading: boolean;
  isRefreshing: boolean;
}
export function OrdersHeader({
  onRefresh,
  onAddOrder,
  isLoading,
  isRefreshing
}: OrdersHeaderProps) {
  return <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
      <h1 className="text-2xl font-bold tracking-tight">Orders</h1>
      <div className="flex items-center gap-2">
        
        <Button onClick={() => onAddOrder("walk-in")} size="sm" variant="outline" className="gap-1">
          <User className="h-4 w-4" />
          New Walk-in Order
        </Button>
        <Button onClick={() => onAddOrder("client")} size="sm" className="gap-1">
          <UserRound className="h-4 w-4" />
          New Client Order
        </Button>
      </div>
    </div>;
}