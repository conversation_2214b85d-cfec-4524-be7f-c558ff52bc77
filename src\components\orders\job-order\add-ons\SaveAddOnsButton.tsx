
import { Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";

interface SaveAddOnsButtonProps {
  isSaving: boolean;
  onSave: () => void;
}

export function SaveAddOnsButton({ isSaving, onSave }: SaveAddOnsButtonProps) {
  return (
    <Button onClick={onSave} disabled={isSaving}>
      {isSaving ? (
        <>
          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          Saving...
        </>
      ) : (
        "Save Add-ons"
      )}
    </Button>
  );
}
