
import { FormField, FormItem, FormControl, FormLabel } from "@/components/ui/form";
import { UseFormReturn } from "react-hook-form";
import { Checkbox } from "@/components/ui/checkbox";

// This component is now deprecated and should not be used
// Special treatments should be added at the item level instead
interface SpecialTreatmentsSectionProps {
  form: UseFormReturn<any>;
}

export function SpecialTreatmentsSection({ form }: SpecialTreatmentsSectionProps) {
  // Component is deprecated - special treatments should be handled at the item level
  return null;
}
