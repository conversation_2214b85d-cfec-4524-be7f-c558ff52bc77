
import { supabase } from "@/integrations/supabase/client";
import { UserProfile } from "./types";

// Get all users with their profiles and roles using the Edge Function
export async function getUsers(): Promise<UserProfile[]> {
  try {
    console.log('Attempting to fetch users via Edge Function');
    // Use the edge function to get users with their emails
    const { data, error } = await supabase.functions.invoke('list-users');
    
    if (error) {
      console.error('Error fetching users from edge function:', error);
      
      // Fallback to getting only profiles and roles
      console.log('Using fallback method to retrieve users');
      return await getUsersFallback();
    }

    console.log(`Successfully retrieved ${data.length} users from Edge Function`);
    return data;
  } catch (error) {
    console.error('Error in getUsers:', error);
    
    // Fallback to getting only profiles and roles
    console.log('Using fallback method after exception');
    return await getUsersFallback();
  }
}

// Fallback method that doesn't rely on admin privileges
async function getUsersFallback(): Promise<UserProfile[]> {
  try {
    console.log('Executing getUsersFallback');
    
    // Get profiles first
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('id, first_name, last_name, phone, position, created_at');

    if (profilesError) {
      console.error('Error fetching profiles:', profilesError);
      return [];
    }

    console.log(`Found ${profiles?.length || 0} profiles in fallback method`);

    // Get user roles
    const { data: roles, error: rolesError } = await supabase
      .from('user_roles')
      .select('user_id, role');

    if (rolesError) {
      console.error('Error fetching user roles:', rolesError);
      return [];
    }

    console.log(`Found ${roles?.length || 0} role records in fallback method`);

    // Map the data without emails
    const users: UserProfile[] = (profiles || []).map(profile => {
      const userRole = roles?.find(r => r.user_id === profile.id);
      
      console.log(`Mapping profile ID ${profile.id} with role: ${userRole?.role || 'client'}`);
      
      return {
        id: profile.id,
        email: "", // Email is not available without admin privileges
        first_name: profile.first_name || null,
        last_name: profile.last_name || null,
        phone: profile.phone || null,
        position: profile.position || null,
        role: userRole?.role || 'client',
        created_at: profile.created_at || new Date().toISOString(),
      };
    });

    console.log(`Returning ${users.length} users from fallback method`);
    return users;
  } catch (error) {
    console.error('Error in getUsersFallback:', error);
    return [];
  }
}
