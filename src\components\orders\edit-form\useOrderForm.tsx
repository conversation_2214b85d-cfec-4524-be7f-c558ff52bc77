
import { useState, useEffect } from "react";
import { Order } from "@/types";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { OrderFormData } from "./OrderFormTypes";

export function useOrderForm(order: Order | null, onOrderUpdated?: () => void) {
  const [formData, setFormData] = useState<OrderFormData>({
    customerName: "",
    customerPhone: "",
    notes: "",
    weightKilos: 0,
    numberOfPieces: 1,
    customerType: "walk-in",
    isDryCleaning: false,
  });
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  
  // Populate form when order changes
  useEffect(() => {
    if (order) {
      setFormData({
        customerName: order.customer?.name || "",
        customerPhone: order.customer?.phone || "",
        notes: order.notes || "",
        weightKilos: order.weightKilos || 0,
        numberOfPieces: order.numberOfPieces || 1,
        customerType: order.customerType || "walk-in",
        isDryCleaning: order.isDryCleaning || false,
      });
    }
  }, [order]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleNumberChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: parseFloat(value) || 0 }));
  };

  const handleCustomerTypeChange = (value: "client" | "walk-in") => {
    setFormData((prev) => ({ ...prev, customerType: value }));
  };
  
  const handleDryCleaningChange = (checked: boolean) => {
    setFormData((prev) => ({ ...prev, isDryCleaning: checked }));
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!order) return;
    
    setIsSubmitting(true);
    
    try {
      // Prepare update data
      const updateData = {
        customer_name: formData.customerName,
        phone_number: formData.customerPhone,
        notes: formData.notes,
        weight_kilos: formData.weightKilos,
        number_of_pieces: formData.numberOfPieces,
        customer_type: formData.customerType,
        is_dry_cleaning: formData.isDryCleaning,
        updated_at: new Date().toISOString(),
      };
      
      // Update in database
      const { error } = await supabase
        .from('orders')
        .update(updateData)
        .eq('id', order.uuid);
        
      if (error) {
        throw error;
      }
      
      toast({
        title: "Order Updated",
        description: `Successfully updated order ${order.id}`,
      });
      
      // Trigger refresh
      if (onOrderUpdated) onOrderUpdated();
      
    } catch (error: any) {
      console.error('Error updating order:', error);
      toast({
        title: "Update Failed",
        description: error.message || "Failed to update the order",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return {
    formData,
    isSubmitting,
    handleInputChange,
    handleNumberChange,
    handleCustomerTypeChange,
    handleDryCleaningChange,
    handleSubmit
  };
}
