
// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://fombybvzokxyfnosffvn.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZvbWJ5YnZ6b2t4eWZub3NmZnZuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ0NDgwNzYsImV4cCI6MjA2MDAyNDA3Nn0.zTjqV9Skc7KI3IQztXf9Uk6zCQHuSMY41ktXSVWRevQ";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(
  SUPABASE_URL, 
  SUPABASE_PUBLISHABLE_KEY,
  {
    auth: {
      persistSession: true,
      autoRefreshToken: true,
      storageKey: 'supabase.auth.token',
      storage: localStorage
    }
  }
);
