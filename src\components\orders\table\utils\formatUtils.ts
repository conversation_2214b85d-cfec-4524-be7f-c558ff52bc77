
/**
 * Format currency consistently
 * @param value Number to format as currency
 * @returns Formatted currency string
 */
export function formatCurrency(value: number): string {
  return new Intl.NumberFormat("en-PH", {
    style: "currency",
    currency: "PHP",
  }).format(value);
}

/**
 * Parse service items from order
 * @param order Order with items field
 * @returns Array of service items
 */
export function parseServiceItems(order: any): any[] {
  let serviceItems: any[] = [];
  try {
    // The items field might be stored as a JSON string
    if (typeof order.items === 'string' && order.items) {
      serviceItems = JSON.parse(order.items);
    } else if (Array.isArray(order.items)) {
      serviceItems = order.items;
    }
  } catch (e) {
    console.error("Failed to parse service items:", e);
  }
  return serviceItems;
}

/**
 * Determine if an order is from a client
 * @param order Order to check
 * @returns Boolean indicating if it's a client order
 */
export function isClientOrder(order: any): boolean {
  return order.customerType === "client" || 
         (!!order.clientId && order.clientId !== '00000000-0000-0000-0000-000000000000');
}
