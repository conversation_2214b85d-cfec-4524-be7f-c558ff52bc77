
import { SERVICE_TYPES } from "../constants";
import { AddOnSelections } from "../../OrderFormTypes";

// Constants for pricing
const DETERGENT_PRICES = {
  none: 0,
  regular: 15,
  color: 20
};

const CONDITIONER_PRICES = {
  none: 0,
  regular: 10,
  fresh: 15,
  floral: 15
};

// Note: These fixed prices are no longer used for item-based pricing
// as we now double the entire item price instead
const STAIN_REMOVER_PRICE = 25;
const BLEACH_PRICE = 25;

export async function calculateAddOnPrice(
  weightKilos: number,
  addOns: AddOnSelections
): Promise<number> {
  let addOnTotal = 0;
  
  // Calculate detergent price based on type
  if (addOns.detergentType !== "none") {
    const detergentPrice = DETERGENT_PRICES[addOns.detergentType];
    addOnTotal += detergentPrice * (addOns.detergentQuantity || 1);
  }
  
  // Calculate conditioner price based on type
  if (addOns.conditionerType !== "none") {
    const conditionerPrice = CONDITIONER_PRICES[addOns.conditionerType];
    addOnTotal += conditionerPrice * (addOns.conditionerQuantity || 1);
  }
  
  // Add stain remover price if selected
  if (addOns.useStainRemover) {
    addOnTotal += STAIN_REMOVER_PRICE;
  }
  
  // Add bleach price if selected
  if (addOns.useBleach) {
    addOnTotal += BLEACH_PRICE;
  }
  
  return addOnTotal;
}
