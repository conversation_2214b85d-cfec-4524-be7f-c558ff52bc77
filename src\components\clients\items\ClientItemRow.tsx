
import { ClientItem } from "@/services/clientItemService";
import { ClientItemWithQuantity } from "@/components/orders/OrderFormTypes";
import { TableCell, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Plus, Minus } from "lucide-react";
import { cn } from "@/lib/utils";
import { AndroidSelectionButton } from "@/components/ui/android-selection-button";

interface ClientItemRowProps {
  item: ClientItem;
  selectedItem: ClientItemWithQuantity | undefined;
  isSelected: boolean;
  onToggle: (item: ClientItem, isSelected: boolean) => void;
  onQuantityChange: (itemId: string, newQuantity: number) => void;
  unitPrice: number;
}

export function ClientItemRow({
  item,
  selectedItem,
  isSelected,
  onToggle,
  onQuantityChange,
  unitPrice
}: ClientItemRowProps) {
  console.log("ClientItemRow rendering:", {
    itemId: item.id,
    itemName: item.name,
    selectedItem,
    isSelected,
    unitPrice
  });

  // Use the selectedItem quantity or default to 1
  const quantity = selectedItem?.quantity || 1;
  // Always use the constant unitPrice for consistency
  const total = unitPrice * quantity;

  console.log("ClientItemRow calculated values:", { quantity, total });

  return (
    <TableRow key={item.id}>
      <TableCell>
        <AndroidSelectionButton
          selected={isSelected}
          onToggle={() => {
            console.log("ClientItemRow: AndroidSelectionButton toggled:", { itemId: item.id, currentState: isSelected });
            onToggle(item, !isSelected);
          }}
          label={isSelected ? `Deselect ${item.name}` : `Select ${item.name}`}
        />
      </TableCell>
      <TableCell>{item.name}</TableCell>
      <TableCell>
        {isSelected && (
          <div className="flex items-center gap-2">
            <Button
              type="button"
              size="sm"
              variant="outline"
              className="android-touch-button w-12 p-0"
              onClick={() => {
                console.log("ClientItemRow: Decrease quantity for", item.id);
                onQuantityChange(item.id, quantity - 1);
              }}
              disabled={quantity <= 1}
            >
              <Minus className="h-5 w-5" />
            </Button>
            <Input
              className="h-12 w-20 text-center text-base touch-input"
              type="number"
              value={quantity}
              onChange={(e) => {
                const newValue = parseInt(e.target.value) || 1;
                console.log("ClientItemRow: Input quantity changed for", item.id, "to", newValue);
                onQuantityChange(item.id, newValue);
              }}
              min="1"
            />
            <Button
              type="button"
              size="sm"
              variant="outline"
              className="android-touch-button w-12 p-0"
              onClick={() => {
                console.log("ClientItemRow: Increase quantity for", item.id);
                onQuantityChange(item.id, quantity + 1);
              }}
            >
              <Plus className="h-5 w-5" />
            </Button>
          </div>
        )}
      </TableCell>
      <TableCell className="text-right">
        {isSelected && (
          <span>₱ {total.toFixed(2)}</span>
        )}
      </TableCell>
    </TableRow>
  );
}
