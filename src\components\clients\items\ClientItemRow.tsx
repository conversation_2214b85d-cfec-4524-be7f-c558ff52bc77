
import { ClientItem } from "@/services/clientItemService";
import { ClientItemWithQuantity } from "@/components/orders/OrderFormTypes";
import { TableCell, TableRow } from "@/components/ui/table";
import { ToggleSelectionButton } from "@/components/ui/toggle-selection-button";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Plus, Minus } from "lucide-react";

interface ClientItemRowProps {
  item: ClientItem;
  selectedItem: ClientItemWithQuantity | undefined;
  isSelected: boolean;
  onToggle: (item: ClientItem, isSelected: boolean) => void;
  onQuantityChange: (itemId: string, newQuantity: number) => void;
  unitPrice: number;
}

export function ClientItemRow({
  item,
  selectedItem,
  isSelected,
  onToggle,
  onQuantityChange,
  unitPrice
}: ClientItemRowProps) {
  console.log("ClientItemRow rendering:", {
    itemId: item.id,
    itemName: item.name,
    selectedItem,
    isSelected,
    unitPrice
  });

  // Use the selectedItem quantity or default to 1
  const quantity = selectedItem?.quantity || 1;
  // Always use the constant unitPrice for consistency
  const total = unitPrice * quantity;

  console.log("ClientItemRow calculated values:", { quantity, total });

  return (
    <div className="mb-2">
      <ToggleSelectionButton
        selected={isSelected}
        onToggle={() => {
          console.log("ClientItemRow: ToggleSelectionButton toggled:", { itemId: item.id, currentState: isSelected });
          onToggle(item, !isSelected);
        }}
      >
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <div className="font-medium text-base">{item.name}</div>
            {isSelected && (
              <div className="text-sm text-muted-foreground mt-1">
                ₱{unitPrice.toFixed(2)} per item
              </div>
            )}
          </div>

          {isSelected && (
            <div className="flex items-center space-x-2 ml-4">
              <button
                type="button"
                className="h-8 w-8 border border-gray-300 rounded-md bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary touch-action-manipulation active:scale-95 transition-all duration-150 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
                onClick={(e) => {
                  e.stopPropagation();
                  console.log("ClientItemRow: Decrease quantity for", item.id);
                  onQuantityChange(item.id, quantity - 1);
                }}
                disabled={quantity <= 1}
              >
                <Minus className="h-4 w-4" />
              </button>
              <Input
                className="h-8 w-12 text-center text-sm touch-action-manipulation"
                type="number"
                value={quantity}
                onClick={(e) => e.stopPropagation()}
                onChange={(e) => {
                  const newValue = parseInt(e.target.value) || 1;
                  console.log("ClientItemRow: Input quantity changed for", item.id, "to", newValue);
                  onQuantityChange(item.id, newValue);
                }}
                min="1"
              />
              <button
                type="button"
                className="h-8 w-8 border border-gray-300 rounded-md bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary touch-action-manipulation active:scale-95 transition-all duration-150 flex items-center justify-center"
                onClick={(e) => {
                  e.stopPropagation();
                  console.log("ClientItemRow: Increase quantity for", item.id);
                  onQuantityChange(item.id, quantity + 1);
                }}
              >
                <Plus className="h-4 w-4" />
              </button>
              <div className="text-sm font-medium text-right min-w-[60px]">
                ₱{total.toFixed(2)}
              </div>
            </div>
          )}

          {!isSelected && (
            <div className="text-sm text-muted-foreground">
              Not selected
            </div>
          )}
        </div>
      </ToggleSelectionButton>
    </div>
  );
}
