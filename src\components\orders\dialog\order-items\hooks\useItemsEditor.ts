
import { useState } from "react";
import { LineItem, Order } from "@/types";
import { updateOrderItems } from "@/services/orders/mutations/updateOrderItems";
import { useToast } from "@/hooks/use-toast";
import { useOrder } from "@/contexts/OrderContext";

export function useItemsEditor(order: Order, lineItems: LineItem[]) {
  const [isEditing, setIsEditing] = useState(false);
  const [editableItems, setEditableItems] = useState<LineItem[]>([]);
  const { refreshOrder } = useOrder();
  const { toast } = useToast();

  // Initialize editable items when starting edit mode
  const handleStartEdit = () => {
    setEditableItems([...lineItems]);
    setIsEditing(true);
  };

  // Handle item change when editing
  const handleItemChange = (updatedItem: LineItem, index: number) => {
    const newItems = [...editableItems];
    newItems[index] = updatedItem;
    setEditableItems(newItems);
  };

  // Add a new blank item
  const handleAddItem = () => {
    setEditableItems([
      ...editableItems,
      {
        id: `temp-${Date.now()}`,
        name: "",
        quantity: 1,
        unitPrice: 0,
        total: 0
      }
    ]);
  };

  // Remove an item
  const handleRemoveItem = (index: number) => {
    const newItems = [...editableItems];
    newItems.splice(index, 1);
    setEditableItems(newItems);
  };

  // Save changes
  const handleSaveChanges = async () => {
    try {
      // Filter out empty items
      const validItems = editableItems.filter(item => item.name && item.quantity > 0);
      const result = await updateOrderItems(order, validItems);
      
      if (!result.success) {
        throw new Error("Failed to update order items");
      }
      
      toast({
        title: "Success",
        description: "Order items have been updated"
      });

      // Refresh order data
      await refreshOrder();

      // Exit edit mode
      setIsEditing(false);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update order items",
        variant: "destructive"
      });
      console.error("Failed to save item changes:", error);
    }
  };

  // Cancel editing
  const handleCancelEdit = () => {
    setIsEditing(false);
  };

  return {
    isEditing,
    editableItems,
    handleStartEdit,
    handleItemChange,
    handleAddItem,
    handleRemoveItem,
    handleSaveChanges,
    handleCancelEdit
  };
}
