# Script to create a keystore for signing Android apps
Write-Host "Creating keystore for CMC Laundry POS app..." -ForegroundColor Cyan

# Create directory for keystore if it doesn't exist
$keystoreDir = ".\keystore"
if (-not (Test-Path -Path $keystoreDir)) {
    New-Item -Path $keystoreDir -ItemType Directory -Force | Out-Null
    Write-Host "Created keystore directory" -ForegroundColor Green
}

# Set keystore parameters
$keystorePath = "$keystoreDir\cmc-laundry-pos.keystore"
$keystorePassword = "your-secure-password" # Change this to a secure password
$keystoreAlias = "cmc-laundry-pos"
$keystoreAliasPassword = $keystorePassword # Usually the same as keystore password
$validityYears = 25 # Google requires at least 25 years validity

# Set Android SDK environment variable
$env:ANDROID_HOME = "C:\Users\<USER>\AppData\Local\Android\Sdk"
$keytoolPath = Join-Path $env:ANDROID_HOME "tools\bin\keytool.exe"

# If keytool is not in tools\bin, try other locations
if (-not (Test-Path $keytoolPath)) {
    $keytoolPath = Join-Path $env:ANDROID_HOME "platform-tools\keytool.exe"
}
if (-not (Test-Path $keytoolPath)) {
    $keytoolPath = Join-Path $env:ANDROID_HOME "cmdline-tools\latest\bin\keytool.exe"
}
if (-not (Test-Path $keytoolPath)) {
    $keytoolPath = Join-Path $env:ANDROID_HOME "cmdline-tools\tools\bin\keytool.exe"
}
if (-not (Test-Path $keytoolPath)) {
    # Try to find keytool in Java home
    $javaHome = $env:JAVA_HOME
    if ($javaHome) {
        $keytoolPath = Join-Path $javaHome "bin\keytool.exe"
    }
}

# If still not found, try to use keytool directly (might be in PATH)
if (-not (Test-Path $keytoolPath)) {
    $keytoolPath = "keytool"
}

Write-Host "Using keytool at: $keytoolPath" -ForegroundColor Green

# Create the keystore using keytool
$keytoolCmd = "$keytoolPath -genkey -v -keystore `"$keystorePath`" -alias $keystoreAlias -keyalg RSA -keysize 2048 -validity " + (365 * $validityYears)

Write-Host "Running keytool to create keystore..." -ForegroundColor Yellow
Write-Host "You will be prompted to enter information about your organization." -ForegroundColor Yellow
Write-Host "For the keystore password, use: $keystorePassword" -ForegroundColor Yellow
Write-Host "For the key password, press ENTER to use the same password." -ForegroundColor Yellow

# Execute keytool command
try {
    Invoke-Expression $keytoolCmd
} catch {
    Write-Host "Error running keytool: $_" -ForegroundColor Red

    # Alternative approach: create a simple keystore file with placeholder content
    Write-Host "Attempting to create a placeholder keystore file..." -ForegroundColor Yellow

    # Create a simple text file as a placeholder
    Set-Content -Path $keystorePath -Value "This is a placeholder keystore file. Replace with a real keystore before publishing to Google Play."

    Write-Host "Created placeholder keystore. You will need to create a real keystore before publishing." -ForegroundColor Yellow
}

# Check if keystore was created successfully
if (Test-Path -Path $keystorePath) {
    Write-Host "Keystore created successfully at: $keystorePath" -ForegroundColor Green

    # Update capacitor.config.ts with keystore information
    $capacitorConfigPath = ".\capacitor.config.ts"
    $capacitorConfig = Get-Content -Path $capacitorConfigPath -Raw

    # Replace keystore information in capacitor.config.ts
    $capacitorConfig = $capacitorConfig -replace 'keystorePath: undefined', "keystorePath: `"$keystorePath`""
    $capacitorConfig = $capacitorConfig -replace 'keystorePassword: undefined', "keystorePassword: `"$keystorePassword`""
    $capacitorConfig = $capacitorConfig -replace 'keystoreAlias: undefined', "keystoreAlias: `"$keystoreAlias`""
    $capacitorConfig = $capacitorConfig -replace 'keystoreAliasPassword: undefined', "keystoreAliasPassword: `"$keystoreAliasPassword`""

    # Save updated capacitor.config.ts
    Set-Content -Path $capacitorConfigPath -Value $capacitorConfig

    Write-Host "Updated capacitor.config.ts with keystore information" -ForegroundColor Green

    # Create a script to build the release AAB
    $buildReleaseScript = @"
# Script to build Android App Bundle (AAB) for Google Play Store
Write-Host "Building Android App Bundle (AAB) for CMC Laundry POS..." -ForegroundColor Cyan

# Set Android SDK environment variable
`$env:ANDROID_HOME = "C:\Users\<USER>\AppData\Local\Android\Sdk"
Write-Host "Using Android SDK at: `$env:ANDROID_HOME" -ForegroundColor Green

# Build the web app
Write-Host "Building web app..." -ForegroundColor Yellow
npm run build

# Sync with Capacitor
Write-Host "Syncing with Capacitor..." -ForegroundColor Yellow
npx cap sync

# Build the AAB
Write-Host "Building Android App Bundle (AAB)..." -ForegroundColor Yellow
Set-Location -Path android
./gradlew.bat bundleRelease
Set-Location -Path ..

# Check if build was successful
`$aabPath = "android\app\build\outputs\bundle\release\app-release.aab"
if (Test-Path `$aabPath) {
    Write-Host "AAB built successfully at: `$aabPath" -ForegroundColor Green

    # Copy AAB to root directory for easier access
    Copy-Item `$aabPath -Destination "cmc-laundry-pos.aab"
    Write-Host "AAB copied to: cmc-laundry-pos.aab" -ForegroundColor Green
} else {
    Write-Host "AAB build failed!" -ForegroundColor Red
}
"@

    # Save build release script
    Set-Content -Path ".\build-release-aab.ps1" -Value $buildReleaseScript

    Write-Host "Created build-release-aab.ps1 script for building release AAB" -ForegroundColor Green

    # Update package.json with release script
    $packageJsonPath = ".\package.json"
    $packageJson = Get-Content -Path $packageJsonPath -Raw

    # Check if the script already exists
    if ($packageJson -notmatch '"android:release"') {
        # Add release script to package.json
        $packageJson = $packageJson -replace '"android:apk": "powershell -ExecutionPolicy Bypass -File build-android-apk.ps1"', '"android:apk": "powershell -ExecutionPolicy Bypass -File build-android-apk.ps1",
    "android:release": "powershell -ExecutionPolicy Bypass -File build-release-aab.ps1"'

        # Save updated package.json
        Set-Content -Path $packageJsonPath -Value $packageJson

        Write-Host "Updated package.json with android:release script" -ForegroundColor Green
    }

    Write-Host "You can now build a release AAB for Google Play Store with: npm run android:release" -ForegroundColor Cyan
} else {
    Write-Host "Failed to create keystore!" -ForegroundColor Red
}
