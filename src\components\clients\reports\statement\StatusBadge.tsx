
import { Badge } from "@/components/ui/badge";

type PaidStatus = 'paid' | 'partial' | 'unpaid';

interface StatusBadgeProps {
  status: string;
  paidStatus: PaidStatus;
  isOverdue?: boolean;
}

export function StatusBadge({ status, paidStatus, isOverdue }: StatusBadgeProps) {
  // Determine badge properties based on status
  let variant:
    | "default"
    | "destructive"
    | "outline"
    | "secondary"
    | "success" = "secondary";
  let label = status;
  
  // Override with payment status
  if (paidStatus === 'paid') {
    variant = "success";
    label = "Paid";
  } else if (paidStatus === 'partial') {
    variant = "secondary";
    label = "Partial";
  } else if (isOverdue) {
    variant = "destructive";
    label = "Overdue";
  } else {
    variant = "outline";
    label = "Unpaid";
  }
  
  return (
    <Badge variant={variant} className="capitalize">
      {label}
    </Badge>
  );
}
