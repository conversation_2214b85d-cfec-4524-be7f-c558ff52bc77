
import { supabase } from "@/integrations/supabase/client";
import { CustomerType } from "@/types";

/**
 * Fetches client data based on client ID
 */
export async function fetchClientData(clientId: string): Promise<{
  name: string;
  contactPerson: string;
  phone: string;
  prefix: string | null;
}> {
  const { data: clientDetails } = await supabase
    .from('clients')
    .select('name, contact_person, phone, prefix')
    .eq('id', clientId)
    .single();
    
  if (clientDetails) {
    return {
      name: clientDetails.name,
      contactPerson: clientDetails.contact_person || '',
      phone: clientDetails.phone || '',
      prefix: clientDetails.prefix
    };
  }
  
  return {
    name: '',
    contactPerson: '',
    phone: '',
    prefix: null
  };
}

/**
 * Gets appropriate client ID based on order type
 */
export async function resolveClientId(customerType: CustomerType, providedClientId?: string): Promise<string | null> {
  // For client orders, use provided client ID
  if (customerType === 'client') {
    if (!providedClientId) {
      throw new Error("Client ID is required for client orders");
    }
    return providedClientId;
  } 
  
  // For walk-in orders, find or create a generic walk-in client
  const { data: walkInClient } = await supabase
    .from('clients')
    .select('id')
    .eq('name', 'Walk-in Customers')
    .limit(1);

  if (walkInClient && walkInClient.length > 0) {
    return walkInClient[0].id;
  } 
  
  // Fallback to any client if no dedicated walk-in client exists
  const { data: fallbackClient } = await supabase
    .from('clients')
    .select('id')
    .limit(1);
    
  if (fallbackClient && fallbackClient.length > 0) {
    return fallbackClient[0].id;
  }
  
  throw new Error("No valid client found for walk-in order");
}
