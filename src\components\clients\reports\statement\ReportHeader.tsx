
import { <PERSON><PERSON><PERSON><PERSON>, CardTitle, CardDescription } from '@/components/ui/card';
import { ReportActions } from './ReportActions';
import { ReportFilters } from './ReportFilters';
import { DateRange } from 'react-day-picker';

interface ReportHeaderProps {
  isPrinting: boolean;
  handlePrint: () => void;
  handleExportCSV: () => void;
  dateRange: DateRange;
  updateDateFilter: (range: DateRange) => void;
  overdueFilter: boolean | null;
  updateOverdueFilter: (value: boolean | null) => void;
  datePresets: {
    label: string;
    getValue: () => {
      from: Date;
      to: Date;
    };
  }[];
}

export function ReportHeader({
  isPrinting,
  handlePrint,
  handleExportCSV,
  dateRange,
  updateDateFilter,
  overdueFilter,
  updateOverdueFilter,
  datePresets
}: ReportHeaderProps) {
  return (
    <CardHeader className="pb-3 px-0">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <CardTitle>Statement of Account</CardTitle>
          <CardDescription>View and print your billing statement</CardDescription>
        </div>
        <div className="flex flex-col md:flex-row md:items-end gap-4">
          {/* Print and Export buttons */}
          <ReportActions 
            isPrinting={isPrinting} 
            handlePrint={handlePrint} 
            handleExportCSV={handleExportCSV} 
          />
          
          {/* Filters */}
          <ReportFilters
            dateRange={dateRange}
            updateDateFilter={updateDateFilter}
            overdueFilter={overdueFilter}
            updateOverdueFilter={updateOverdueFilter}
            datePresets={datePresets}
          />
        </div>
      </div>
    </CardHeader>
  );
}
