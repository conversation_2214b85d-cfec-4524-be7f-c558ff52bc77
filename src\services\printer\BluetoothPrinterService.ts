
import { bluetoothPrinterCore } from './BluetoothPrinterCore';
import { receiptFormatter } from './receiptFormatter';
import { PrintOrderData, PrinterStatus } from './types';
import { useToast } from "@/hooks/use-toast";
import { useState, useEffect } from 'react';

// Export necessary types from our service
export type { PrinterStatus };

class BluetoothPrinterService {
  // Pass-through methods from the core
  public isSupported = (): boolean => {
    return bluetoothPrinterCore.isSupported();
  };
  
  public getStatus = (): PrinterStatus => {
    return bluetoothPrinterCore.getStatus();
  };
  
  public onStatusChange = (callback: (status: PrinterStatus) => void): (() => void) => {
    return bluetoothPrinterCore.onStatusChange(callback);
  };
  
  public connectPrinter = async (): Promise<boolean> => {
    return await bluetoothPrinterCore.connectPrinter();
  };
  
  public disconnect = async (): Promise<void> => {
    await bluetoothPrinterCore.disconnect();
  };
  
  public print = async (data: string): Promise<boolean> => {
    return await bluetoothPrinterCore.print(data);
  };
  
  // Format and print order receipt
  public async printReceipt(orderData: PrintOrderData): Promise<boolean> {
    try {
      const receiptContent = receiptFormatter.formatReceipt(orderData);
      
      // Print the receipt
      return await this.print(receiptContent);
    } catch (error) {
      console.error('Error formatting receipt for printing:', error);
      return false;
    }
  }

  // Format and print job order for staff
  public async printJobOrder(orderData: PrintOrderData): Promise<boolean> {
    try {
      const jobOrderContent = receiptFormatter.formatJobOrder(orderData);
      
      // Print the job order
      return await this.print(jobOrderContent);
    } catch (error) {
      console.error('Error formatting job order for printing:', error);
      return false;
    }
  }
}

// Create a singleton instance
export const bluetoothPrinterService = new BluetoothPrinterService();

// React hook for using the printer service
export function usePrinter() {
  const [printerStatus, setPrinterStatus] = useState<PrinterStatus>('disconnected');
  const { toast } = useToast();
  
  useEffect(() => {
    // Set initial status
    setPrinterStatus(bluetoothPrinterService.getStatus());
    
    // Subscribe to status changes
    const unsubscribe = bluetoothPrinterService.onStatusChange((status) => {
      setPrinterStatus(status);
    });
    
    return () => {
      unsubscribe();
    };
  }, []);
  
  const connectPrinter = async () => {
    try {
      if (!bluetoothPrinterService.isSupported()) {
        toast({
          title: "Not Supported",
          description: "Bluetooth is not supported in this browser",
          variant: "destructive"
        });
        return false;
      }
      
      const connected = await bluetoothPrinterService.connectPrinter();
      
      if (connected) {
        toast({
          title: "Printer Connected",
          description: "Successfully connected to thermal printer"
        });
      } else {
        toast({
          title: "Connection Failed",
          description: "Could not connect to thermal printer",
          variant: "destructive"
        });
      }
      
      return connected;
    } catch (error) {
      console.error('Error connecting to printer:', error);
      toast({
        title: "Connection Failed",
        description: "Could not connect to thermal printer",
        variant: "destructive"
      });
      return false;
    }
  };
  
  const disconnectPrinter = async () => {
    await bluetoothPrinterService.disconnect();
    toast({
      title: "Printer Disconnected",
      description: "Printer has been disconnected"
    });
  };
  
  const printReceipt = async (orderData: any) => {
    if (printerStatus !== 'connected') {
      toast({
        title: "Printer Not Connected",
        description: "Please connect a thermal printer first",
        variant: "destructive"
      });
      return false;
    }
    
    const success = await bluetoothPrinterService.printReceipt(orderData);
    
    if (success) {
      toast({
        title: "Print Success",
        description: "Receipt has been sent to the printer"
      });
    } else {
      toast({
        title: "Print Failed",
        description: "Failed to print receipt",
        variant: "destructive"
      });
    }
    
    return success;
  };

  const printJobOrder = async (orderData: any) => {
    if (printerStatus !== 'connected') {
      toast({
        title: "Printer Not Connected",
        description: "Please connect a thermal printer first",
        variant: "destructive"
      });
      return false;
    }
    
    const success = await bluetoothPrinterService.printJobOrder(orderData);
    
    if (success) {
      toast({
        title: "Print Success",
        description: "Job order has been sent to the printer"
      });
    } else {
      toast({
        title: "Print Failed",
        description: "Failed to print job order",
        variant: "destructive"
      });
    }
    
    return success;
  };
  
  return {
    isSupported: bluetoothPrinterService.isSupported(),
    printerStatus,
    connectPrinter,
    disconnectPrinter,
    printReceipt,
    printJobOrder
  };
}
