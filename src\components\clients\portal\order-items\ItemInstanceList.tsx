import React from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Plus, Minus, ChevronDown, ChevronUp, Copy, Trash2 } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { ItemTreatmentOptions, type ItemTreatments } from './ItemTreatmentOptions';
import { ClientOrderItem } from '../order-form/types';

interface ItemInstanceListProps {
  items: ClientOrderItem[];
  type: string;
  expandedItems: Record<string, boolean>;
  onRemoveItem: (instanceId: string) => void;
  onDuplicateItem: (instanceId: string) => void;
  onToggleExpand: (instanceId: string) => void;
  onQuantityChange: (instanceId: string, newQuantity: number) => void;
  onTreatmentChange: (instanceId: string, treatments: ItemTreatments) => void;
}

export function ItemInstanceList({ 
  items, 
  type, 
  expandedItems,
  onRemoveItem,
  onDuplicateItem,
  onToggleExpand,
  onQuantityChange,
  onTreatmentChange
}: ItemInstanceListProps) {
  if (items.length === 0) {
    return <p className="text-sm text-muted-foreground">No items selected in this category.</p>;
  }

  const handleQuantityChange = (item: ClientOrderItem, event: React.ChangeEvent<HTMLInputElement>) => {
    const newQuantity = parseInt(event.target.value);
    if (!isNaN(newQuantity)) {
      onQuantityChange(item.instanceId!, newQuantity);
    }
  };

  // When initializing the treatments object, ensure all required properties are provided
  const handleExpandClick = (item: ClientOrderItem) => {
    onToggleExpand(item.instanceId!);
    
    // If the item doesn't have treatments yet, initialize with default values
    if (!item.treatments) {
      onTreatmentChange(item.instanceId!, {
        useStainRemoval: false,
        detergentType: 'none' as const,
        conditionerType: 'none' as const
      });
    }
  };

  return (
    <div className="space-y-2">
      {items.map(item => (
        <div key={item.instanceId} className="border rounded-md p-2 bg-white">
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <span className="font-medium">{item.name}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Button 
                variant="ghost" 
                size="icon" 
                onClick={() => onRemoveItem(item.instanceId!)}
                aria-label="Remove item"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
              <Button 
                variant="ghost" 
                size="icon" 
                onClick={() => onDuplicateItem(item.instanceId!)}
                aria-label="Duplicate item"
              >
                <Copy className="h-4 w-4" />
              </Button>
              <Button 
                variant="ghost" 
                size="icon" 
                onClick={() => handleExpandClick(item)}
                aria-expanded={expandedItems[item.instanceId!]}
                aria-label="Toggle item details"
              >
                {expandedItems[item.instanceId!] ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
              </Button>
            </div>
          </div>

          {expandedItems[item.instanceId!] && (
            <div className="mt-2 space-y-2">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <Button 
                    variant="outline" 
                    size="icon"
                    onClick={() => onQuantityChange(item.instanceId!, item.quantity - 1)}
                    disabled={item.quantity <= 1}
                    aria-label="Decrease quantity"
                  >
                    <Minus className="h-4 w-4" />
                  </Button>
                  <Input
                    type="number"
                    className="w-16 text-center"
                    value={item.quantity}
                    onChange={(e) => handleQuantityChange(item, e)}
                    min="1"
                    aria-label="Item quantity"
                  />
                  <Button 
                    variant="outline" 
                    size="icon"
                    onClick={() => onQuantityChange(item.instanceId!, item.quantity + 1)}
                    aria-label="Increase quantity"
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <ItemTreatmentOptions 
                itemId={item.instanceId!}
                itemName={item.name}
                initialTreatments={item.treatments}
                onTreatmentChange={(instanceId, treatments) => onTreatmentChange(instanceId, treatments)}
              />
            </div>
          )}
        </div>
      ))}
    </div>
  );
}
