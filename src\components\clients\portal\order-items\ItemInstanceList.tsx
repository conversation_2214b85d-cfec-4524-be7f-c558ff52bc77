
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ChevronDown, ChevronUp, Trash, Copy, Plus, Minus } from "lucide-react";
import { formatCurrency } from "@/lib/utils";
import { ClientOrderItem } from "../order-form/types";
import { ItemTreatmentOptions, ItemTreatments } from "./ItemTreatmentOptions";

interface ItemInstanceListProps {
  items: ClientOrderItem[];
  expandedItems: Record<string, boolean>;
  onRemove: (instanceId: string) => void;
  onDuplicate: (instanceId: string) => void;
  onToggleExpand: (instanceId: string) => void;
  onQuantityChange: (instanceId: string, quantity: number) => void;
  onTreatmentChange: (instanceId: string, treatments: ItemTreatments) => void;
}

export function ItemInstanceList({
  items,
  expandedItems,
  onRemove,
  onDuplicate,
  onToggleExpand,
  onQuantityChange,
  onTreatmentChange
}: ItemInstanceListProps) {
  return (
    <>
      {items.map((item) => (
        <div key={item.instanceId} className="border rounded-lg p-3 bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="font-medium">{item.name}</span>
              <span className="text-sm text-muted-foreground">
                ({formatCurrency(item.unitPrice || 0)})
              </span>
            </div>
            
            <div className="flex items-center gap-2">
              <div className="flex items-center bg-background rounded-md border shadow-sm">
                <Button 
                  type="button"
                  variant="ghost" 
                  size="icon"
                  className="h-8 w-8 rounded-l-md"
                  onClick={() => onQuantityChange(item.instanceId, Math.max(1, (item.quantity || 1) - 1))}
                >
                  <Minus className="h-3 w-3" />
                </Button>
                
                <Input
                  className="h-8 w-12 text-center border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
                  value={item.quantity || 1}
                  onChange={(e) => {
                    const val = parseInt(e.target.value);
                    if (!isNaN(val) && val > 0) {
                      onQuantityChange(item.instanceId, val);
                    }
                  }}
                />
                
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 rounded-r-md"
                  onClick={() => onQuantityChange(item.instanceId, (item.quantity || 1) + 1)}
                >
                  <Plus className="h-3 w-3" />
                </Button>
              </div>
              
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => onDuplicate(item.instanceId)}
              >
                <Copy className="h-3.5 w-3.5" />
              </Button>
              
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => onRemove(item.instanceId)}
              >
                <Trash className="h-3.5 w-3.5" />
              </Button>
              
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => onToggleExpand(item.instanceId)}
              >
                {expandedItems[item.instanceId] ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
          
          {expandedItems[item.instanceId] && (
            <div className="mt-3 pt-3 border-t">
              <ItemTreatmentOptions
                itemId={item.instanceId}
                itemName={item.name}
                initialTreatments={item.treatments || {
                  useStainRemoval: false,
                  detergentType: 'none',
                  conditionerType: 'none'
                }}
                onTreatmentChange={(itemId, treatments) => onTreatmentChange(itemId, treatments)}
              />
            </div>
          )}
        </div>
      ))}
    </>
  );
}
