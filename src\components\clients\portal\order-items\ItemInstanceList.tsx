
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Plus, Minus, ChevronDown, ChevronUp, Copy, Trash2 } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { ItemTreatmentOptions, type ItemTreatments } from './ItemTreatmentOptions';
import { ClientOrderItem } from '../order-form/types';
import { Badge } from '@/components/ui/badge';

interface ItemInstanceListProps {
  items: ClientOrderItem[];
  type: string;
  expandedItems: Record<string, boolean>;
  onRemoveItem: (instanceId: string) => void;
  onDuplicateItem: (instanceId: string) => void;
  onToggleExpand: (instanceId: string) => void;
  onQuantityChange: (instanceId: string, newQuantity: number) => void;
  onTreatmentChange: (instanceId: string, treatments: ItemTreatments) => void;
}

export function ItemInstanceList({
  items,
  type,
  expandedItems,
  onRemoveItem,
  onDuplicateItem,
  onToggleExpand,
  onQuantityChange,
  onTreatmentChange
}: ItemInstanceListProps) {
  if (items.length === 0) {
    return <p className="text-sm text-muted-foreground">No items selected in this category.</p>;
  }

  const handleQuantityChange = (item: ClientOrderItem, event: React.ChangeEvent<HTMLInputElement>) => {
    const newQuantity = parseInt(event.target.value);
    if (!isNaN(newQuantity)) {
      onQuantityChange(item.instanceId!, newQuantity);
    }
  };

  // When initializing the treatments object, ensure all required properties are provided
  const handleExpandClick = (item: ClientOrderItem) => {
    onToggleExpand(item.instanceId!);

    // If the item doesn't have treatments yet, initialize with default values
    if (!item.treatments) {
      onTreatmentChange(item.instanceId!, {
        useStainRemoval: false,
        useBeachTreatment: false,
        detergentType: 'none' as const,
        conditionerType: 'none' as const
      });
    }
  };

  // Format treatments to display as a badge
  const getTreatmentSummary = (treatments?: ItemTreatments) => {
    if (!treatments) return null;

    const treatmentOptions = [];

    if (treatments.useStainRemoval) treatmentOptions.push('Stain Removal');
    if (treatments.useBeachTreatment) treatmentOptions.push('Beach Treatment');
    if (treatments.detergentType !== 'none') treatmentOptions.push(`${treatments.detergentType} Detergent`);
    if (treatments.conditionerType !== 'none') treatmentOptions.push(`${treatments.conditionerType} Conditioner`);

    return treatmentOptions.length > 0 ? treatmentOptions.join(', ') : null;
  };

  return (
    <div className="space-y-2">
      {items.map(item => (
        <div key={item.instanceId} className="border rounded-md p-2 bg-white">
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <span className="font-medium">{item.name}</span>
              {item.treatments && getTreatmentSummary(item.treatments) && (
                <Badge variant="outline" className="ml-2 text-xs">
                  {getTreatmentSummary(item.treatments)}
                </Badge>
              )}
            </div>
            <div className="flex items-center space-x-2">
              <button
                type="button"
                className="h-10 w-10 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary touch-action-manipulation active:scale-95 transition-all duration-150 flex items-center justify-center"
                onClick={() => onRemoveItem(item.instanceId!)}
                aria-label="Remove item"
              >
                <Trash2 className="h-5 w-5 text-red-500" />
              </button>
              <button
                type="button"
                className="h-10 w-10 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary touch-action-manipulation active:scale-95 transition-all duration-150 flex items-center justify-center"
                onClick={() => onDuplicateItem(item.instanceId!)}
                aria-label="Duplicate item"
                title="Duplicate item - useful for adding same item with different treatments"
              >
                <Copy className="h-5 w-5 text-blue-500" />
              </button>
              <button
                type="button"
                className="h-10 w-10 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary touch-action-manipulation active:scale-95 transition-all duration-150 flex items-center justify-center"
                onClick={() => handleExpandClick(item)}
                aria-expanded={expandedItems[item.instanceId!]}
                aria-label="Toggle item details"
              >
                {expandedItems[item.instanceId!] ? <ChevronUp className="h-5 w-5" /> : <ChevronDown className="h-5 w-5" />}
              </button>
            </div>
          </div>

          {expandedItems[item.instanceId!] && (
            <div className="mt-2 space-y-2">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <button
                    type="button"
                    className="h-12 w-12 border border-gray-300 rounded-md bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary touch-action-manipulation active:scale-95 transition-all duration-150 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
                    onClick={() => onQuantityChange(item.instanceId!, Math.max(1, (item.quantity || 1) - 1))}
                    disabled={(item.quantity || 1) <= 1}
                    aria-label="Decrease quantity"
                  >
                    <Minus className="h-5 w-5" />
                  </button>
                  <Input
                    type="number"
                    className="w-20 h-12 text-center text-base touch-action-manipulation"
                    value={item.quantity || 1}
                    onChange={(e) => handleQuantityChange(item, e)}
                    min="1"
                    aria-label="Item quantity"
                  />
                  <button
                    type="button"
                    className="h-12 w-12 border border-gray-300 rounded-md bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary touch-action-manipulation active:scale-95 transition-all duration-150 flex items-center justify-center"
                    onClick={() => onQuantityChange(item.instanceId!, (item.quantity || 1) + 1)}
                    aria-label="Increase quantity"
                  >
                    <Plus className="h-5 w-5" />
                  </button>
                </div>
              </div>

              <ItemTreatmentOptions
                itemId={item.instanceId!}
                itemName={item.name}
                initialTreatments={item.treatments}
                onTreatmentChange={(instanceId, treatments) => onTreatmentChange(instanceId, treatments)}
              />
            </div>
          )}
        </div>
      ))}
    </div>
  );
}
