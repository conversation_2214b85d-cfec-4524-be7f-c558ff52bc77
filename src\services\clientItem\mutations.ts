
import { supabase } from "@/integrations/supabase/client";
import { addToSyncQueue } from "@/services/SyncService";
import { toast } from "sonner";
import { ClientItem } from "./types";
import { addToClientItemsStore, removeFromClientItemsStore } from "./localStorage";
import { getClientItems } from "./queries";

// Function to add a client item
export const addClientItem = async (itemData: {
  client_id: string;
  name: string;
  unit_price?: number;
}): Promise<ClientItem | null> => {
  try {
    // First check if an item with the same name already exists for this client
    const existingItems = await getClientItems(itemData.client_id);
    const normalizedName = itemData.name.trim().toLowerCase();
    
    const duplicateItem = existingItems.find(
      item => item.name.toLowerCase() === normalizedName
    );
    
    if (duplicateItem) {
      toast.error(`"${itemData.name}" already exists for this client`);
      return null;
    }
    
    // Check authentication status
    const { data: sessionData } = await supabase.auth.getSession();
    const isAuthenticated = !!sessionData.session;
    
    // If client has a local ID or we're not authenticated, store locally
    const isLocalClient = itemData.client_id.startsWith('local-');
    const unitPrice = itemData.unit_price || 0;
    
    if (isLocalClient || !isAuthenticated) {
      console.log(`Adding client item locally for client ${itemData.client_id}`);
      
      const newItem: ClientItem = {
        ...itemData,
        id: `local-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        unit_price: unitPrice
      };
      
      // Add to local store
      addToClientItemsStore(newItem);
      
      // Only add to sync queue if we're authenticated but client is local
      if (isAuthenticated && isLocalClient) {
        // We'll implement this later when we tackle sync for client items
        console.log("Client item for local client will be synced when client is synced");
      }
      // Add to sync queue if not authenticated and client is not local
      else if (!isAuthenticated && !isLocalClient) {
        addToSyncQueue('client_items', 'create', {
          ...itemData,
          unit_price: unitPrice,
          item_type: 'default' // Add default item_type for backward compatibility
        });
      }
      
      return newItem;
    }
    
    // Try to add to Supabase
    const { data, error } = await supabase
      .from('client_items')
      .insert({
        client_id: itemData.client_id,
        name: itemData.name,
        unit_price: unitPrice,
        item_type: 'default' // Add default item_type for backward compatibility
      })
      .select('id, client_id, name, created_at, updated_at, unit_price')
      .single();
    
    if (error) {
      // Check if this is a duplicate error from the database
      if (error.code === '23505') { // PostgreSQL unique violation code
        toast.error(`"${itemData.name}" already exists for this client`);
        return null;
      }
      
      console.error("Error adding client item:", error);
      toast.error("Failed to save client item");
      
      // Fallback to local store
      const newItem: ClientItem = {
        ...itemData,
        id: `local-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        unit_price: unitPrice
      };
      
      addToClientItemsStore(newItem);
      return newItem;
    }
    
    toast.success("Client item added successfully");
    return data;
  } catch (err) {
    console.error("Error in addClientItem:", err);
    toast.error("An error occurred while adding client item");
    return null;
  }
};

// Function to delete a client item
export const deleteClientItem = async (itemId: string, clientId: string): Promise<boolean> => {
  try {
    // Check if this is a local item
    const isLocalItem = itemId.startsWith('local-');
    const isLocalClient = clientId.startsWith('local-');
    
    // Check authentication status
    const { data: sessionData } = await supabase.auth.getSession();
    const isAuthenticated = !!sessionData.session;
    
    // For local items or if not authenticated, remove from local store
    if (isLocalItem || isLocalClient || !isAuthenticated) {
      console.log(`Removing local client item ${itemId}`);
      removeFromClientItemsStore(itemId);
      
      // Add to sync queue if not local item and not authenticated
      if (!isLocalItem && !isAuthenticated) {
        addToSyncQueue('client_items', 'delete', { id: itemId });
      }
      
      toast.success("Client item removed successfully");
      return true;
    }
    
    // Try to delete from Supabase
    const { error } = await supabase
      .from('client_items')
      .delete()
      .eq('id', itemId);
    
    if (error) {
      console.error("Error deleting client item:", error);
      toast.error("Failed to delete client item");
      return false;
    }
    
    // Also remove from local store to keep in sync
    removeFromClientItemsStore(itemId);
    
    toast.success("Client item deleted successfully");
    return true;
  } catch (err) {
    console.error("Error in deleteClientItem:", err);
    toast.error("An error occurred while deleting client item");
    return false;
  }
};
