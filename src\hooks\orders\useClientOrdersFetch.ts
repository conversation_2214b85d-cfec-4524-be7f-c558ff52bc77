
import { useState, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Order, LineItem, CustomerType } from '@/types';
import { Json } from '@/integrations/supabase/types';
import { useToast } from '@/hooks/use-toast';
import { format } from 'date-fns';
import { fromDbNumber } from '@/utils/db-converters';

// Define interface matching the Supabase orders table structure
// Important: amount and paid_amount are actually numbers in the database,
// while detergent_quantity and conditioner_quantity are stored as strings
interface SupabaseOrder {
  id: string;
  client_id: string;
  created_at: string;
  delivery_date: string;
  items: Json;
  use_detergent: boolean;
  use_conditioner: boolean;
  use_stain_remover?: boolean;
  use_bleach?: boolean;
  amount: number;  // Number in database
  paid_amount: number;  // Number in database
  weight_kilos: number;
  number_of_pieces: number;
  reference_code?: string;
  status: string;
  customer_type: string;
  customer_name?: string;
  phone_number?: string;
  notes?: string;
  detergent_quantity?: string;  // String in database
  conditioner_quantity?: string;  // String in database
  service_type?: string;
  vat_amount?: number;
  subtotal_before_vat?: number;
}

export const useClientOrdersFetch = () => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const parseLineItems = (items: Json): LineItem[] => {
    if (!items) return [];
    
    try {
      const itemsArray = typeof items === 'string' 
        ? JSON.parse(items) 
        : Array.isArray(items) 
          ? items 
          : [items];
          
      if (Array.isArray(itemsArray)) {
        return itemsArray.map(item => ({
          id: String(item?.id || ''),
          name: String(item?.name || ''),
          quantity: Number(item?.quantity || 0),
          unitPrice: Number(item?.unitPrice || 0),
          total: Number(item?.total || 0)
        }));
      }
      return [];
    } catch (error) {
      console.error('Error parsing line items:', error);
      return [];
    }
  };

  const fetchOrders = useCallback(async (clientId: string) => {
    setIsLoading(true);
    try {
      const { data: sessionData } = await supabase.auth.getSession();
      if (!sessionData.session) {
        console.log('No active session, skipping order fetch');
        setIsLoading(false);
        return;
      }

      const { data: clientData } = await supabase
        .from('clients')
        .select('prefix, name')
        .eq('id', clientId)
        .single();
        
      const clientName = clientData?.name || 'Client';
      const clientPrefix = clientData?.prefix || '';
      
      const { data, error } = await supabase
        .from('orders')
        .select('*')
        .eq('client_id', clientId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      
      console.log("useClientOrders: Fetched orders:", data?.length);
      
      if (data) {
        const formattedOrders: Order[] = data.map((order: SupabaseOrder) => {
          // Format dates properly
          const orderDate = new Date(order.created_at);
          const deliveryDate = new Date(order.delivery_date);
          
          // Determine detergent and conditioner types based on boolean flags
          // since these fields don't exist in the database
          const detergentType = order.use_detergent ? 'regular' : 'none';
          const conditionerType = order.use_conditioner ? 'regular' : 'none';
          
          return {
            id: order.reference_code || order.id,
            uuid: order.id,
            orderDate: format(orderDate, 'MMM dd, yyyy'),
            deliveryDate: format(deliveryDate, 'MMM dd, yyyy'),
            customer: {
              name: clientName,
              phone: ''
            },
            amount: order.amount, // No need for fromDbNumber as it's already a number
            paidAmount: order.paid_amount, // No need for fromDbNumber as it's already a number
            status: order.status,
            clientId: order.client_id,
            useDetergent: order.use_detergent || false,
            useFabricConditioner: order.use_conditioner || false,
            useStainRemover: Boolean(order.use_stain_remover) || false,
            useBleach: Boolean(order.use_bleach) || false,
            detergentType: detergentType,
            conditionerType: conditionerType,
            detergentQuantity: fromDbNumber(order.detergent_quantity || '1'),
            conditionerQuantity: fromDbNumber(order.conditioner_quantity || '1'),
            lineItems: parseLineItems(order.items),
            customerType: 'client' as CustomerType,
            weightKilos: Number(order.weight_kilos || 0),
            numberOfPieces: Number(order.number_of_pieces || 0),
            prefix: clientPrefix || '',
            serviceType: String(order.service_type || ''),
            notes: order.notes || '',
            vatAmount: Number(order.vat_amount || 0),
            subtotalBeforeVAT: Number(order.subtotal_before_vat || 0)
          };
        });
        
        setOrders(formattedOrders);
      }
    } catch (error) {
      console.error('Error fetching orders:', error);
      toast({
        title: 'Error',
        description: 'Failed to load orders.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  return { orders, fetchOrders, isLoading };
};
