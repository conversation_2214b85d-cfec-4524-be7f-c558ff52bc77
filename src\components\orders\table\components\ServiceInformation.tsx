
import { Order } from "@/types";

interface ServiceInformationProps {
  order: Order;
  serviceItems: any[];
  formatCurrency: (value: number) => string;
  isClientOrder: boolean;
}

export function ServiceInformation({ order, serviceItems, formatCurrency, isClientOrder }: ServiceInformationProps) {
  return (
    <div>
      <h4 className="font-medium mb-2">
        {isClientOrder ? "Client Service Information" : "Walk-in Service Information"}
      </h4>
      <div className={`bg-background rounded border p-3 ${isClientOrder ? 'bg-blue-50' : 'bg-green-50'}`}>
        <div className="flex justify-between mb-2">
          <span className="font-medium">{order.serviceType.replace(/_/g, ' ').toUpperCase()} service</span>
          <span>{formatCurrency(order.subtotalBeforeVAT || order.amount)}</span>
        </div>
        
        {/* Display customer information for walk-in orders */}
        {!isClientOrder && (
          <div className="text-sm mb-2">
            <strong>Customer:</strong> {order.customer?.name} 
            {order.customer?.phone ? ` (${order.customer.phone})` : ''}
          </div>
        )}
        
        {/* Display weight/pieces information */}
        {(order.weightKilos || order.numberOfPieces) && (
          <div className="text-sm text-muted-foreground mb-2">
            {order.weightKilos ? `Weight: ${order.weightKilos} kg` : ''}
            {order.weightKilos && order.numberOfPieces ? ' · ' : ''}
            {order.numberOfPieces ? `Pieces: ${order.numberOfPieces}` : ''}
          </div>
        )}
        
        {/* Display service items if they exist */}
        {serviceItems && serviceItems.length > 0 && (
          <div className="mt-3 border-t pt-2">
            <p className="font-medium text-sm mb-1">Service Items:</p>
            <ul className="list-disc list-inside space-y-1 pl-2 text-sm">
              {serviceItems.map((item, idx) => (
                <li key={idx}>
                  {item.name || item.type} 
                  {item.quantity ? ` × ${item.quantity}` : ''} 
                  {item.unitPrice || item.price ? ` - ${formatCurrency(item.unitPrice || item.price)}` : ''}
                  {item.treatmentDescription && (
                    <span className="text-xs text-muted-foreground ml-1">
                      ({item.treatmentDescription})
                    </span>
                  )}
                </li>
              ))}
            </ul>
          </div>
        )}
        
        {/* Display total with VAT if applicable */}
        {order.vatAmount > 0 && (
          <div className="flex justify-between text-sm border-t pt-2 mt-2">
            <span>VAT:</span>
            <span>{formatCurrency(order.vatAmount)}</span>
          </div>
        )}
        
        {/* Display final total */}
        <div className="flex justify-between text-sm font-medium border-t pt-2 mt-2">
          <span>Total:</span>
          <span>{formatCurrency(order.amount)}</span>
        </div>
      </div>
    </div>
  );
}
