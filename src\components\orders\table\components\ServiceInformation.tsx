
import React from "react";
import { Order } from "@/types";
import { SERVICE_TYPES } from "../../pricing/constants";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

interface ServiceInformationProps {
  order: Order;
  serviceItems: any[];
  formatCurrency: (value: number) => string;
  isClientOrder: boolean;
}

export function ServiceInformation({ order, serviceItems, formatCurrency, isClientOrder }: ServiceInformationProps) {
  const getServiceTypeDisplay = (serviceType?: string) => {
    if (!serviceType) return "Standard Service";
    
    // Convert snake_case to Title Case
    return serviceType
      .replace(/_/g, ' ')
      .replace(/\w\S*/g, (txt) => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase());
  };
  
  const isDryCleaningService = order.serviceType === SERVICE_TYPES.DRY_CLEANING;
  
  // Get dry cleaning items from the order if they exist
  const dryCleaningItems = order.dryCleaningItems || [];
  const hasDryCleaningItems = dryCleaningItems && dryCleaningItems.length > 0;

  // Calculate total items and amount for dry cleaning
  const totalDryCleaningItems = hasDryCleaningItems ? 
    dryCleaningItems.reduce((sum, item) => sum + (item.quantity || 0), 0) : 0;
    
  const totalDryCleaningAmount = hasDryCleaningItems ?
    dryCleaningItems.reduce((sum, item) => sum + ((item.price || 0) * (item.quantity || 0)), 0) : 0;

  return (
    <div>
      <h4 className="font-medium mb-2">
        {isClientOrder ? "Client Service Information" : "Walk-in Service Information"}
      </h4>
      <div className={`bg-background rounded border p-3 ${isClientOrder ? 'bg-blue-50' : 'bg-green-50'} ${isDryCleaningService ? 'border-amber-300' : ''}`}>
        <div className="flex justify-between mb-2">
          <span className="font-medium">
            {getServiceTypeDisplay(order.serviceType)} 
            {isDryCleaningService && <span className="ml-1 text-amber-600">(Dry Cleaning)</span>}
          </span>
          <span>{formatCurrency(order.subtotalBeforeVAT || order.amount)}</span>
        </div>
        
        {/* Display customer information for walk-in orders */}
        {!isClientOrder && (
          <div className="text-sm mb-2">
            <strong>Customer:</strong> {order.customer?.name} 
            {order.customer?.phone ? ` (${order.customer.phone})` : ''}
          </div>
        )}
        
        {/* Display weight/pieces information - hide weight for client orders */}
        <div className="text-sm text-muted-foreground mb-2">
          {!isClientOrder && order.weightKilos ? `Weight: ${order.weightKilos} kg` : ''}
          {!isClientOrder && order.weightKilos && order.numberOfPieces ? ' · ' : ''}
          {order.numberOfPieces ? `Pieces: ${order.numberOfPieces}` : ''}
          {isDryCleaningService && totalDryCleaningItems > 0 && (
            <span className="ml-1 text-amber-600 font-medium">
              · {totalDryCleaningItems} dry cleaning items
            </span>
          )}
        </div>
        
        {/* Display dry cleaning items as a proper table if this is a dry cleaning order */}
        {isDryCleaningService && hasDryCleaningItems && (
          <div className="mt-3 border-t pt-2">
            <p className="font-medium text-sm mb-1">Dry Cleaning Items:</p>
            <Table className="w-full border-collapse text-sm">
              <TableHeader className="bg-amber-50/50">
                <TableRow>
                  <TableHead className="py-1 text-xs font-medium">Item</TableHead>
                  <TableHead className="py-1 text-xs font-medium text-center">Qty</TableHead>
                  <TableHead className="py-1 text-xs font-medium text-right">Price</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {dryCleaningItems.map((item, idx) => {
                  const displayName = item.type?.replace(/_/g, ' ')
                    .replace(/\w\S*/g, txt => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase());
                  
                  return (
                    <TableRow key={idx} className="border-b border-amber-100/30">
                      <TableCell className="py-1 text-xs">{displayName}</TableCell>
                      <TableCell className="py-1 text-xs text-center">{item.quantity || 1}</TableCell>
                      <TableCell className="py-1 text-xs text-right">
                        {formatCurrency((item.price || 0) * (item.quantity || 1))}
                      </TableCell>
                    </TableRow>
                  );
                })}
                <TableRow className="bg-amber-50">
                  <TableCell colSpan={2} className="py-1 text-xs font-medium text-right">
                    Dry Cleaning Total:
                  </TableCell>
                  <TableCell className="py-1 text-xs font-medium text-right">
                    {formatCurrency(totalDryCleaningAmount)}
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
        )}
        
        {/* Display service items if they exist and not dry cleaning */}
        {serviceItems && serviceItems.length > 0 && !isDryCleaningService && (
          <div className="mt-3 border-t pt-2">
            <p className="font-medium text-sm mb-1">Service Items:</p>
            <ul className="list-disc list-inside space-y-1 pl-2 text-sm">
              {serviceItems.map((item, idx) => (
                <li key={idx}>
                  {item.name || item.type} 
                  {item.quantity ? ` × ${item.quantity}` : ''} 
                  {item.unitPrice || item.price ? ` - ${formatCurrency(item.unitPrice || item.price)}` : ''}
                  {item.treatmentDescription && (
                    <span className="text-xs text-muted-foreground ml-1">
                      ({item.treatmentDescription})
                    </span>
                  )}
                </li>
              ))}
            </ul>
          </div>
        )}
        
        {/* Display total with VAT if applicable */}
        {order.vatAmount > 0 && (
          <div className="flex justify-between text-sm border-t pt-2 mt-2">
            <span>VAT:</span>
            <span>{formatCurrency(order.vatAmount)}</span>
          </div>
        )}
        
        {/* Display final total */}
        <div className="flex justify-between text-sm font-medium border-t pt-2 mt-2">
          <span>Total:</span>
          <span>{formatCurrency(order.amount)}</span>
        </div>
      </div>
    </div>
  );
}
