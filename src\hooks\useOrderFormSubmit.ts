import { useState } from "react";
import { UseFormReturn } from "react-hook-form";
import { OrderFormValues, ClientItemWithQuantity, PriceBreakdown } from "../components/orders/OrderFormTypes";
import { useToast } from "@/hooks/use-toast";
import { extractFormErrors, logFormErrors } from "../components/orders/client-order-form/utils/errorHandlingUtils";

export function useOrderFormSubmit(
  form: UseFormReturn<OrderFormValues>,
  priceBreakdown: PriceBreakdown,
  onSubmit: (data: OrderFormValues & { 
    orderAmount: number;
    vatAmount: number;
    subtotal: number;
  }) => void
) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const handleSubmit = async (data: OrderFormValues) => {
    console.log("=== FORM SUBMIT TRIGGERED ===");
    console.log("Form data:", data);
    console.log("Order type:", data.orderType);
    console.log("Client ID:", data.clientId);
    console.log("Selected client items:", data.selectedClientItems);
    console.log("Current form errors:", form.formState.errors);
    console.log("Price breakdown:", priceBreakdown);
    
    // Log all form fields for debugging
    Object.entries(data).forEach(([key, value]) => {
      console.log(`${key}:`, value);
    });
    
    // Prevent multiple submissions
    if (isSubmitting) {
      console.log("Submission already in progress. Ignoring this request.");
      return;
    }
    
    // Check form type and apply appropriate validation
    const orderType = data.orderType || "walk-in";
    
    // Get errors using our utility function - this will filter out client-specific errors for walk-in orders
    const { errorMessages, hasErrors } = extractFormErrors(form);
    
    // Log detailed information about the validation state
    console.log("Form validation:", {
      orderType,
      hasErrors,
      errorMessages,
      isValid: form.formState.isValid
    });
    
    // For walk-in orders, we ignore clientId-related errors
    const shouldBlock = hasErrors && (
      orderType === "client" || // For client orders, block on any error
      (orderType === "walk-in" && errorMessages.length > 0) // For walk-in, only block if there are non-filtered errors
    );
    
    if (shouldBlock) {
      console.log("Form has validation errors:", errorMessages);
      toast({
        title: "Validation Error",
        description: errorMessages[0]?.message || "Please fix validation errors before submitting",
        variant: "destructive"
      });
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Clear any previous errors
      form.clearErrors();
      
      // Basic validation - validate required fields
      const validationErrors: {[key: string]: string} = {};
      
      // Validate delivery date for all orders
      if (!data.deliveryDate) {
        console.error("Delivery date validation failed: No date selected");
        validationErrors.deliveryDate = "Please select a delivery date";
      } else if (typeof data.deliveryDate === 'string' && data.deliveryDate.trim() === "") {
        console.error("Delivery date validation failed: Empty date string");
        validationErrors.deliveryDate = "Please select a delivery date";
      }
      
      // Validate weight for walk-in orders with weight pricing
      if (orderType === "walk-in" && data.pricingMethod === "weight") {
        const weightKilos = typeof data.weightKilos === 'string' 
          ? parseFloat(data.weightKilos) 
          : Number(data.weightKilos || 0);
          
        if (isNaN(weightKilos) || weightKilos <= 0) {
          console.error("Weight validation failed: Invalid weight");
          validationErrors.weightKilos = "Please enter a valid weight";
        }
      }
      
      if (orderType === "client") {
        // Client order validation
        
        // Validate client selection
        if (!data.clientId || data.clientId.trim() === "") {
          console.error("Client validation failed: No client selected");
          validationErrors.clientId = "Please select a client";
        }
        
        // Validate item selection for client orders using client_item pricing
        if (data.pricingMethod === "client_item" && 
            (!data.selectedClientItems || data.selectedClientItems.length === 0)) {
          console.error("Client items validation failed: No items selected");
          validationErrors.selectedClientItems = "Please select at least one item";
        }
      } else {
        // Walk-in validation
        if (!data.customerName || data.customerName.trim() === "") {
          console.error("Customer name validation failed: No name entered");
          validationErrors.customerName = "Please enter a customer name";
        }
        
        if (!data.phoneNumber || data.phoneNumber.trim() === "") {
          console.error("Phone number validation failed: No phone entered");
          validationErrors.phoneNumber = "Please enter a phone number";
        }
      }
      
      // If validation errors exist, set them and exit
      if (Object.keys(validationErrors).length > 0) {
        console.error("Validation errors found:", validationErrors);
        
        // Set all errors
        Object.entries(validationErrors).forEach(([field, message]) => {
          form.setError(field as any, { 
            type: "manual", 
            message
          });
        });
        
        // Show toast with first error
        const firstError = Object.values(validationErrors)[0];
        toast({
          title: "Validation Error",
          description: firstError,
          variant: "destructive"
        });
        
        setIsSubmitting(false);
        return;
      }
      
      console.log("Form validated successfully, proceeding with submission");
      
      // Normalize client items to ensure all properties are defined
      const validatedClientItems: ClientItemWithQuantity[] = (data.selectedClientItems || [])
        .filter(item => item !== null && item !== undefined)
        .map(item => ({
          id: item.id || "",
          name: item.name || "",
          item_type: item.item_type || "standard", // Ensure item_type is present
          unitPrice: Number(item.unitPrice) || 50,
          quantity: Number(item.quantity) || 1,
          total: Number((item.unitPrice || 50) * (item.quantity || 1))
        }));
      
      console.log("Normalized client items:", validatedClientItems);
      
      // Ensure delivery date is in proper format YYYY-MM-DD
      let formattedDeliveryDate = data.deliveryDate;
      if (formattedDeliveryDate) {
        try {
          // Try to format the date if it's not already in the correct format
          const dateObj = new Date(formattedDeliveryDate);
          if (!isNaN(dateObj.getTime())) {
            formattedDeliveryDate = dateObj.toISOString().split('T')[0];
          }
        } catch (err) {
          console.error("Error formatting date:", err);
          // Keep the original value if there's an error
        }
      } else {
        // If no delivery date, use tomorrow as default
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        formattedDeliveryDate = tomorrow.toISOString().split('T')[0];
      }
      
      // Keep weightKilos as string to avoid type errors
      const submissionData = {
        ...data,
        deliveryDate: formattedDeliveryDate,
        selectedClientItems: validatedClientItems,
        orderAmount: priceBreakdown.totalPrice,
        vatAmount: priceBreakdown.vatAmount,
        subtotal: priceBreakdown.subtotal,
        // Make sure clientId is undefined for walk-in orders
        clientId: orderType === "walk-in" ? undefined : data.clientId,
        // Keep weightKilos as string to match expected type
        numberOfPieces: data.numberOfPieces
      };
      
      console.log("Submission data prepared:", submissionData);
      onSubmit(submissionData);
    } catch (error) {
      console.error("Error in form submission:", error);
      toast({
        title: "Error",
        description: "Failed to process form. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    handleSubmit,
    isSubmitting
  };
}
