import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { OrderStatusBadge } from "./OrderStatusBadge";
import { Order } from "@/types";
import { Trash2, Edit, X, Printer, FileText } from "lucide-react";
import { useAuth } from "@/contexts/auth";
import { DeleteOrderDialog } from "./DeleteOrderDialog";
import { EditOrderDialog } from "./EditOrderDialog";
import { usePrinterContext } from "@/contexts/PrinterContext";
import { useToast } from "@/hooks/use-toast";

interface ViewOrderDialogProps {
  order: Order;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onStatusChange?: (orderId: string, newStatus: string) => Promise<void>;
}

export function ViewOrderDialog({
  order,
  open,
  onO<PERSON><PERSON><PERSON><PERSON>,
  onStatusChange,
}: ViewOrderDialogProps) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isPrinting, setIsPrinting] = useState(false);
  const { userRole } = useAuth();
  const { printerStatus, printReceipt, printJobOrder, showPrinterConnect } = usePrinterContext();
  const { toast } = useToast();
  
  const isAdmin = userRole === 'admin';
  const canEdit = userRole === 'admin' || userRole === 'staff';
  const isStaff = userRole === 'staff' || userRole === 'admin';

  const handleStatusChange = async (newStatus: string) => {
    if (onStatusChange) {
      await onStatusChange(order.id, newStatus);
    }
  };

  const handleOrderDeleted = () => {
    // Close the view dialog when order is deleted
    onOpenChange(false);
    
    // Refresh the orders list
    window.dispatchEvent(new CustomEvent('order-deleted'));
  };
  
  const handleEditOrder = () => {
    setIsEditDialogOpen(true);
  };
  
  const handleOrderUpdated = () => {
    // Close the view dialog when order is updated
    window.dispatchEvent(new CustomEvent('order-status-updated'));
  };

  const handlePrintReceipt = async () => {
    const useBluetoothPrinter = localStorage.getItem('useBluetoothPrinter') === 'true';
    
    // If Bluetooth printing is enabled and the printer is connected
    if (useBluetoothPrinter && printerStatus === 'connected') {
      try {
        setIsPrinting(true);
        const success = await printReceipt(order);
        
        if (success) {
          toast({
            title: "Print Successful",
            description: `Customer receipt for ${order.id} has been sent to printer`,
          });
        } else {
          throw new Error('Printing failed');
        }
      } catch (error) {
        console.error('Print error:', error);
        toast({
          title: "Print Failed",
          description: "Could not print the receipt. Please try again.",
          variant: "destructive",
        });
        
        // Fall back to browser-based printing
        fallbackBrowserPrint('receipt');
      } finally {
        setIsPrinting(false);
      }
    }
    // If Bluetooth printing is enabled but printer is not connected
    else if (useBluetoothPrinter && printerStatus !== 'connected') {
      toast({
        title: "Printer Not Connected",
        description: "Connect your Bluetooth printer in Settings",
        variant: "destructive",
        action: (
          <Button variant="outline" size="sm" onClick={showPrinterConnect}>
            Connect
          </Button>
        )
      });
      
      // Fall back to browser-based printing
      fallbackBrowserPrint('receipt');
    }
    // Use browser-based printing
    else {
      fallbackBrowserPrint('receipt');
    }
  };

  const handlePrintJobOrder = async () => {
    const useBluetoothPrinter = localStorage.getItem('useBluetoothPrinter') === 'true';
    
    // If Bluetooth printing is enabled and the printer is connected
    if (useBluetoothPrinter && printerStatus === 'connected') {
      try {
        setIsPrinting(true);
        const success = await printJobOrder(order);
        
        if (success) {
          toast({
            title: "Print Successful",
            description: `Job order for ${order.id} has been sent to printer`,
          });
        } else {
          throw new Error('Printing failed');
        }
      } catch (error) {
        console.error('Print error:', error);
        toast({
          title: "Print Failed",
          description: "Could not print the job order. Please try again.",
          variant: "destructive",
        });
        
        // Fall back to browser-based printing
        fallbackBrowserPrint('jobOrder');
      } finally {
        setIsPrinting(false);
      }
    }
    // If Bluetooth printing is enabled but printer is not connected
    else if (useBluetoothPrinter && printerStatus !== 'connected') {
      toast({
        title: "Printer Not Connected",
        description: "Connect your Bluetooth printer in Settings",
        variant: "destructive",
        action: (
          <Button variant="outline" size="sm" onClick={showPrinterConnect}>
            Connect
          </Button>
        )
      });
      
      // Fall back to browser-based printing
      fallbackBrowserPrint('jobOrder');
    }
    // Use browser-based printing
    else {
      fallbackBrowserPrint('jobOrder');
    }
  };

  const fallbackBrowserPrint = (type: 'receipt' | 'jobOrder') => {
    try {
      // Get add-on quantities from order data
      const detergentQty = order.detergentQuantity || 1;
      const conditionerQty = order.conditionerQuantity || 1;
      
      // Format the order data for printing
      let printContent = '';
      let title = '';
      
      if (type === 'receipt') {
        title = 'Customer Receipt';
        printContent = `
CMC LAUNDRY
-----------
ORDER SLIP
----------
Order ID: ${order.id}
Date: ${order.orderDate}
Customer: ${order.customer.name}
Phone: ${order.customer.phone}

Items:
${order.lineItems?.map(item => 
  `${item.name} x${item.quantity} - ₱${item.total.toFixed(2)}`
).join('\n') || 'No items'}

Weight: ${order.weightKilos || 0} kg
Pieces: ${order.numberOfPieces || 0}

Add-ons:
${order.useDetergent ? `- Detergent x${detergentQty}` : ''}
${order.useFabricConditioner ? `- Fabric Conditioner x${conditionerQty}` : ''}
${order.useStainRemover ? '- Stain Remover' : ''}
${order.useBleach ? '- Bleach Treatment' : ''}

Subtotal: ₱${order.subtotalBeforeVAT?.toFixed(2) || '0.00'}
VAT: ₱${order.vatAmount?.toFixed(2) || '0.00'}
Total Amount: ₱${order.amount.toFixed(2)}
Paid Amount: ₱${order.paidAmount.toFixed(2)}
Balance: ₱${(order.amount - order.paidAmount).toFixed(2)}

Status: ${order.status.toUpperCase()}
        `.trim();
      } else {
        title = 'Job Order';
        printContent = `
CMC LAUNDRY
-----------
JOB ORDER
----------
Order ID: ${order.id}
Date: ${order.orderDate}
Service: ${order.serviceType?.toUpperCase() || "REGULAR SERVICE"}
Customer: ${order.customer.name}
Phone: ${order.customer.phone}

ITEMS TO PROCESS:
${order.lineItems?.map(item => 
  `- ${item.name} x${item.quantity}`
).join('\n') || 'No specific items'}

SPECIFICATIONS:
Weight: ${order.weightKilos || 0} kg
Pieces: ${order.numberOfPieces || 0}

PROCESSING INSTRUCTIONS:
${order.useDetergent ? `☐ Use Detergent: ${order.detergentType || "Standard"}\n  Quantity: ${detergentQty}` : ''}
${order.useFabricConditioner ? `☐ Use Fabric Conditioner: ${order.conditionerType || "Standard"}\n  Quantity: ${conditionerQty}` : ''}
${order.useStainRemover ? '☐ Apply Stain Remover Treatment' : ''}
${order.useBleach ? '☐ Apply Bleach Treatment' : ''}

${order.notes ? `SPECIAL NOTES:\n${order.notes}` : ''}

STAFF PROCESSING CHECKLIST:
☐ Sorting Complete
☐ Pre-treatment Applied
☐ Washing Complete
☐ Drying Complete
☐ Folding Complete
☐ Quality Check
☐ Ready for Pickup

Assigned Staff: ________________

Status: ${order.status.toUpperCase()}
        `.trim();
      }

      // Send to browser printer
      const printWindow = window.open('', '', 'width=600,height=600');
      if (!printWindow) {
        throw new Error('Could not open print window');
      }

      printWindow.document.open();
      printWindow.document.write(`
        <html>
          <head>
            <title>${title} - ${order.id}</title>
            <style>
              body {
                font-family: monospace;
                font-size: 12px;
                white-space: pre;
                margin: 0;
                padding: 20px;
              }
              @media print {
                body { margin: 0; }
              }
            </style>
          </head>
          <body>${printContent}</body>
        </html>
      `);
      printWindow.document.close();
      printWindow.print();
      printWindow.close();

      toast({
        title: "Print Initiated",
        description: `${title} for ${order.id} has been sent to printer`,
      });
    } catch (error) {
      console.error('Print error:', error);
      toast({
        title: "Print Failed",
        description: "Could not print the document. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[600px] max-h-screen overflow-y-auto">
          <DialogHeader className="flex flex-row justify-between items-center">
            <div>
              <DialogTitle>Order Details: {order.id}</DialogTitle>
              <div className="mt-2">
                <OrderStatusBadge status={order.status} />
              </div>
            </div>
            <div className="flex space-x-2">
              {/* Edit button - only for admin/staff users */}
              {canEdit && (
                <Button
                  variant="outline"
                  size="icon"
                  onClick={handleEditOrder}
                  className="text-blue-500 hover:text-blue-700 hover:bg-blue-100"
                >
                  <Edit className="h-4 w-4" />
                </Button>
              )}
              
              {/* Delete button - only for admin users */}
              {isAdmin && (
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => setIsDeleteDialogOpen(true)}
                  className="text-red-500 hover:text-red-700 hover:bg-red-100"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              )}
            </div>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Customer Information</h4>
                  <div className="text-sm">
                    <div>
                      <strong>Name:</strong> {order.customer?.name || "N/A"}
                    </div>
                    <div>
                      <strong>Phone:</strong> {order.customer?.phone || "N/A"}
                    </div>
                    <div>
                      <strong>Type:</strong> {order.customerType === "walk-in" ? "Walk-in Customer" : "Client"}
                    </div>
                    {/* Only try to access email if it exists in the customer object */}
                    {(order.customer as any)?.email && (
                      <div>
                        <strong>Email:</strong> {(order.customer as any).email}
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <div>
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Order Details</h4>
                  <div className="text-sm">
                    <div>
                      <strong>Order Date:</strong> {order.orderDate}
                    </div>
                    <div>
                      <strong>Delivery Date:</strong> {order.deliveryDate || "N/A"}
                    </div>
                    <div>
                      <strong>Payment Status:</strong> {order.paidAmount >= order.amount ? "Paid" : "Pending"}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="text-sm font-medium">Items</h4>
              <div className="text-sm">
                <ul className="list-none pl-0">
                  {order.lineItems && order.lineItems.length > 0 ? (
                    order.lineItems.map((item) => (
                      <li key={item.id} className="flex justify-between py-1 border-b">
                        <span>{item.name} x {item.quantity}</span>
                        <span>₱{item.total.toFixed(2)}</span>
                      </li>
                    ))
                  ) : (
                    <li>No items in this order.</li>
                  )}
                </ul>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Additional Information</h4>
                  <div className="text-sm">
                    {order.weightKilos && (
                      <div>
                        <strong>Weight:</strong> {order.weightKilos} kg
                      </div>
                    )}
                    {order.numberOfPieces && (
                      <div>
                        <strong>Pieces:</strong> {order.numberOfPieces}
                      </div>
                    )}
                    {order.notes && (
                      <div>
                        <strong>Notes:</strong> {order.notes}
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <div>
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Payment Details</h4>
                  <div className="text-sm">
                    <div>
                      <strong>Subtotal:</strong> ₱{order.subtotalBeforeVAT?.toFixed(2) || '0.00'}
                    </div>
                    <div>
                      <strong>VAT:</strong> ₱{order.vatAmount?.toFixed(2) || '0.00'}
                    </div>
                    <div>
                      <strong>Total Amount:</strong> ₱{order.amount?.toFixed(2) || '0.00'}
                    </div>
                    <div>
                      <strong>Paid Amount:</strong> ₱{order.paidAmount?.toFixed(2) || '0.00'}
                    </div>
                    <div>
                      <strong>Balance:</strong> ₱{(order.amount - order.paidAmount)?.toFixed(2) || '0.00'}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <div className="flex gap-2 justify-end w-full">
              {/* Print Receipt Button */}
              <Button 
                variant="outline" 
                onClick={handlePrintReceipt}
                disabled={isPrinting}
              >
                <Printer className="h-4 w-4 mr-2" />
                Receipt
              </Button>

              {/* Print Job Order Button - only for staff and admin */}
              {isStaff && (
                <Button 
                  variant="outline" 
                  onClick={handlePrintJobOrder}
                  disabled={isPrinting}
                >
                  <FileText className="h-4 w-4 mr-2" />
                  Job Order
                </Button>
              )}
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Delete Order Dialog */}
      {order && (
        <DeleteOrderDialog
          orderId={order.uuid || order.id}
          orderReference={order.id}
          open={isDeleteDialogOpen}
          onOpenChange={setIsDeleteDialogOpen}
          onOrderDeleted={handleOrderDeleted}
        />
      )}
      
      {/* Edit Order Dialog */}
      {order && canEdit && (
        <EditOrderDialog
          order={order}
          open={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
          onOrderUpdated={handleOrderUpdated}
        />
      )}
    </>
  );
}
