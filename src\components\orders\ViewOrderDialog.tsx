
import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer,
} from "@/components/ui/dialog";
import { OrderStatusBadge } from "./OrderStatusBadge";
import { Order } from "@/types";
import { useAuth } from "@/contexts/auth";
import { DeleteOrderDialog } from "./DeleteOrderDialog";
import { EditOrderDialog } from "./EditOrderDialog";
import { OrderLineItems } from "./dialog/OrderLineItems";
import { OrderActions } from "./dialog/OrderActions";
import { CustomerInformation } from "./dialog/CustomerInformation";
import { OrderDetails } from "./dialog/OrderDetails";
import { AdditionalInformation } from "./dialog/AdditionalInformation";
import { PaymentInformation } from "./dialog/PaymentInformation";
import { PrintButtons } from "./dialog/PrintButtons";
import { usePrintFunctions } from "./dialog/PrintingUtils";
import { OrderStatusWorkflow } from "./workflow/OrderStatusWorkflow";

interface ViewOrderDialogProps {
  order: Order;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onStatusChange?: (orderId: string, newStatus: string) => Promise<void>;
}

export function ViewOrderDialog({
  order,
  open,
  onOpenChange,
  onStatusChange,
}: ViewOrderDialogProps) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const { userRole } = useAuth();
  const { isPrinting, handlePrintReceipt, handlePrintJobOrder } = usePrintFunctions();
  
  const isAdmin = userRole === 'admin';
  const canEdit = userRole === 'admin' || userRole === 'staff';
  const isStaff = userRole === 'staff' || userRole === 'admin';

  const handleStatusChange = async (newStatus: string) => {
    if (onStatusChange) {
      await onStatusChange(order.id, newStatus);
    }
  };

  const handleOrderDeleted = () => {
    // Close the view dialog when order is deleted
    onOpenChange(false);
    
    // Refresh the orders list
    window.dispatchEvent(new CustomEvent('order-deleted'));
  };
  
  const handleEditOrder = () => {
    setIsEditDialogOpen(true);
  };
  
  const handleOrderUpdated = () => {
    // Close the view dialog when order is updated
    window.dispatchEvent(new CustomEvent('order-status-updated'));
  };

  // Determine if this is a client order with client items
  const isClientOrder = order.customerType === 'client' || 
                       (!!order.clientId && order.clientId !== '00000000-0000-0000-0000-000000000000') ||
                       (order.id.includes('-') && !order.id.startsWith('ORD-'));

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[600px] max-h-screen overflow-y-auto">
          <DialogHeader className="flex flex-row justify-between items-center">
            <div>
              <DialogTitle>Order Details: {order.id}</DialogTitle>
              <div className="mt-2">
                <OrderStatusBadge status={order.status} />
              </div>
            </div>
            
            <OrderActions 
              order={order}
              canEdit={canEdit}
              isAdmin={isAdmin}
              onEdit={handleEditOrder}
              onDelete={() => setIsDeleteDialogOpen(true)}
            />
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <CustomerInformation order={order} />
              <OrderDetails order={order} />
            </div>

            {/* Use the OrderLineItems component */}
            <OrderLineItems order={order} />

            {/* Add the OrderStatusWorkflow component */}
            {isStaff && onStatusChange && (
              <OrderStatusWorkflow
                order={order}
                onStatusChange={onStatusChange}
              />
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <AdditionalInformation order={order} />
              <PaymentInformation order={order} />
            </div>
          </div>
          
          <DialogFooter>
            <PrintButtons
              onPrintReceipt={() => handlePrintReceipt(order)}
              onPrintJobOrder={() => handlePrintJobOrder(order)}
              isPrinting={isPrinting}
              isStaff={isStaff}
            />
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Delete Order Dialog */}
      {order && (
        <DeleteOrderDialog
          orderId={order.uuid || order.id}
          orderReference={order.id}
          open={isDeleteDialogOpen}
          onOpenChange={setIsDeleteDialogOpen}
          onOrderDeleted={handleOrderDeleted}
        />
      )}
      
      {/* Edit Order Dialog */}
      {order && canEdit && (
        <EditOrderDialog
          order={order}
          open={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
          onOrderUpdated={handleOrderUpdated}
        />
      )}
    </>
  );
}
