
import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import { OrderStatusBadge } from "./OrderStatusBadge";
import { Order } from "@/types";
import { useAuth } from "@/contexts/auth";
import { OrderActions } from "./dialog/OrderActions";
import { PrintButtons } from "./dialog/PrintButtons";
import { usePrintFunctions } from "./dialog/PrintingUtils";
import { useOrderDialogs } from "./dialog/OrderDialogs";
import { OrderContent } from "./dialog/OrderContent";
import { useOrderData } from "./dialog/hooks/useOrderData";
import { OrderDialogManager } from "./dialog/OrderDialogManager";
import { OrderContextProvider } from "@/contexts/OrderContext";

interface ViewOrderDialogProps {
  order: Order;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onStatus<PERSON>hange?: (orderId: string, newStatus: string) => Promise<void>;
}

export function ViewOrderDialog({
  order,
  open,
  onOpenChange,
  onStatusChange,
}: ViewOrderDialogProps) {
  const { userRole } = useAuth();
  const { isPrinting, handlePrintReceipt, handlePrintJobOrder } = usePrintFunctions();
  const { currentOrder, refreshOrderData } = useOrderData(order);
  
  // Get dialog state management functions
  const { 
    isDeleteDialogOpen, 
    setIsDeleteDialogOpen,
    isEditDialogOpen, 
    setIsEditDialogOpen
  } = useOrderDialogs();
  
  const isAdmin = userRole === 'admin';
  const canEdit = userRole === 'admin' || userRole === 'staff';

  const handleStatusChange = async (newStatus: string) => {
    if (onStatusChange) {
      await onStatusChange(order.id, newStatus);
      refreshOrderData();
    }
  };

  const handleOrderDeleted = () => {
    // Close the view dialog when order is deleted
    onOpenChange(false);
    
    // Refresh the orders list
    window.dispatchEvent(new CustomEvent('order-deleted'));
  };
  
  const handleOrderUpdated = () => {
    // Refresh order data when updated
    refreshOrderData();
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[700px] md:max-w-[800px] max-h-[90vh] overflow-y-auto">
          <OrderContextProvider initialOrder={currentOrder} onOrderRefreshed={handleOrderUpdated}>
            <DialogHeader className="flex flex-row justify-between items-center">
              <div>
                <DialogTitle>Order Details: {currentOrder.id}</DialogTitle>
                <div className="mt-2">
                  <OrderStatusBadge status={currentOrder.status} />
                </div>
              </div>
              
              <OrderActions 
                order={currentOrder}
                canEdit={canEdit}
                isAdmin={isAdmin}
                onEdit={() => setIsEditDialogOpen(true)}
                onDelete={() => setIsDeleteDialogOpen(true)}
                onClose={() => onOpenChange(false)}
              />
            </DialogHeader>
            
            <OrderContent 
              order={currentOrder}
              onStatusChange={handleStatusChange}
              onOrderUpdated={handleOrderUpdated}
            />
            
            <DialogFooter>
              <PrintButtons
                onPrintReceipt={() => handlePrintReceipt(currentOrder)}
                onPrintJobOrder={() => handlePrintJobOrder(currentOrder)}
                isPrinting={isPrinting}
                isStaff={userRole === 'staff' || userRole === 'admin'}
              />
            </DialogFooter>
          </OrderContextProvider>
        </DialogContent>
      </Dialog>
      
      {/* Use simplified OrderDialogManager without item and addon edit dialogs */}
      <OrderDialogManager 
        order={currentOrder}
        canEdit={canEdit}
        isDeleteDialogOpen={isDeleteDialogOpen}
        setIsDeleteDialogOpen={setIsDeleteDialogOpen}
        isEditDialogOpen={isEditDialogOpen}
        setIsEditDialogOpen={setIsEditDialogOpen}
        isEditItemsDialogOpen={false}
        setIsEditItemsDialogOpen={() => {}}
        isEditAddOnsDialogOpen={false}
        setIsEditAddOnsDialogOpen={() => {}}
        onOrderDeleted={handleOrderDeleted}
        onOrderUpdated={handleOrderUpdated}
      />
    </>
  );
}
