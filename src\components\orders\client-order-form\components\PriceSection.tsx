
import React, { useState } from "react";
import { PriceBreakdown, ClientItemWithQuantity, DryCleaningItem } from "../../OrderFormTypes";
import { FormSectionWrapper } from "./FormSectionWrapper";
import { SERVICE_TYPES } from "../../pricing/constants";
import { TotalSection } from "../../price-calculation/TotalSection";
import { ClientItemsBreakdown, DryCleaningItemsBreakdown } from "../../price-calculation/ItemsBreakdownSection";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface PriceSectionProps {
  priceBreakdown: PriceBreakdown;
  detergentType: "none" | "regular" | "color";
  detergentQuantity: number;
  conditionerType: "none" | "regular" | "fresh" | "floral";
  conditionerQuantity: number;
  useStainRemover: boolean;
  useBleach: boolean;
  serviceType: string;
  selectedServiceTypes?: string[];
  weightKilos: number;
  numberOfPieces: number;
  clientItems: ClientItemWithQuantity[];
  dryCleaningItems?: DryCleaningItem[];
  serviceWeights?: { serviceType: string; weightKilos: number }[];
}

export function PriceSection({
  priceBreakdown,
  detergentType,
  conditionerType,
  detergentQuantity,
  conditionerQuantity,
  useStainRemover,
  useBleach,
  clientItems,
  serviceType,
  selectedServiceTypes = [SERVICE_TYPES.WASH_DRY_FOLD],
  dryCleaningItems = [],
  weightKilos,
  serviceWeights = []
}: PriceSectionProps) {
  const [activeTab, setActiveTab] = useState("all");
  
  // Check if we have multiple service types
  const includesWashAndFold = selectedServiceTypes.some(type => 
    type === SERVICE_TYPES.WASH_DRY_FOLD || 
    type === SERVICE_TYPES.WASH_DRY_PRESS || 
    type === SERVICE_TYPES.WASH_DRY_FOLD_SPECIAL ||
    type === SERVICE_TYPES.COMFORTERS ||
    type === SERVICE_TYPES.TOWELS_CURTAINS_LINENS
  );
  const includesDryCleaning = selectedServiceTypes.includes(SERVICE_TYPES.DRY_CLEANING);
  const hasAddOns = 
    detergentType !== "none" || 
    conditionerType !== "none" || 
    useStainRemover || 
    useBleach;
  const hasMultipleServices = selectedServiceTypes.length > 1;
  
  // Only show tabs if both services are selected
  const showServiceTabs = hasMultipleServices && includesWashAndFold && includesDryCleaning;

  // Helper function to get weight for a specific service
  const getServiceWeight = (serviceType: string) => {
    const serviceWeight = serviceWeights.find(sw => sw.serviceType === serviceType);
    return serviceWeight ? serviceWeight.weightKilos : weightKilos;
  };

  return (
    <FormSectionWrapper>
      <h3 className="font-semibold text-lg mb-4">Price Calculation</h3>
      
      {showServiceTabs ? (
        <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3 mb-2">
            <TabsTrigger value="all" className="text-xs md:text-sm">All Services</TabsTrigger>
            <TabsTrigger value="wash" className="text-xs md:text-sm">Wash & Fold</TabsTrigger>
            <TabsTrigger value="dry" className="text-xs md:text-sm">Dry Cleaning</TabsTrigger>
          </TabsList>
          
          <TabsContent value="all" className="mt-0 space-y-4">
            {/* Combined view showing both services */}
            <div className="border rounded-md bg-white shadow-sm">
              {includesWashAndFold && clientItems && clientItems.length > 0 && (
                <div className="p-4 border-b">
                  <h4 className="font-medium mb-2 text-laundry-blue">Wash & Fold Items</h4>
                  <ClientItemsBreakdown 
                    items={clientItems} 
                    basePrice={priceBreakdown.basePrice} 
                  />
                </div>
              )}
              
              {includesDryCleaning && dryCleaningItems && dryCleaningItems.length > 0 && (
                <div className="p-4 border-b">
                  <h4 className="font-medium mb-2 text-amber-600">Dry Cleaning Items</h4>
                  <DryCleaningItemsBreakdown 
                    items={dryCleaningItems} 
                    basePrice={priceBreakdown.basePrice} 
                  />
                </div>
              )}
              
              {/* Total section */}
              <div className="p-4">
                <TotalSection priceBreakdown={priceBreakdown} />
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="wash" className="mt-0 space-y-4">
            {/* Wash & Fold view */}
            <div className="border rounded-md bg-white shadow-sm">
              {includesWashAndFold && clientItems && clientItems.length > 0 && (
                <div className="p-4 border-b">
                  <ClientItemsBreakdown 
                    items={clientItems} 
                    basePrice={priceBreakdown.basePrice} 
                  />
                </div>
              )}
              
              {/* Total section */}
              <div className="p-4">
                <TotalSection priceBreakdown={priceBreakdown} />
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="dry" className="mt-0 space-y-4">
            {/* Dry Cleaning view */}
            <div className="border rounded-md bg-white shadow-sm">
              {includesDryCleaning && dryCleaningItems && dryCleaningItems.length > 0 && (
                <div className="p-4 border-b">
                  <DryCleaningItemsBreakdown 
                    items={dryCleaningItems} 
                    basePrice={priceBreakdown.basePrice} 
                  />
                </div>
              )}
              
              {/* Total section */}
              <div className="p-4">
                <TotalSection priceBreakdown={priceBreakdown} />
              </div>
            </div>
          </TabsContent>
        </Tabs>
      ) : (
        <div className="space-y-4">
          {/* Display services based on selected types */}
          {includesWashAndFold && clientItems && clientItems.length > 0 && (
            <div className={`${hasMultipleServices ? 'mb-4' : ''}`}>
              {hasMultipleServices && <h4 className="font-medium mb-2 text-laundry-blue">Wash & Fold Items</h4>}
              <ClientItemsBreakdown 
                items={clientItems} 
                basePrice={priceBreakdown.basePrice} 
              />
            </div>
          )}
          
          {includesDryCleaning && dryCleaningItems && dryCleaningItems.length > 0 && (
            <div>
              {hasMultipleServices && <h4 className="font-medium mb-2 text-amber-600">Dry Cleaning Items</h4>}
              <DryCleaningItemsBreakdown 
                items={dryCleaningItems} 
                basePrice={priceBreakdown.basePrice} 
              />
            </div>
          )}
          
          {/* Total section */}
          <TotalSection priceBreakdown={priceBreakdown} />
        </div>
      )}
    </FormSectionWrapper>
  );
}
