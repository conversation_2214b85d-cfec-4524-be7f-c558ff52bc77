
import React from "react";
import { PriceBreakdown, DryCleaningItem, ClientItemWithQuantity } from "../../OrderFormTypes";
import { FormSectionWrapper } from "./FormSectionWrapper";
import { SERVICE_TYPES } from "../../pricing/constants";

interface PriceSectionProps {
  priceBreakdown: PriceBreakdown;
  detergentType: "none" | "regular" | "color";
  detergentQuantity: number;
  conditionerType: "none" | "regular" | "fresh" | "floral";
  conditionerQuantity: number;
  useStainRemover: boolean;
  useBleach: boolean;
  serviceType: string;
  weightKilos: number;
  numberOfPieces: number;
  clientItems: ClientItemWithQuantity[];
  dryCleaningItems?: DryCleaningItem[];
}

export function PriceSection({
  priceBreakdown,
  detergentType,
  conditionerType,
  detergentQuantity,
  conditionerQuantity,
  useStainRemover,
  useBleach,
  clientItems,
  serviceType,
  dryCleaningItems = []
}: PriceSectionProps) {
  
  const isDryCleaning = serviceType === SERVICE_TYPES.DRY_CLEANING;
  const hasAddOns = 
    detergentType !== "none" || 
    conditionerType !== "none" || 
    useStainRemover || 
    useBleach;

  return (
    <FormSectionWrapper>
      <h3 className="font-semibold text-lg mb-4">Price Calculation</h3>
      
      <div className="space-y-4">
        {/* Show dry cleaning items or client items based on service type */}
        {isDryCleaning && dryCleaningItems && dryCleaningItems.length > 0 ? (
          <DryCleaningItemsBreakdown 
            items={dryCleaningItems} 
            basePrice={priceBreakdown.basePrice} 
          />
        ) : (
          <ClientItemsBreakdown 
            items={clientItems} 
            basePrice={priceBreakdown.basePrice} 
          />
        )}
        
        {/* Add-ons are no longer included in client order forms */}
        
        {/* Total section */}
        <TotalSection priceBreakdown={priceBreakdown} />
      </div>
    </FormSectionWrapper>
  );
}

// Include the necessary components that were previously imported
function TotalSection({ priceBreakdown }: { priceBreakdown: PriceBreakdown }) {
  return (
    <div className="border-t pt-3">
      <div className="space-y-1">
        <div className="flex justify-between text-sm">
          <span>Subtotal:</span>
          <span>₱{priceBreakdown.subtotal.toFixed(2)}</span>
        </div>
        
        <div className="flex justify-between text-sm">
          <span>VAT (12%):</span>
          <span>₱{priceBreakdown.vatAmount.toFixed(2)}</span>
        </div>
        
        <div className="flex justify-between font-semibold text-base mt-2">
          <span>Total:</span>
          <span>₱{priceBreakdown.totalPrice.toFixed(2)}</span>
        </div>
      </div>
    </div>
  );
}

function DryCleaningItemsBreakdown({ 
  items, 
  basePrice 
}: { 
  items: DryCleaningItem[];
  basePrice: number;
}) {
  if (!items || items.length === 0) return null;
  
  return (
    <div className="border rounded-md p-3">
      <h4 className="font-medium mb-2">Dry Cleaning Items</h4>
      <div className="space-y-1 text-sm">
        {items.map((item, index) => (
          <div key={index} className="flex justify-between">
            <span>
              {item.type} x{item.quantity || 1}
            </span>
            <span>₱{((item.price || 0) * (item.quantity || 1)).toFixed(2)}</span>
          </div>
        ))}
        
        <div className="flex justify-between font-medium pt-1 border-t">
          <span>Subtotal:</span>
          <span>₱{basePrice.toFixed(2)}</span>
        </div>
      </div>
    </div>
  );
}

function ClientItemsBreakdown({ 
  items, 
  basePrice 
}: { 
  items: ClientItemWithQuantity[];
  basePrice: number;
}) {
  if (!items || items.length === 0) return null;
  
  return (
    <div className="border rounded-md p-3">
      <h4 className="font-medium mb-2">Items</h4>
      <div className="space-y-1 text-sm">
        {items.map((item, index) => (
          <div key={index} className="flex justify-between">
            <span>
              {item.name} x{item.quantity || 1}
            </span>
            <span>₱{((item.unit_price || 0) * (item.quantity || 1)).toFixed(2)}</span>
          </div>
        ))}
        
        <div className="flex justify-between font-medium pt-1 border-t">
          <span>Subtotal:</span>
          <span>₱{basePrice.toFixed(2)}</span>
        </div>
      </div>
    </div>
  );
}
