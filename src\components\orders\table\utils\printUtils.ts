
import { Order } from "@/types";
import { useToast } from "@/hooks/use-toast";

/**
 * Creates browser-based print content and sends to printer
 */
export const fallbackBrowserPrint = (order: Order, type: 'receipt' | 'jobOrder') => {
  try {
    // Get add-on quantities from order data
    const detergentQty = order.detergentQuantity || 1;
    const conditionerQty = order.conditionerQuantity || 1;
    
    // Check if order has dry cleaning items
    const hasDryCleaningItems = (order.isDryCleaning || order.serviceType === 'dry_cleaning') && 
                               Array.isArray(order.dryCleaningItems) && 
                               order.dryCleaningItems.length > 0;
    
    // Format item type for display
    const formatItemType = (type: string) => {
      return type
        .replace(/_/g, ' ')
        .replace(/\w\S*/g, txt => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase());
    };
    
    // Format the order data for printing
    let printContent = '';
    let title = '';
    
    if (type === 'receipt') {
      title = 'Customer Receipt';
      printContent = `
CMC LAUNDRY
-----------
ORDER SLIP
----------
Order ID: ${order.id}
Date: ${order.orderDate}
Customer: ${order.customer.name}
Phone: ${order.customer.phone}
${(order.customer as any)?.email ? `Email: ${(order.customer as any).email}` : ''}

${order.isDryCleaning || order.serviceType === 'dry_cleaning' ? 'SERVICE: DRY CLEANING' : ''}

Items:
`;

      // Add regular line items with proper treatment info
      if (order.lineItems && order.lineItems.length > 0) {
        order.lineItems.forEach(item => {
          const treatment = item.treatmentDescription ? ` (${item.treatmentDescription})` : '';
          printContent += `${item.name}${treatment} x${item.quantity} - ₱${item.total ? item.total.toFixed(2) : (item.unitPrice * item.quantity).toFixed(2)}\n`;
        });
      } else {
        printContent += 'No items\n';
      }

      if (hasDryCleaningItems) {
        printContent += `
Dry Cleaning Items:
${order.dryCleaningItems?.map(item => 
`${formatItemType(item.type)} x${item.quantity || 1} - ₱${((item.price || 0) * (item.quantity || 1)).toFixed(2)}`
).join('\n')}
`;
      }

      printContent += `
Weight: ${order.weightKilos || 0} kg
Pieces: ${order.numberOfPieces || 0}

Add-ons:
${order.useDetergent ? `- Detergent (${order.detergentType || 'Standard'}) x${detergentQty}` : ''}
${order.useFabricConditioner ? `- Fabric Conditioner (${order.conditionerType || 'Standard'}) x${conditionerQty}` : ''}
${order.useStainRemover ? '- Stain Remover' : ''}
${order.useBleach ? '- Bleach Treatment' : ''}

Subtotal: ₱${order.subtotalBeforeVAT?.toFixed(2) || '0.00'}
VAT: ₱${order.vatAmount?.toFixed(2) || '0.00'}
Total Amount: ₱${order.amount.toFixed(2)}
Paid Amount: ₱${order.paidAmount.toFixed(2)}
Balance: ₱${(order.amount - order.paidAmount).toFixed(2)}

Status: ${order.status.toUpperCase()}
      `.trim();
    } else {
      title = 'Job Order';
      printContent = `
CMC LAUNDRY
-----------
JOB ORDER
----------
Order ID: ${order.id}
Date: ${order.orderDate}
Service: ${order.isDryCleaning || order.serviceType === 'dry_cleaning' ? "DRY CLEANING" : (order.serviceType?.toUpperCase() || "REGULAR SERVICE")}
Customer: ${order.customer.name}
Phone: ${order.customer.phone}
${(order.customer as any)?.email ? `Email: ${(order.customer as any).email}` : ''}

ITEMS TO PROCESS:
`;
      
      // Add regular line items with treatment info
      if (order.lineItems && order.lineItems.length > 0) {
        order.lineItems.forEach(item => {
          const treatment = item.treatmentDescription ? ` (${item.treatmentDescription})` : '';
          printContent += `- ${item.name}${treatment} x${item.quantity}\n`;
        });
      } else {
        printContent += 'No specific items\n';
      }

      if (hasDryCleaningItems) {
        printContent += `
DRY CLEANING ITEMS:
${order.dryCleaningItems?.map(item => 
`- ${formatItemType(item.type)} x${item.quantity || 1}`
).join('\n')}
`;
      }

      printContent += `
SPECIFICATIONS:
Weight: ${order.weightKilos || 0} kg
Pieces: ${order.numberOfPieces || 0}

PROCESSING INSTRUCTIONS:
${order.useDetergent ? `☐ Use Detergent: ${order.detergentType || "Standard"}\n  Quantity: ${detergentQty}` : ''}
${order.useFabricConditioner ? `☐ Use Fabric Conditioner: ${order.conditionerType || "Standard"}\n  Quantity: ${conditionerQty}` : ''}
${order.useStainRemover ? '☐ Apply Stain Remover Treatment' : ''}
${order.useBleach ? '☐ Apply Bleach Treatment' : ''}
${order.isDryCleaning || order.serviceType === 'dry_cleaning' ? '☐ Process as DRY CLEANING' : ''}

${order.notes ? `SPECIAL NOTES:\n${order.notes}` : ''}

STAFF PROCESSING CHECKLIST:
☐ Sorting Complete
☐ Pre-treatment Applied
☐ Washing Complete
☐ Drying Complete
☐ Folding Complete
☐ Quality Check
☐ Ready for Pickup

Assigned Staff: ________________

Status: ${order.status.toUpperCase()}
      `.trim();
    }

    // Send to browser printer
    const printWindow = window.open('', '', 'width=600,height=600');
    if (!printWindow) {
      throw new Error('Could not open print window');
    }

    printWindow.document.open();
    printWindow.document.write(`
      <html>
        <head>
          <title>${title} - ${order.id}</title>
          <style>
            body {
              font-family: monospace;
              font-size: 12px;
              white-space: pre;
              margin: 0;
              padding: 20px;
            }
            @media print {
              body { margin: 0; }
            }
          </style>
        </head>
        <body>${printContent}</body>
      </html>
    `);
    printWindow.document.close();
    printWindow.print();
    printWindow.close();

    return { success: true, title };
  } catch (error) {
    console.error('Print error:', error);
    return { success: false, error };
  }
};
