
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Plus, Minus } from "lucide-react";
import { ClientItemWithQuantity } from "../OrderFormTypes";

interface ItemQuantityControlsProps {
  instance: ClientItemWithQuantity;
  onQuantityChange: (instanceId: string, quantity: number) => void;
}

export function ItemQuantityControls({ instance, onQuantityChange }: ItemQuantityControlsProps) {
  const handleQuantityChange = (value: number) => {
    onQuantityChange(instance.instanceId, value);
  };
  
  return (
    <div className="flex items-center justify-between space-x-2">
      <Button 
        variant="outline" 
        size="icon" 
        className="h-8 w-8" 
        type="button"
        onClick={(e) => {
          e.preventDefault();
          handleQuantityChange(Math.max(1, (instance.quantity || 1) - 1));
        }}
      >
        <Minus className="h-3 w-3" />
      </Button>
      
      <Input
        type="number"
        min="1"
        value={instance.quantity || 1}
        onChange={(e) => handleQuantityChange(parseInt(e.target.value) || 1)}
        className="text-center h-8 w-14"
        onClick={(e) => e.stopPropagation()}
        onBlur={(e) => {
          const value = parseInt(e.target.value);
          if (!value || value < 1) {
            handleQuantityChange(1);
          }
        }}
      />
      
      <Button 
        variant="outline" 
        size="icon" 
        className="h-8 w-8" 
        type="button"
        onClick={(e) => {
          e.preventDefault();
          handleQuantityChange((instance.quantity || 1) + 1);
        }}
      >
        <Plus className="h-3 w-3" />
      </Button>
    </div>
  );
}
