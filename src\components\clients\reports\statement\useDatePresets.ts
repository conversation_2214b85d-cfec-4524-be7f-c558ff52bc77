
import { addDays, addMonths, startOfMonth, endOfMonth, startOfYear, endOfYear, subMonths, subYears } from 'date-fns';

export function useDatePresets() {
  const today = new Date();
  
  const datePresets = [
    {
      label: 'This Month',
      getValue: () => ({
        from: startOfMonth(today),
        to: endOfMonth(today)
      })
    },
    {
      label: 'Last Month',
      getValue: () => {
        const lastMonth = addMonths(today, -1);
        return {
          from: startOfMonth(lastMonth),
          to: endOfMonth(lastMonth)
        };
      }
    },
    {
      label: 'Last 30 Days',
      getValue: () => ({
        from: addDays(today, -30),
        to: today
      })
    },
    {
      label: 'Last 90 Days',
      getValue: () => ({
        from: addDays(today, -90),
        to: today
      })
    },
    {
      label: 'Last 6 Months',
      getValue: () => ({
        from: subMonths(today, 6),
        to: today
      })
    },
    {
      label: 'This Year',
      getValue: () => ({
        from: startOfYear(today),
        to: endOfYear(today)
      })
    },
    {
      label: 'Last Year',
      getValue: () => {
        const lastYear = subYears(today, 1);
        return {
          from: startOfYear(lastYear),
          to: endOfYear(lastYear)
        };
      }
    }
  ];

  return { datePresets };
}
