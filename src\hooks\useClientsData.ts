
import { useState, useEffect, useCallback } from "react";
import { getClients } from "@/services/clients/queries";
import { supabase } from "@/integrations/supabase/client";

// Define client type for better type safety
export interface ClientData {
  id: string;
  name: string;
  prefix?: string;
  contact_person?: string;
  phone?: string;
}

export function useClientsData() {
  const [clients, setClients] = useState<ClientData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [ready, setReady] = useState(false);
  
  const loadClients = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      console.log("Fetching clients...");
      
      // Get authentication status
      const { data: sessionData } = await supabase.auth.getSession();
      console.log("Authentication status:", sessionData.session ? "Authenticated" : "Not authenticated");
      
      // Try to fetch client data from Supabase
      const response = await getClients();
      console.log("Client data received:", response);
      
      // Map and sanitize each client object to ensure required properties exist
      const sanitizedClients = response.map(client => ({
        id: client.id || crypto.randomUUID(),
        name: client.name || "Unnamed Client",
        prefix: client.prefix,
        contact_person: client.contact_person,
        phone: client.phone
      }));
      
      setClients(sanitizedClients);
      console.log("Clients processed:", sanitizedClients.length, "items");
    } catch (error) {
      console.error("Error loading clients:", error);
      setClients([]);
      setError("Failed to load clients from database.");
    } finally {
      setLoading(false);
      setReady(true);
    }
  }, []);
  
  // Fetch clients - with better error handling and type safety
  useEffect(() => {
    let isMounted = true;
    
    const initClients = async () => {
      if (!isMounted) return;
      await loadClients();
    };
    
    initClients();
    
    // Setup real-time subscription to clients changes
    const setupRealtimeSubscription = async () => {
      try {
        const { data: sessionData } = await supabase.auth.getSession();
        if (!sessionData.session) {
          console.log("No session found, skipping realtime subscription");
          return null;
        }
        
        // Create a channel for realtime updates
        console.log("Setting up realtime subscription for clients table");
        
        const channel = supabase
          .channel('clients-changes')
          .on(
            'postgres_changes',
            { event: '*', schema: 'public', table: 'clients' },
            (payload) => {
              console.log('Client data changed:', payload);
              if (isMounted) {
                loadClients(); // Refresh the client list when changes occur
              }
            }
          )
          .subscribe();
          
        return () => {
          console.log("Cleaning up realtime subscription");
          supabase.removeChannel(channel);
        };
      } catch (error) {
        console.error("Error setting up real-time subscription:", error);
        return null;
      }
    };
    
    let cleanupFunction: (() => void) | null = null;
    
    setupRealtimeSubscription().then(cleanup => {
      cleanupFunction = cleanup;
    });
    
    return () => {
      isMounted = false;
      if (cleanupFunction) {
        cleanupFunction();
      }
    };
  }, [loadClients]);

  return { clients, loading, error, ready };
}
