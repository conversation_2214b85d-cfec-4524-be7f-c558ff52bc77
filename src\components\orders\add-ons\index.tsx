
import { UseFormReturn } from "react-hook-form";
import { OrderFormValues } from "../OrderFormTypes";
import { TestTube } from "lucide-react";
import { DetergentSection } from "./DetergentSection";
import { ConditionerSection } from "./ConditionerSection";
import { LegacyFieldsSection } from "./LegacyFieldsSection";

interface AddOnsProps {
  form: UseFormReturn<OrderFormValues>;
}

export function AddOns({ form }: AddOnsProps) {
  return (
    <div className="space-y-5">
      <h3 className="font-semibold text-lg flex items-center">
        <TestTube className="mr-2 h-5 w-5 text-laundry-blue" />
        Add-ons & Treatments
      </h3>
      
      <DetergentSection form={form} />
      <ConditionerSection form={form} />
      <LegacyFieldsSection form={form} />
    </div>
  );
}
