
import { Badge } from "@/components/ui/badge";
import { Order } from "@/types";

interface OrderDetailsProps {
  order: Order;
  formatCurrency: (value: number) => string;
  isClientOrder: boolean;
}

export function OrderDetails({ order, formatCurrency, isClientOrder }: OrderDetailsProps) {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <h4 className="font-medium mb-2">Order Details</h4>
          <dl className="grid grid-cols-2 gap-1 text-sm">
            <dt className="text-muted-foreground">Order ID:</dt>
            <dd>{order.id}</dd>
            {order.reference_code && order.reference_code !== order.id && (
              <>
                <dt className="text-muted-foreground">Reference Code:</dt>
                <dd>{order.reference_code}</dd>
              </>
            )}
            <dt className="text-muted-foreground">Order Date:</dt>
            <dd>{order.orderDate}</dd>
            <dt className="text-muted-foreground">Delivery Date:</dt>
            <dd>{order.deliveryDate}</dd>
            <dt className="text-muted-foreground">Status:</dt>
            <dd>{order.status}</dd>
            <dt className="text-muted-foreground">Customer Type:</dt>
            <dd>{isClientOrder ? "Client" : "Walk-in"}</dd>
          </dl>
        </div>
        
        <div>
          <h4 className="font-medium mb-2">Payment Information</h4>
          <dl className="grid grid-cols-2 gap-1 text-sm">
            <dt className="text-muted-foreground">Total:</dt>
            <dd>{formatCurrency(order.amount)}</dd>
            <dt className="text-muted-foreground">Paid:</dt>
            <dd>{formatCurrency(order.paidAmount)}</dd>
            <dt className="text-muted-foreground">Balance:</dt>
            <dd>{formatCurrency(order.amount - order.paidAmount)}</dd>
          </dl>
        </div>
      </div>
    </div>
  );
}
