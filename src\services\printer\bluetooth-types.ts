
// Type definitions for Web Bluetooth API
export interface BluetoothDevice {
  id: string;
  name?: string;
  gatt?: BluetoothRemoteGATTServer;
  addEventListener(type: string, listener: EventListener): void;
  removeEventListener(type: string, listener: EventListener): void;
}

export interface BluetoothRemoteGATTServer {
  device: BluetoothDevice;
  connected: boolean;
  connect(): Promise<BluetoothRemoteGATTServer>;
  disconnect(): void;
  getPrimaryService(serviceUuid: string): Promise<BluetoothRemoteGATTService>;
}

export interface BluetoothRemoteGATTService {
  uuid: string;
  device: BluetoothDevice;
  getCharacteristic(characteristicUuid: string): Promise<BluetoothRemoteGATTCharacteristic>;
}

export interface BluetoothRemoteGATTCharacteristic {
  uuid: string;
  service: BluetoothRemoteGATTService;
  properties: {
    broadcast: boolean;
    read: boolean;
    writeWithoutResponse: boolean;
    write: boolean;
    notify: boolean;
    indicate: boolean;
    authenticatedSignedWrites: boolean;
    reliableWrite: boolean;
    writableAuxiliaries: boolean;
  };
  writeValue(value: BufferSource): Promise<void>;
  readValue(): Promise<DataView>;
}

// Extend navigator interface
export interface WebBluetoothNavigator extends Navigator {
  bluetooth?: {
    requestDevice(options: {
      filters?: Array<{ services?: string[]; namePrefix?: string }>;
      optionalServices?: string[];
    }): Promise<BluetoothDevice>;
  };
}

// Utility function to get a typed navigator
export function getBluetoothNavigator(): WebBluetoothNavigator {
  return navigator as WebBluetoothNavigator;
}
