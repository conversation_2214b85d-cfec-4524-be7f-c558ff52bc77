
import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Package2, Loader2 } from "lucide-react";
import { Order, LineItem } from "@/types";
import { useToast } from "@/hooks/use-toast";
import { ItemsTable } from "./items/ItemsTable";
import { OrderTotalSummary } from "./items/OrderTotalSummary";
import { updateOrderItems } from "@/services/orders/mutations/updateOrderItems";
import { 
  recalculateLineItemTotals, 
  calculateOrderTotal 
} from "@/utils/orderCalculations";
import { useOrder } from "@/contexts/OrderContext";

interface EditItemsDialogProps {
  order: Order | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onItemsUpdated: () => void;
}

export function EditItemsDialog({
  order,
  open,
  onOpenChange,
  onItemsUpdated
}: EditItemsDialogProps) {
  const [items, setItems] = useState<LineItem[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const { dispatch, refreshOrder } = useOrder();
  
  // Reset and initialize items when order changes or dialog opens
  useEffect(() => {
    if (order && open) {
      console.log("EditItemsDialog - Initializing items from order:", order.id);
      
      // Create a deep copy of the items to avoid reference issues
      const itemsCopy = order.lineItems ? JSON.parse(JSON.stringify(order.lineItems)) : [];
      console.log("Items loaded:", itemsCopy);
      setItems(itemsCopy);
    }
  }, [order, open]);
  
  const handleAddItem = () => {
    const newItem: LineItem = {
      id: `temp-${Date.now()}`,
      name: "",
      quantity: 1,
      unitPrice: 0,
      total: 0,
      treatmentDescription: ""
    };
    
    setItems([...items, newItem]);
  };
  
  const handleRemoveItem = (id: string) => {
    setItems(items.filter(item => item.id !== id));
  };
  
  const handleItemChange = (id: string, field: keyof LineItem, value: any) => {
    setItems(items.map(item => {
      if (item.id === id) {
        return { ...item, [field]: value };
      }
      return item;
    }));
  };
  
  const calculateDialogOrderTotal = () => {
    return calculateOrderTotal(items);
  };
  
  const handleSubmit = async () => {
    if (!order) return;
    
    setIsSubmitting(true);
    
    try {
      console.log("Submitting updated order items:", items);
      
      // Ensure all items have valid data
      const validItems = items.filter(item => 
        item.name && 
        item.quantity > 0 && 
        item.unitPrice >= 0
      );
      
      console.log("Valid items to be saved:", validItems);
      
      // Pre-calculate totals to ensure they're correct
      const itemsWithCorrectTotals = recalculateLineItemTotals(validItems);
      
      console.log("Items with recalculated totals:", itemsWithCorrectTotals);
      
      const result = await updateOrderItems(order, itemsWithCorrectTotals);
      
      if (!result.success) {
        throw new Error("Failed to update order items");
      }
      
      toast({
        title: "Items Updated",
        description: `Order items have been updated successfully.`,
      });
      
      // Update the order context if we have updated data
      if (result.updatedOrder) {
        dispatch({ 
          type: 'UPDATE_ITEMS', 
          payload: result.updatedOrder.lineItems || [] 
        });
        
        if (result.updatedOrder.amount) {
          dispatch({ 
            type: 'UPDATE_AMOUNT', 
            payload: result.updatedOrder.amount 
          });
        }
      }
      
      // Refresh the full order data
      await refreshOrder();
      
      // Call the parent's update handler to trigger a refresh
      onItemsUpdated();
      
      // Close the dialog
      onOpenChange(false);
    } catch (error) {
      console.error("Failed to update order items:", error);
      toast({
        title: "Update Failed",
        description: "Could not update order items",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  if (!order) return null;
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Package2 className="mr-2 h-5 w-5" />
            Edit Order Items
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          <ItemsTable 
            items={items}
            clientId={order.clientId}
            onRemoveItem={handleRemoveItem}
            onItemChange={handleItemChange}
          />
          
          <OrderTotalSummary 
            total={calculateDialogOrderTotal()}
            onAddItem={handleAddItem}
          />
        </div>
        
        <DialogFooter className="mt-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button 
            onClick={handleSubmit} 
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : 'Save Changes'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
