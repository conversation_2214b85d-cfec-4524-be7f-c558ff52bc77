
import { useState, useEffect } from "react";
import { getClientItems } from "@/services/clientItem";
import { ClientItem } from "@/services/clientItem";

export function useClientItems(clientId: string | undefined) {
  const [items, setItems] = useState<ClientItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (!clientId) {
      setItems([]);
      return;
    }

    const fetchItems = async () => {
      setIsLoading(true);
      try {
        console.log("Fetching client items for clientId:", clientId);
        const fetchedItems = await getClientItems(clientId);
        console.log("Fetched items with original prices:", fetchedItems);
        
        // Don't set default values for unit_price - use exactly what comes from the database
        const validItems = fetchedItems.map(item => ({
          ...item,
          id: item.id || "",         // Ensure id is never undefined
          name: item.name || "",     // Ensure name is never undefined
          unit_price: item.unit_price // Keep the original unit_price from database
        }));
        
        setItems(validItems);
      } catch (error) {
        console.error("Failed to fetch client items:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchItems();
  }, [clientId]);

  return { items, isLoading };
}
