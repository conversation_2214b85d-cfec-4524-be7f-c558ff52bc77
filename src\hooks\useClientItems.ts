
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { ClientItem } from "@/services/clientItem/types";

interface UseClientItemsResult {
  items: ClientItem[];
  isLoading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
}

export function useClientItems(clientId: string): UseClientItemsResult {
  const [items, setItems] = useState<ClientItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchClientItems = async () => {
    // Don't fetch if we don't have a valid client ID
    if (!clientId) {
      setItems([]);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      console.log("Fetching items for client:", clientId);
      
      const { data, error } = await supabase
        .from("client_items")
        .select("*")
        .eq("client_id", clientId);

      if (error) {
        throw error;
      }

      console.log(`Found ${data?.length || 0} items for client ${clientId}`);
      setItems(data || []);
    } catch (err) {
      console.error("Error fetching client items:", err);
      setError(err instanceof Error ? err : new Error("Failed to fetch client items"));
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchClientItems();
  }, [clientId]);

  return { items, isLoading, error, refetch: fetchClientItems };
}
