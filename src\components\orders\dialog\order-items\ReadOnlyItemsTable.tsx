
import { Order, LineItem } from "@/types";
import { getTreatmentPrice } from "@/utils/orderCalculations";

interface ReadOnlyItemsTableProps {
  lineItems: LineItem[];
  order: Order;
}

export function ReadOnlyItemsTable({ lineItems, order }: ReadOnlyItemsTableProps) {
  return (
    <div className="border rounded-md">
      <table className="w-full">
        <thead className="bg-muted/50">
          <tr>
            <th className="text-left p-2">Item</th>
            <th className="text-center p-2">Quantity</th>
            <th className="text-right p-2">Unit Price</th>
            <th className="text-right p-2">Treatment</th>
            <th className="text-right p-2">Total</th>
          </tr>
        </thead>
        <tbody>
          {lineItems.length === 0 ? (
            <tr>
              <td colSpan={5} className="p-4 text-center text-muted-foreground">
                No items added to this order.
              </td>
            </tr>
          ) : (
            lineItems.map((item, index) => {
              const basePricePerItem = item.unitPrice || 0;
              const treatmentCost = getTreatmentPrice(item.treatmentDescription);
              
              // Get base treatment display info
              let treatmentDisplay = item.treatmentDescription || 'None';
              
              // Format special treatments display
              let specialTreatments = [];
              if (item.treatments?.useStainRemoval) {
                specialTreatments.push('+Stain');
              }
              if (item.treatments?.useBeachTreatment) {
                specialTreatments.push('+Bleach');
              }
              
              // If we have special treatments and the base treatment is 'None', 
              // just show the special treatments
              if (specialTreatments.length > 0) {
                if (treatmentDisplay === 'None') {
                  treatmentDisplay = specialTreatments.join(' ');
                } else {
                  treatmentDisplay += ` (${specialTreatments.join(' ')})`;
                }
              }
                
              return (
                <tr key={`${item.id}-${index}`} className="border-t">
                  <td className="p-2">{item.name}</td>
                  <td className="p-2 text-center">{item.quantity || 1}</td>
                  <td className="p-2 text-right">₱ {basePricePerItem.toFixed(2)}</td>
                  <td className="p-2 text-right">
                    <span className={specialTreatments.length > 0 ? 'text-amber-600 font-medium' : ''}>
                      {treatmentDisplay}
                    </span>
                    {treatmentCost > 0 && (
                      <span className="text-xs ml-1 font-medium text-amber-600">
                        (+₱{treatmentCost.toFixed(2)})
                      </span>
                    )}
                  </td>
                  <td className="p-2 text-right">₱ {(item.total || 0).toFixed(2)}</td>
                </tr>
              );
            })
          )}
          
          {/* Display summary row if there are treatments or add-ons */}
          {lineItems.length > 0 || order.useDetergent || order.useFabricConditioner || order.useStainRemover || order.useBleach ? (
            <tr className="border-t bg-muted/30">
              <td colSpan={4} className="text-right p-2 font-medium">Total:</td>
              <td className="p-2 text-right font-medium">₱ {(order.amount || 0).toFixed(2)}</td>
            </tr>
          ) : null}
        </tbody>
      </table>
    </div>
  );
}
