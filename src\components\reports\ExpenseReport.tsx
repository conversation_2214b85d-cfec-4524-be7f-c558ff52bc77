
import { useState, useRef } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Download, FileText, Printer } from 'lucide-react';
import { DateRange } from 'react-day-picker';
import { format, subMonths } from 'date-fns';
import { DateRangeFilter } from '@/components/accounting/dashboard/DateRangeFilter';
import { useReactToPrint } from '@/hooks/use-print';
import { toast } from 'sonner';
import { Cell, Legend, Pie, PieChart, ResponsiveContainer, Tooltip } from 'recharts';
import { useExpenseReportData } from '@/hooks/useExpenseReportData';
import { Skeleton } from '@/components/ui/skeleton';

interface ExpenseReportProps {
  clientId?: string;
}

export function ExpenseReport({ clientId }: ExpenseReportProps) {
  const printRef = useRef<HTMLDivElement>(null);
  const [isPrinting, setIsPrinting] = useState(false);
  const [dateRange, setDateRange] = useState<DateRange>({
    from: subMonths(new Date(), 1),
    to: new Date(),
  });
  
  // Use our custom hook to get real expense data
  const { isLoading, expenseData, summaryData } = useExpenseReportData(dateRange);
  
  // Use our custom hook for printing
  const handlePrint = useReactToPrint({
    content: () => printRef.current,
    documentTitle: `Expense Report - ${format(new Date(), 'yyyy-MM-dd')}`,
    onBeforePrint: () => setIsPrinting(true),
    onAfterPrint: () => {
      setIsPrinting(false);
      toast.success("Report printed successfully");
    },
    onPrintError: () => {
      setIsPrinting(false);
      toast.error("Printing failed. Please try again");
    }
  });

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82ca9d', '#8dd1e1'];
  
  // Date range presets
  const datePresets = [
    {
      label: 'This Month',
      getValue: () => ({
        from: new Date(new Date().setDate(1)),
        to: new Date(),
      }),
    },
    {
      label: 'Last Month',
      getValue: () => ({
        from: subMonths(new Date(), 1),
        to: new Date(),
      }),
    },
    {
      label: 'Last 3 Months',
      getValue: () => ({
        from: subMonths(new Date(), 3),
        to: new Date(),
      }),
    }
  ];

  // Function to handle CSV export
  const handleExportCSV = () => {
    if (expenseData.length === 0) {
      toast.error("No data to export");
      return;
    }

    // Create CSV content
    let csvContent = 'data:text/csv;charset=utf-8,';
    
    // Add headers
    csvContent += 'Date,Description,Category,Payment Method,Amount\n';
    
    // Add rows
    expenseData.forEach(expense => {
      csvContent += `${format(new Date(expense.date), 'yyyy-MM-dd')},${expense.description},${expense.category},${expense.paymentMethod},${expense.amount}\n`;
    });
    
    // Create download link
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement('a');
    link.setAttribute('href', encodedUri);
    link.setAttribute('download', `Expense_Report_${format(new Date(), 'yyyy-MM-dd')}.csv`);
    document.body.appendChild(link);
    
    // Trigger download
    link.click();
    document.body.removeChild(link);
    
    toast.success('CSV file downloaded successfully');
  };

  return (
    <div className="space-y-6" ref={printRef}>
      <Card className="print:hidden">
        <CardHeader className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 pb-3">
          <div>
            <CardTitle>Expense Report</CardTitle>
            <CardDescription>Overview of expense breakdown</CardDescription>
          </div>
          <div className="flex flex-col md:flex-row items-start md:items-center gap-4">
            <DateRangeFilter 
              dateRange={dateRange}
              onDateRangeChange={setDateRange}
              presets={datePresets}
            />
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={handlePrint}
                disabled={isPrinting}
              >
                <Printer className="mr-1 h-4 w-4" />
                Print
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={handleExportCSV}
                disabled={isLoading}
              >
                <Download className="mr-1 h-4 w-4" />
                CSV
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>
      
      {/* Summary Stats */}
      <div className="grid md:grid-cols-1 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Total Expenses</CardDescription>
            {isLoading ? (
              <Skeleton className="h-9 w-36" />
            ) : (
              <CardTitle className="text-3xl font-bold">
                ₱{summaryData.totalExpenses.toLocaleString()}
              </CardTitle>
            )}
          </CardHeader>
        </Card>
      </div>
      
      {/* Expense Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Expense Distribution</CardTitle>
          <CardDescription>
            {dateRange.from && dateRange.to ? (
              <>
                From {format(dateRange.from, 'MMMM d, yyyy')} to {format(dateRange.to, 'MMMM d, yyyy')}
              </>
            ) : (
              'Select a date range to filter data'
            )}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="h-80">
              <Skeleton className="h-full w-full" />
            </div>
          ) : summaryData.expensesByCategory.length > 0 ? (
            <div className="h-80 flex justify-center">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={summaryData.expensesByCategory}
                    cx="50%"
                    cy="50%"
                    labelLine={true}
                    label={({ name, percentage }) => `${name}: ${percentage.toFixed(1)}%`}
                    outerRadius={100}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {summaryData.expensesByCategory.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => `₱${Number(value).toLocaleString()}`} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
          ) : (
            <div className="flex items-center justify-center h-80 border rounded text-muted-foreground">
              No expenses found for the selected date range
            </div>
          )}
        </CardContent>
      </Card>
      
      {/* Expense Data Table */}
      <Card>
        <CardHeader>
          <CardTitle>Expense Details</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="space-y-4">
              {Array.from({ length: 5 }).map((_, i) => (
                <Skeleton key={i} className="h-12 w-full" />
              ))}
            </div>
          ) : expenseData.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4">Date</th>
                    <th className="text-left py-3 px-4">Description</th>
                    <th className="text-left py-3 px-4">Category</th>
                    <th className="text-left py-3 px-4">Payment Method</th>
                    <th className="text-right py-3 px-4">Amount</th>
                  </tr>
                </thead>
                <tbody>
                  {expenseData.map((expense) => (
                    <tr key={expense.id} className="border-b hover:bg-slate-50">
                      <td className="py-3 px-4">{format(new Date(expense.date), 'MMM dd, yyyy')}</td>
                      <td className="py-3 px-4">{expense.description}</td>
                      <td className="py-3 px-4 capitalize">{expense.category}</td>
                      <td className="py-3 px-4">{expense.paymentMethod}</td>
                      <td className="py-3 px-4 text-right font-medium">₱{expense.amount.toLocaleString()}</td>
                    </tr>
                  ))}
                </tbody>
                <tfoot>
                  <tr className="font-semibold bg-slate-50">
                    <td className="py-3 px-4" colSpan={4}>Total</td>
                    <td className="py-3 px-4 text-right">₱{summaryData.totalExpenses.toLocaleString()}</td>
                  </tr>
                </tfoot>
              </table>
            </div>
          ) : (
            <div className="text-center py-8 border rounded">
              <p className="text-muted-foreground">No expenses found for the selected date range</p>
            </div>
          )}
        </CardContent>
      </Card>
      
      {/* Category Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle>Category Breakdown</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="space-y-4">
              {Array.from({ length: 5 }).map((_, i) => (
                <Skeleton key={i} className="h-12 w-full" />
              ))}
            </div>
          ) : summaryData.expensesByCategory.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4">Category</th>
                    <th className="text-right py-3 px-4">Amount</th>
                    <th className="text-right py-3 px-4">Percentage</th>
                  </tr>
                </thead>
                <tbody>
                  {summaryData.expensesByCategory.map((category, index) => (
                    <tr key={index} className="border-b hover:bg-slate-50">
                      <td className="py-3 px-4">{category.name}</td>
                      <td className="py-3 px-4 text-right font-medium">₱{category.value.toLocaleString()}</td>
                      <td className="py-3 px-4 text-right">{category.percentage.toFixed(1)}%</td>
                    </tr>
                  ))}
                </tbody>
                <tfoot>
                  <tr className="font-semibold bg-slate-50">
                    <td className="py-3 px-4">Total</td>
                    <td className="py-3 px-4 text-right">₱{summaryData.totalExpenses.toLocaleString()}</td>
                    <td className="py-3 px-4 text-right">100%</td>
                  </tr>
                </tfoot>
              </table>
            </div>
          ) : (
            <div className="text-center py-8 border rounded">
              <p className="text-muted-foreground">No expense data available for category breakdown</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
