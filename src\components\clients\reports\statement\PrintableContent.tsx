
import { OrderSummary } from '@/hooks/orders/reports/types';
import { Client } from '@/services/clients/types';
import { formatCurrency } from '@/lib/formatters';

interface PrintableContentProps {
  client: Client | null;
  orders: OrderSummary[];
  summary: {
    totalOrders: number;
    totalAmount: number;
    totalPaid: number;
    totalPayable: number;
    overdueOrdersCount: number;
    paidOrders: OrderSummary[];
    unpaidOrders: OrderSummary[];
    overdueOrders: OrderSummary[];
  };
  reportRef: React.RefObject<HTMLDivElement>;
}

export function PrintableContent({ client, orders, summary, reportRef }: PrintableContentProps) {  
  return (
    <div ref={reportRef} className="hidden print:block p-8">
      <div className="text-center mb-8">
        <h1 className="text-2xl font-bold">Statement of Account</h1>
      </div>
      <div className="mb-6">
        <p><strong>Date:</strong> {new Date().toLocaleDateString()}</p>
      </div>
      
      <table className="w-full border-collapse mb-6">
        <thead>
          <tr className="border-b-2 border-gray-400">
            <th className="text-left py-2">Reference</th>
            <th className="text-left py-2">Date</th>
            <th className="text-right py-2">Amount</th>
            <th className="text-right py-2">Paid</th>
            <th className="text-right py-2">Balance</th>
          </tr>
        </thead>
        <tbody>
          {orders.map((order) => (
            <tr key={order.id} className="border-b border-gray-300">
              <td className="py-2">{order.referenceCode || '-'}</td>
              <td className="py-2">{order.orderDate}</td>
              <td className="py-2 text-right">{formatCurrency(order.amount)}</td>
              <td className="py-2 text-right">{formatCurrency(order.paidAmount)}</td>
              <td className="py-2 text-right">{formatCurrency(order.amount - order.paidAmount)}</td>
            </tr>
          ))}
        </tbody>
        <tfoot>
          <tr className="border-t-2 border-gray-400 font-bold">
            <td colSpan={3} className="py-2">Total</td>
            <td className="py-2 text-right">{formatCurrency(summary.totalPaid)}</td>
            <td className="py-2 text-right">{formatCurrency(summary.totalPayable)}</td>
          </tr>
        </tfoot>
      </table>
      
      <div className="mt-8 text-sm">
        <p>Thank you for your business. Please contact us with any questions regarding this statement.</p>
        <p className="mt-4">CMC Laundry Services - (02) 123-4567</p>
      </div>
    </div>
  );
}
