
import { Order } from "@/types";
import { OrderStatusBadge } from "@/components/orders/OrderStatusBadge";
import { Button } from "@/components/ui/button";
import { Eye, CreditCard } from "lucide-react";
import { Card, CardContent, CardHeader } from "@/components/ui/card";

interface OrderCardProps {
  order: Order;
  onAddPayment?: (order: Order) => void;
  onView?: (order: Order) => void;
}

export function OrderCard({ order, onAddPayment, onView }: OrderCardProps) {
  const handleViewClick = () => {
    if (onView) {
      onView(order);
    }
  };
  
  const handleAddPaymentClick = () => {
    if (onAddPayment) {
      onAddPayment(order);
    }
  };
  
  // Format dates properly
  const formatDate = (dateStr?: string) => {
    if (!dateStr) return 'N/A';
    
    try {
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) return dateStr;
      
      return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      }).format(date);
    } catch (e) {
      return dateStr;
    }
  };
  
  return (
    <Card className="bg-white border border-gray-100 shadow-sm">
      <CardHeader className="py-3 px-4 border-b flex flex-row items-center justify-between bg-gray-50">
        <div className="flex flex-col">
          <div className="text-sm font-medium">{order.id}</div>
          {order.reference_code && order.reference_code !== order.id && (
            <div className="text-xs text-muted-foreground">Ref: {order.reference_code}</div>
          )}
        </div>
        <OrderStatusBadge status={order.status || 'processing'} />
      </CardHeader>
      <CardContent className="py-3 px-4">
        <div className="grid grid-cols-2 gap-2 text-sm mb-3">
          <div>
            <div className="text-muted-foreground text-xs">Customer</div>
            <div>{order.customer?.name || 'Unknown'}</div>
          </div>
          <div>
            <div className="text-muted-foreground text-xs">Date</div>
            <div>{formatDate(order.orderDate)}</div>
          </div>
          <div>
            <div className="text-muted-foreground text-xs">Amount</div>
            <div>₱ {(order.amount || 0).toFixed(2)}</div>
          </div>
          <div>
            <div className="text-muted-foreground text-xs">Paid</div>
            <div>₱ {(order.paidAmount || 0).toFixed(2)}</div>
          </div>
        </div>
        
        {/* Display service or items summary */}
        {(order.lineItems && order.lineItems.length > 0) ? (
          <div className="text-xs text-muted-foreground mb-2">
            Items: {order.lineItems.length} ({order.lineItems.reduce((sum, item) => sum + item.quantity, 0)} pieces)
          </div>
        ) : order.serviceType ? (
          <div className="text-xs text-muted-foreground mb-2">
            {order.serviceType.replace(/_/g, ' ')} service
            {order.numberOfPieces ? ` · ${order.numberOfPieces} pieces` : ''}
            {order.weightKilos ? ` · ${order.weightKilos} kg` : ''}
          </div>
        ) : null}
        
        <div className="flex justify-between items-center mt-2">
          <Button 
            variant="outline" 
            size="sm"
            onClick={handleAddPaymentClick}
            className="text-xs"
          >
            <CreditCard className="h-3 w-3 mr-1" /> Add Payment
          </Button>
          <Button 
            variant="outline" 
            size="sm"
            onClick={handleViewClick}
            className="text-xs"
          >
            <Eye className="h-3 w-3 mr-1" /> View
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
