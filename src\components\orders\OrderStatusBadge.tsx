
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";

interface OrderStatusBadgeProps {
  status: string;
  className?: string;
}

export function OrderStatusBadge({ status, className }: OrderStatusBadgeProps) {
  // Get status display name
  const getStatusDisplayName = (status: string) => {
    switch (status) {
      case 'pending': return 'Pending';
      case 'for_pickup': return 'For Pickup';
      case 'pickup_complete': return 'Pickup Complete';
      case 'processing': return 'Processing';
      case 'for_treatment': return 'For Treatment';
      case 'hard_stain': return 'Hard Stain';
      case 'partial_delivery': return 'Partial Delivery';
      case 'fulfilled': return 'Delivery Complete';
      case 'cancelled': return 'Cancelled';
      default: return status.charAt(0).toUpperCase() + status.slice(1).replace(/_/g, ' ');
    }
  };

  // Get badge color based on status
  const getStatusBadgeStyle = (status: string) => {
    switch (status) {
      case 'pending':
        return "bg-gray-100 text-gray-800 border-gray-300";
      case 'for_pickup':
        return "bg-blue-100 text-blue-800 border-blue-300";
      case 'pickup_complete':
        return "bg-green-100 text-green-800 border-green-300";
      case 'processing':
        return "bg-indigo-100 text-indigo-800 border-indigo-300";
      case 'for_treatment':
        return "bg-orange-100 text-orange-800 border-orange-300";
      case 'hard_stain':
        return "bg-red-100 text-red-800 border-red-300";
      case 'partial_delivery':
        return "bg-amber-100 text-amber-800 border-amber-300";
      case 'fulfilled':
        return "bg-purple-100 text-purple-800 border-purple-300";
      case 'cancelled':
        return "bg-rose-100 text-rose-800 border-rose-300";
      default:
        return "bg-gray-100 text-gray-800 border-gray-300";
    }
  };

  return (
    <Badge className={cn("uppercase", getStatusBadgeStyle(status), className)}>
      {getStatusDisplayName(status)}
    </Badge>
  );
}
