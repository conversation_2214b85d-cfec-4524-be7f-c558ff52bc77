
import { Order } from "@/types";

interface OrderDetailsProps {
  order: Order;
}

export function OrderDetails({ order }: OrderDetailsProps) {
  return (
    <div className="space-y-2">
      <h4 className="text-sm font-medium">Order Details</h4>
      <div className="text-sm">
        <div>
          <strong>Order ID:</strong> {order.id}
          {order.reference_code && order.reference_code !== order.id && (
            <span className="text-muted-foreground ml-1">(Ref: {order.reference_code})</span>
          )}
        </div>
        <div>
          <strong>Order Date:</strong> {order.orderDate}
        </div>
        <div>
          <strong>Delivery Date:</strong> {order.deliveryDate || "N/A"}
        </div>
        <div>
          <strong>Payment Status:</strong> {order.paidAmount >= order.amount ? "Paid" : "Pending"}
        </div>
        {order.serviceType && (
          <div>
            <strong>Service Type:</strong> {order.serviceType.replace(/_/g, ' ')}
          </div>
        )}
        <div>
          <strong>Customer Type:</strong> {order.customerType === "client" ? "Client" : "Walk-in"}
        </div>
      </div>
    </div>
  );
}
