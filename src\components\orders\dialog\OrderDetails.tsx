import { Order } from "@/types";
import { SERVICE_TYPES } from "../pricing/constants";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
interface OrderDetailsProps {
  order: Order;
}
export function OrderDetails({
  order
}: OrderDetailsProps) {
  const isClientOrder = order.customerType === 'client';

  // Format service type for display
  const getServiceTypeDisplay = (serviceType?: string) => {
    if (!serviceType) return "Standard Service";

    // Convert snake_case to Title Case with proper spacing
    return serviceType.replace(/_/g, ' ').replace(/\w\S*/g, txt => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase());
  };

  // Check if it's a dry cleaning service
  const isDryCleaningService = order.serviceType === SERVICE_TYPES.DRY_CLEANING || order.isDryCleaning;

  // Count dry cleaning items if available
  const totalDryCleaningItems = isDryCleaningService && order.dryCleaningItems ? order.dryCleaningItems.reduce((sum, item) => sum + (item.quantity || 0), 0) : 0;

  // Format currency
  const formatCurrency = (amount: number) => {
    return `₱${amount.toFixed(2)}`;
  };

  // Format item type for display
  const formatItemType = (type: string) => {
    return type.replace(/_/g, ' ').replace(/\w\S*/g, txt => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase());
  };

  // Debug log for dry cleaning data
  console.log("OrderDetails component - Dry cleaning data:", {
    orderId: order.id,
    isDryCleaningService,
    isDryCleaning: order.isDryCleaning,
    serviceType: order.serviceType,
    hasDryCleaningItems: Boolean(order.dryCleaningItems && order.dryCleaningItems.length > 0),
    totalDryCleaningItems,
    dryCleaningItems: order.dryCleaningItems
  });
  return <div className="space-y-2">
      <h4 className="text-sm font-medium">Order Details</h4>
      <div className="text-sm">
        <div>
          <strong>Order ID:</strong> {order.id}
          {order.reference_code && order.reference_code !== order.id && <span className="text-muted-foreground ml-1">(Ref: {order.reference_code})</span>}
        </div>
        <div>
          <strong>Order Date:</strong> {order.orderDate}
        </div>
        <div>
          <strong>Delivery Date:</strong> {order.deliveryDate || "N/A"}
        </div>
        <div>
          <strong>Payment Status:</strong> {order.paidAmount >= order.amount ? "Paid" : "Pending"}
        </div>
        
        <div>
          <strong>Customer Type:</strong> {order.customerType === "client" ? "Client" : "Walk-in"}
        </div>
        
        {/* Display dry cleaning item details as a proper table if applicable */}
        {isDryCleaningService && order.dryCleaningItems && order.dryCleaningItems.length > 0}
      </div>
    </div>;
}