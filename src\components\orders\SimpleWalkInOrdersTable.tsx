
import React from "react";
import { Order } from "@/types";
import { 
  Table, 
  TableBody, 
  TableHeader, 
  TableRow, 
  TableHead, 
  TableCell 
} from "@/components/ui/table";
import { OrderStatusBadge } from "./OrderStatusBadge";
import { Button } from "@/components/ui/button";
import { Eye } from "lucide-react";
import { formatCurrency } from "@/lib/utils";

interface SimpleWalkInOrdersTableProps {
  orders: Order[];
  onViewOrder: (order: Order) => void;
  onOrderDeleted?: () => void;
}

export function SimpleWalkInOrdersTable({ orders, onViewOrder, onOrderDeleted }: SimpleWalkInOrdersTableProps) {
  // Fixed filtering to correctly identify walk-in orders
  const walkInOrders = orders.filter(order => order.customerType === "walk-in");
  
  // Log walk-in orders count for debugging
  console.log(`SimpleWalkInOrdersTable - Total orders: ${orders.length} Walk-in orders: ${walkInOrders.length}`);
  
  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Order ID</TableHead>
            <TableHead>Customer</TableHead>
            <TableHead>Date</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-right">Amount</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {walkInOrders.length === 0 ? (
            <TableRow>
              <TableCell colSpan={6} className="text-center py-8">
                No walk-in orders found
              </TableCell>
            </TableRow>
          ) : (
            walkInOrders.map((order) => (
              <TableRow key={order.id}>
                <TableCell>{order.id}</TableCell>
                <TableCell>
                  <div>
                    <div className="font-medium">{order.customer?.name || "Unknown Customer"}</div>
                    <div className="text-sm text-muted-foreground">{order.customer?.phone || ""}</div>
                  </div>
                </TableCell>
                <TableCell>{order.orderDate}</TableCell>
                <TableCell>
                  <OrderStatusBadge status={order.status} />
                </TableCell>
                <TableCell className="text-right">
                  {formatCurrency(order.amount)}
                </TableCell>
                <TableCell className="text-right">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onViewOrder(order)}
                  >
                    <Eye className="h-4 w-4 mr-1" />
                    View
                  </Button>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
}
