
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription, Card<PERSON>ontent, CardFooter } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { User<PERSON><PERSON>, Loader2 } from "lucide-react";
import { toast } from "sonner";
import { ClientWithUser } from "./types";
import { ClientUserTable } from "./ClientUserTable";
import { useClientBatchCreation } from "./useClientBatchCreation";
import { ClientLoginInfoGrid } from "./ClientLoginInfoGrid";

export function BatchClientUserCreation() {
  const {
    clients,
    loading,
    processing,
    successfulCreations,
    selectedCount,
    availableClientsCount,
    toggleSelectAll,
    toggleSelect,
    updateEmail,
    createUserAccounts,
  } = useClientBatchCreation();

  return (
    <div className="space-y-6">
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Create User Accounts for Clients</CardTitle>
          <CardDescription>
            Create user accounts for existing clients. Each client will receive a user account with their email address and the default password "Client1234".
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center p-6">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span className="ml-2">Loading client data...</span>
            </div>
          ) : (
            <>
              {availableClientsCount === 0 ? (
                <Alert>
                  <AlertDescription>
                    All clients with email addresses already have user accounts. If you need to add more, please update client email addresses first.
                  </AlertDescription>
                </Alert>
              ) : (
                <ClientUserTable 
                  clients={clients}
                  toggleSelectAll={toggleSelectAll}
                  toggleSelect={toggleSelect}
                  updateEmail={updateEmail}
                  processing={processing}
                  selectedCount={selectedCount}
                  availableClientsCount={availableClientsCount}
                />
              )}
            </>
          )}
        </CardContent>
        
        <CardFooter className="justify-between">
          <div className="text-sm text-muted-foreground">
            Default password for new accounts: <strong>Client1234</strong>
          </div>
          <Button
            onClick={createUserAccounts}
            disabled={selectedCount === 0 || processing || loading}
            className="ml-auto"
          >
            {processing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                <UserPlus className="mr-2 h-4 w-4" />
                Create {selectedCount} User Account{selectedCount !== 1 ? 's' : ''}
              </>
            )}
          </Button>
        </CardFooter>
      </Card>

      {successfulCreations.length > 0 && (
        <ClientLoginInfoGrid successfulCreations={successfulCreations} />
      )}
    </div>
  );
}
