
import React from "react";
import { UseFormReturn } from "react-hook-form";
import { OrderFormValues, ServiceWeight } from "../OrderFormTypes";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowUp, ArrowDown } from "lucide-react";
import { FormLabel } from "@/components/ui/form";
import { SERVICE_TYPES } from "../pricing/constants";
import { getServiceLabel } from "../price-calculation/utils";

interface ServiceWeightInputProps {
  form: UseFormReturn<OrderFormValues>;
  serviceType: string;
}

export function ServiceWeightInput({ form, serviceType }: ServiceWeightInputProps) {
  // Get the current service weights array
  const serviceWeights = form.watch("serviceWeights") || [];
  
  // Find the current weight for this service type
  const currentWeight = serviceWeights.find(sw => sw.serviceType === serviceType)?.weightKilos || 0;
  
  // Helper function to update weight for a specific service
  const updateServiceWeight = (serviceType: string, weight: number) => {
    // Create a properly typed array with explicit non-optional properties
    const updatedWeights: ServiceWeight[] = [...serviceWeights].map(sw => ({
      serviceType: sw.serviceType,
      weightKilos: sw.weightKilos
    }));
    
    const index = updatedWeights.findIndex(sw => sw.serviceType === serviceType);
    
    if (index >= 0) {
      // Update existing entry with explicit non-optional properties
      updatedWeights[index] = { 
        serviceType: serviceType, 
        weightKilos: weight 
      };
    } else {
      // Add new entry with explicit non-optional properties
      updatedWeights.push({ 
        serviceType: serviceType, 
        weightKilos: weight 
      });
    }
    
    // Update the form with properly typed ServiceWeight array
    form.setValue("serviceWeights", updatedWeights);
  };

  // Helper function to increment weight
  const incrementWeight = () => {
    updateServiceWeight(serviceType, currentWeight + 0.1);
  };

  // Helper function to decrement weight
  const decrementWeight = () => {
    if (currentWeight > 0.1) {
      updateServiceWeight(serviceType, currentWeight - 0.1);
    }
  };

  // Handle manual input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value);
    if (!isNaN(value) && value >= 0) {
      updateServiceWeight(serviceType, value);
    }
  };

  // Get minimum weight based on service type
  const getMinimumWeight = () => {
    if (serviceType === SERVICE_TYPES.WASH_DRY_FOLD || 
        serviceType === SERVICE_TYPES.WASH_DRY_PRESS || 
        serviceType === SERVICE_TYPES.WASH_DRY_FOLD_SPECIAL) {
      return 3;
    } else if (serviceType === SERVICE_TYPES.COMFORTERS || 
              serviceType === SERVICE_TYPES.TOWELS_CURTAINS_LINENS) {
      return 2;
    }
    return 0;
  };

  const minWeight = getMinimumWeight();
  const displayValue = currentWeight.toFixed(1);

  return (
    <div className="mb-4">
      <FormLabel className="text-base flex items-center">
        {getServiceLabel(serviceType)} Weight (kg)
      </FormLabel>
      <div className="flex">
        <Button 
          type="button" 
          variant="outline" 
          className="rounded-r-none h-12 px-3"
          onClick={decrementWeight}
        >
          <ArrowDown className="h-5 w-5" />
        </Button>
        <Input 
          value={displayValue}
          onChange={handleInputChange}
          inputMode="decimal" 
          pattern="[0-9]*\.?[0-9]*" 
          min="0" 
          step="0.1" 
          placeholder="0.0" 
          className="rounded-none h-12 text-center text-base"
        />
        <Button 
          type="button" 
          variant="outline" 
          className="rounded-l-none h-12 px-3"
          onClick={incrementWeight}
        >
          <ArrowUp className="h-5 w-5" />
        </Button>
      </div>
      {currentWeight < minWeight && minWeight > 0 && (
        <p className="text-sm text-amber-600 mt-1">
          * Minimum weight for this service is {minWeight}kg. You will be charged for {minWeight}kg.
        </p>
      )}
    </div>
  );
}
