
import * as React from "react";
import { Check, ChevronsUpDown } from "lucide-react";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

export type ComboboxOption = {
  value: string;
  label: string;
  price?: number;
};

interface ComboboxProps {
  options: ComboboxOption[];
  value: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  emptyMessage?: string;
  disabled?: boolean;
  className?: string;
}

export function Combobox({
  options = [],
  value,
  onValueChange,
  placeholder = "Select an option",
  emptyMessage = "No results found.",
  disabled = false,
  className,
}: ComboboxProps) {
  const [open, setOpen] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState("");
  
  // Ensure options is always a valid array with defensive programming
  const safeOptions = React.useMemo(() => {
    if (!Array.isArray(options)) {
      console.warn("Combobox received non-array options:", options);
      return [];
    }
    return options.filter(option => {
      // Validate each option has required properties
      if (!option || typeof option !== 'object') {
        console.warn("Invalid option object:", option);
        return false;
      }
      if (!('value' in option) || !('label' in option)) {
        console.warn("Option missing required properties:", option);
        return false;
      }
      return true;
    });
  }, [options]);
  
  // Find the selected option to display its label
  const selectedOption = React.useMemo(() => {
    if (!safeOptions.length) return null;
    
    const option = safeOptions.find((option) => option.value === value);
    if (!option && value && value !== "custom") {
      console.debug("Selected value not found in options:", value, safeOptions);
    }
    return option;
  }, [safeOptions, value]);

  // Filter options based on search query
  const filteredOptions = React.useMemo(() => {
    if (!safeOptions.length) return [];
    if (!searchQuery) return safeOptions;
    
    return safeOptions.filter((option) =>
      option.label.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [safeOptions, searchQuery]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          disabled={disabled}
          className={cn("w-full justify-between", className)}
          onClick={() => {
            setSearchQuery("");
            if (!open && !disabled) {
              console.debug("Opening combobox with options:", safeOptions);
            }
          }}
        >
          {selectedOption?.label || placeholder}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent 
        className="w-[var(--radix-popover-trigger-width)] p-0 bg-popover" 
        align="start"
        style={{ zIndex: 50 }}
      >
        <Command shouldFilter={false}>
          <CommandInput 
            placeholder={placeholder} 
            onValueChange={setSearchQuery}
            className="h-9"
          />
          <CommandGroup className="max-h-[200px] overflow-auto">
            {filteredOptions.length > 0 ? (
              // Always provide an array of elements to map
              filteredOptions.map((option) => (
                <CommandItem
                  key={option.value}
                  value={option.value}
                  onSelect={() => {
                    console.debug("Selected item:", option);
                    onValueChange(option.value);
                    setOpen(false);
                  }}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      value === option.value ? "opacity-100" : "opacity-0"
                    )}
                  />
                  <span className="flex-1">{option.label}</span>
                  {option.price !== undefined && (
                    <span className="ml-auto text-muted-foreground">
                      ₱{option.price.toFixed(2)}
                    </span>
                  )}
                </CommandItem>
              ))
            ) : (
              // Always ensure CommandEmpty is present to avoid undefined children
              <CommandEmpty>
                {searchQuery ? "No matches found" : emptyMessage}
              </CommandEmpty>
            )}
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
