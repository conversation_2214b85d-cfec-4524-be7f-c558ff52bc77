# Script to update app version for Google Play Store releases
param(
    [Parameter(Mandatory=$false)]
    [string]$VersionName,
    
    [Parameter(Mandatory=$false)]
    [int]$VersionCode = 0
)

Write-Host "Updating CMC Laundry POS app version..." -ForegroundColor Cyan

# Check if build.gradle exists
$buildGradlePath = ".\android\app\build.gradle"
if (-not (Test-Path -Path $buildGradlePath)) {
    Write-Host "Error: build.gradle not found at $buildGradlePath" -ForegroundColor Red
    exit 1
}

# Read current build.gradle
$buildGradle = Get-Content -Path $buildGradlePath -Raw

# Extract current version code and name
$currentVersionCode = 0
$currentVersionName = "1.0.0"

if ($buildGradle -match 'versionCode\s+(\d+)') {
    $currentVersionCode = [int]$Matches[1]
}

if ($buildGradle -match 'versionName\s+"([^"]+)"') {
    $currentVersionName = $Matches[1]
}

Write-Host "Current version: $currentVersionName ($currentVersionCode)" -ForegroundColor Yellow

# Determine new version code and name
$newVersionCode = $currentVersionCode
$newVersionName = $currentVersionName

if ($VersionCode -gt 0) {
    $newVersionCode = $VersionCode
} else {
    $newVersionCode = $currentVersionCode + 1
}

if ($VersionName) {
    $newVersionName = $VersionName
}

# Update build.gradle
$updatedBuildGradle = $buildGradle -replace 'versionCode\s+\d+', "versionCode $newVersionCode"
$updatedBuildGradle = $updatedBuildGradle -replace 'versionName\s+"[^"]+"', "versionName `"$newVersionName`""

# Save updated build.gradle
Set-Content -Path $buildGradlePath -Value $updatedBuildGradle

Write-Host "Updated version: $newVersionName ($newVersionCode)" -ForegroundColor Green
Write-Host "Now you can build a new release with: npm run android:release" -ForegroundColor Cyan
