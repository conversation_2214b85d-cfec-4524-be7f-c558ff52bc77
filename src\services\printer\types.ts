
export type PrinterStatus = 'disconnected' | 'connecting' | 'connected' | 'printing' | 'error';

export interface PrintOrderData {
  id: string;
  orderDate: string;
  customer: {
    name: string;
    phone: string;
    email?: string;
  };
  customerType: 'client' | 'walk-in';
  lineItems?: {
    name: string;
    quantity: number;
    total: number;
    treatmentDescription?: string;
  }[];
  weightKilos?: number;
  numberOfPieces?: number;
  useDetergent?: boolean;
  useFabricConditioner?: boolean;
  useStainRemover?: boolean;
  useBleach?: boolean;
  detergentType?: string;
  conditionerType?: string;
  detergentQuantity?: number;
  conditionerQuantity?: number;
  amount: number;
  paidAmount: number;
  status: string;
  subtotalBeforeVAT?: number;
  vatAmount?: number;
  notes?: string;
  serviceType?: string;
  isDryCleaning?: boolean;
  dryCleaningItems?: Array<{
    type: string;
    price: number;
    quantity: number;
    name?: string;
    total?: number;
  }>;
  deliveryDate?: string;
}
