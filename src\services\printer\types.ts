
export type PrinterStatus = 'disconnected' | 'connecting' | 'connected' | 'printing' | 'error';

export interface PrintOrderData {
  id: string;
  orderDate: string;
  customer: {
    name: string;
    phone: string;
    email?: string;
  };
  lineItems?: {
    name: string;
    quantity: number;
    total: number;
  }[];
  weightKilos?: number;
  numberOfPieces?: number;
  useDetergent?: boolean;
  useFabricConditioner?: boolean;
  useStainRemover?: boolean;
  useBleach?: boolean;
  detergentType?: string;
  conditionerType?: string;
  detergentQuantity?: number;
  conditionerQuantity?: number;
  amount: number;
  paidAmount: number;
  status: string;
  subtotalBeforeVAT?: number;
  vatAmount?: number;
  notes?: string;
  serviceType?: string;
}
