
import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Eye, Edit, Trash2 } from "lucide-react";
import { Order } from "@/types";

interface ActionButtonsProps {
  order: Order;
  onViewOrder: (order: Order) => void;
  onEditOrder?: (order: Order) => void;
  canEdit: boolean;
  isAdmin: boolean;
  onDeleteClick: () => void;
}

export function ActionButtons({ 
  order, 
  onViewOrder, 
  onEditOrder,
  canEdit,
  isAdmin,
  onDeleteClick
}: ActionButtonsProps) {
  return (
    <>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => onViewOrder(order)}
        title="View Order"
      >
        <Eye className="h-4 w-4" />
      </Button>
      
      {/* Edit button - only for admin and staff */}
      {onEditOrder && canEdit && (
        <Button
          variant="ghost"
          size="icon"
          onClick={() => onEditOrder(order)}
          title="Edit Order"
        >
          <Edit className="h-4 w-4" />
        </Button>
      )}
      
      {/* Delete button - only for admin users */}
      {isAdmin && (
        <Button
          variant="ghost"
          size="icon"
          onClick={onDeleteClick}
          title="Delete Order"
          className="text-red-500 hover:text-red-700 hover:bg-red-100"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      )}
    </>
  );
}
