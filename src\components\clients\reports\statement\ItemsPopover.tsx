
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { formatCurrency } from '@/lib/formatters';

interface Item {
  name: string;
  quantity: number;
  unitPrice: number;
  total: number;
}

interface ItemsPopoverProps {
  items: Item[];
}

export function ItemsPopover({ items }: ItemsPopoverProps) {
  if (!items || items.length === 0) {
    return <span className="text-muted-foreground">No items</span>;
  }
  
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="link" className="p-0 h-auto font-normal text-foreground">
          {items.length} {items.length === 1 ? 'item' : 'items'}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0">
        <div className="p-4 border-b">
          <h4 className="font-medium">Order Items</h4>
          <p className="text-sm text-muted-foreground">
            Details of items in this order
          </p>
        </div>
        <div className="max-h-80 overflow-auto">
          <table className="w-full">
            <thead className="bg-muted/50 sticky top-0">
              <tr>
                <th className="p-2 text-left text-xs">Item</th>
                <th className="p-2 text-right text-xs">Qty</th>
                <th className="p-2 text-right text-xs">Price</th>
                <th className="p-2 text-right text-xs">Subtotal</th>
              </tr>
            </thead>
            <tbody>
              {items.map((item, index) => (
                <tr key={index} className="border-t">
                  <td className="p-2 text-sm">{item.name}</td>
                  <td className="p-2 text-sm text-right">{item.quantity}</td>
                  <td className="p-2 text-sm text-right">{formatCurrency(item.unitPrice)}</td>
                  <td className="p-2 text-sm text-right font-medium">{formatCurrency(item.total)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </PopoverContent>
    </Popover>
  );
}
