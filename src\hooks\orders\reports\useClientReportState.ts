
import { useState, useEffect, useCallback, useRef } from 'react';
import { DateRange } from 'react-day-picker';
import { useOrdersFetch } from './useOrdersFetch';
import { useSummaryCalculator } from './useSummaryCalculator';
import { useOrdersFilter } from './useOrdersFilter';
import { OrderSummary, OrdersFilter } from './types';

export function useClientReportState(clientId: string) {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // References
  const printRef = useRef<HTMLDivElement>(null);
  
  // Get order data
  const { orders, fetchOrders, isLoading: ordersLoading } = useOrdersFetch();
  
  // Get filtering and calculation utilities
  const { calculateSummary } = useSummaryCalculator();
  const { 
    dateRange, 
    overdueFilter, 
    filteredOrders,
    updateDateFilter, 
    updateOverdueFilter, 
    applyFilters 
  } = useOrdersFilter();

  // Compute summary data based on filtered orders
  const summary = calculateSummary(filteredOrders);

  // Initialize data loading
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        await fetchOrders(clientId);
      } catch (err) {
        setError(typeof err === 'string' ? err : 'Failed to load order data');
        console.error('Error fetching orders:', err);
      } finally {
        setIsLoading(false);
      }
    };
    
    if (clientId) {
      loadData();
    }
  }, [clientId, fetchOrders]);

  // Apply filters whenever orders or filter settings change
  useEffect(() => {
    if (orders.length > 0) {
      applyFilters(orders, dateRange, overdueFilter);
    }
  }, [orders, dateRange, overdueFilter, applyFilters]);

  // Create date filter presets
  const datePresets = [
    {
      label: 'Last 30 Days',
      getValue: () => {
        const today = new Date();
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(today.getDate() - 30);
        return { from: thirtyDaysAgo, to: today };
      }
    },
    {
      label: 'This Month',
      getValue: () => {
        const today = new Date();
        const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
        return { from: firstDayOfMonth, to: today };
      }
    },
    {
      label: 'Last Month',
      getValue: () => {
        const today = new Date();
        const firstDayLastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
        const lastDayLastMonth = new Date(today.getFullYear(), today.getMonth(), 0);
        return { from: firstDayLastMonth, to: lastDayLastMonth };
      }
    }
  ];

  return {
    clientId,
    orders,
    filteredOrders,
    summary,
    isLoading,
    error,
    dateRange,
    overdueFilter,
    updateDateFilter,
    updateOverdueFilter,
    datePresets,
    printRef
  };
}
