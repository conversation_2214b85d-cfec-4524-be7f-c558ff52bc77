
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { Order, CustomerType } from "@/types";
import { createOrder as addOrder } from "../../orders";
import { createOrderNotification } from "@/services/notifications";

/**
 * Create a new order for a client
 */
export const createClientOrder = async (clientId: string, orderData: any): Promise<Order | null> => {
  try {
    console.log("Creating client order with data:", orderData);
    
    // Get client details for the order
    const { data: client } = await supabase
      .from('clients')
      .select('name, phone, prefix')
      .eq('id', clientId)
      .single();
      
    if (!client) {
      throw new Error("Client not found");
    }
    
    // Create line items from the selected items, including treatment information
    const lineItems = orderData.selectedItems.map((item: any) => {
      // Use the item's unitPrice directly - this should be from the database now
      const unitPrice = item.unitPrice;
      
      // Calculate additional costs based on treatments
      let treatmentCost = 0;
      let treatmentDescription = [];
      
      if (item.treatments) {
        // Add cost for stain removal if selected
        if (item.treatments.useStainRemoval) {
          treatmentCost += 15; // Add ₱15 for stain removal
          treatmentDescription.push("stain removal");
        }
        
        // Add cost for special detergent if selected
        if (item.treatments.detergentType !== 'none') {
          const detergentCost = item.treatments.detergentType === 'regular' ? 5 : 8;
          treatmentCost += detergentCost;
          treatmentDescription.push(`${item.treatments.detergentType} detergent`);
        }
        
        // Add cost for fabric conditioner if selected
        if (item.treatments.conditionerType !== 'none') {
          const conditionerCost = 
            item.treatments.conditionerType === 'regular' ? 5 : 8;
          treatmentCost += conditionerCost;
          treatmentDescription.push(`${item.treatments.conditionerType} conditioner`);
        }
      }
      
      // Calculate total price including treatment costs
      const totalPrice = (unitPrice + treatmentCost) * item.quantity;
      
      return {
        name: item.name,
        quantity: item.quantity,
        unitPrice: unitPrice,
        treatmentCost: treatmentCost,
        treatments: item.treatments || null,
        treatmentDescription: treatmentDescription.join(", "),
        total: totalPrice,
        instanceId: item.instanceId
      };
    });
    
    // Calculate total amount
    const totalAmount = lineItems.reduce((sum: number, item: any) => 
      sum + item.total, 0
    );
    
    // Extract and consolidate add-on information
    const useStainRemoval = lineItems.some((item: any) => 
      item.treatments?.useStainRemoval === true
    );
    
    const useDetergent = lineItems.some((item: any) => 
      item.treatments?.detergentType !== 'none'
    );
    
    const useFabricConditioner = lineItems.some((item: any) => 
      item.treatments?.conditionerType !== 'none'
    );
    
    // Prepare the order object
    const newOrder = {
      orderDate: new Date().toISOString(),
      deliveryDate: orderData.deliveryDate.toISOString(),
      customer: {
        name: client.name,
        phone: client.phone
      },
      amount: totalAmount,
      paidAmount: 0, // Initially unpaid
      status: "ready_for_pickup", // Changed from for_pickup to ready_for_pickup
      useDetergent: useDetergent,
      useFabricConditioner: useFabricConditioner,
      useStainRemover: useStainRemoval,
      customerType: "client" as CustomerType,
      clientId: clientId,
      prefix: client.prefix,
      lineItems: lineItems,
      notes: orderData.notes || ""
    };
    
    // Add the order
    const createdOrder = await addOrder(newOrder);
    
    // Create and send notification
    const notificationData = {
      title: "New Client Order",
      message: `${client.name} has placed a new order`,
      orderId: createdOrder.id,
    };
    
    try {
      // Create notification for staff members
      await createOrderNotification(
        notificationData.title,
        notificationData.message,
        createdOrder.id,
        'staff'
      );
      console.log("Order notification created");
    } catch (notificationError) {
      console.error("Failed to create notification:", notificationError);
    }
    
    return createdOrder;
  } catch (error) {
    console.error("Error creating client order:", error);
    toast("Failed to create order", {
      description: "Please try again",
      style: { backgroundColor: 'red', color: 'white' }
    });
    return null;
  }
};
