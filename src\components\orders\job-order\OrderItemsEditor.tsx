
import { useState } from "react";
import { Order, LineItem } from "@/types";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { Plus, Loader2 } from "lucide-react";
import { updateOrderItems } from "@/services/orders";
import { JobOrderItemRow } from "./JobOrderItemRow";
import { useClientItems } from "@/hooks/useClientItems";
import { useOrder } from "@/contexts/OrderContext";

interface OrderItemsEditorProps {
  order: Order;
  onOrderUpdated: () => Promise<void>;
  isDryCleaning: boolean;
}

export function OrderItemsEditor({
  order,
  onOrderUpdated,
  isDryCleaning
}: OrderItemsEditorProps) {
  const [items, setItems] = useState<LineItem[]>(order.lineItems || []);
  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();
  const { refreshOrder } = useOrder();
  
  const { items: clientItems = [] } = useClientItems(order.clientId || '');
  
  const handleAddItem = () => {
    const newItem: LineItem = {
      id: `new-${Date.now()}`,
      name: "",
      quantity: 1,
      unitPrice: 0,
      total: 0,
      treatmentDescription: ""
    };
    
    setItems([...items, newItem]);
  };
  
  const handleRemoveItem = (itemId: string) => {
    setItems(items.filter(item => item.id !== itemId));
  };
  
  const handleItemChange = (updatedItem: LineItem) => {
    setItems(items.map(item => 
      item.id === updatedItem.id ? updatedItem : item
    ));
  };
  
  const calculateTotal = (item: LineItem) => {
    return item.quantity * item.unitPrice;
  };
  
  const recalculateTotals = (items: LineItem[]): LineItem[] => {
    return items.map(item => ({
      ...item,
      total: calculateTotal(item)
    }));
  };
  
  const handleSaveItems = async () => {
    if (items.some(item => !item.name)) {
      toast({
        title: "Validation Error",
        description: "All items must have a name",
        variant: "destructive"
      });
      return;
    }
    
    setIsSaving(true);
    
    try {
      // Recalculate item totals before saving
      const itemsWithTotals = recalculateTotals(items);
      
      const { success } = await updateOrderItems(order, itemsWithTotals);
      
      if (success) {
        toast({
          title: "Items Updated",
          description: "Order items have been updated successfully"
        });
        await refreshOrder();
        await onOrderUpdated();
      } else {
        throw new Error("Failed to update items");
      }
    } catch (error) {
      console.error("Error saving items:", error);
      toast({
        title: "Save Failed",
        description: "Could not save order items",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">
          {isDryCleaning ? "Dry Cleaning Items" : "Order Items"}
        </h3>
        <Button onClick={handleAddItem} size="sm">
          <Plus className="h-4 w-4 mr-1" /> Add Item
        </Button>
      </div>
      
      <div className="border rounded-md">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Item Name
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Quantity
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Unit Price
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Treatment
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Total
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {items.length === 0 ? (
              <tr>
                <td colSpan={6} className="px-6 py-4 text-center text-sm text-gray-500">
                  No items added yet. Click "Add Item" to begin.
                </td>
              </tr>
            ) : (
              items.map(item => (
                <JobOrderItemRow
                  key={item.id}
                  item={item}
                  clientId={order.clientId}
                  clientItems={clientItems}
                  isDryCleaning={isDryCleaning}
                  onChange={handleItemChange}
                  onRemove={() => handleRemoveItem(item.id)}
                />
              ))
            )}
          </tbody>
          <tfoot>
            <tr>
              <td colSpan={4} className="px-6 py-3 text-right text-sm font-medium">
                Total:
              </td>
              <td className="px-6 py-3 text-left text-sm">
                ₱{items.reduce((sum, item) => sum + (item.total || 0), 0).toFixed(2)}
              </td>
              <td></td>
            </tr>
          </tfoot>
        </table>
      </div>
      
      <div className="flex justify-end">
        <Button onClick={handleSaveItems} disabled={isSaving}>
          {isSaving ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Saving...
            </>
          ) : (
            "Save Items"
          )}
        </Button>
      </div>
    </div>
  );
}
