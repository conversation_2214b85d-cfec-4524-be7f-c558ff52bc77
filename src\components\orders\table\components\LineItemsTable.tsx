
import { Order, LineItem } from "@/types";
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table";

interface LineItemsTableProps {
  order: Order;
  lineItems: LineItem[];
  formatCurrency: (value: number) => string;
  isClientOrder: boolean;
}

export function LineItemsTable({ order, lineItems, formatCurrency, isClientOrder }: LineItemsTableProps) {
  return (
    <div>
      <h4 className="font-medium mb-2">Items</h4>
      <div className={`bg-background rounded border p-2 ${isClientOrder ? 'border-blue-200' : 'border-green-200'}`}>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Item</TableHead>
              <TableHead className="text-right">Qty</TableHead>
              <TableHead className="text-right">Price</TableHead>
              <TableHead className="text-right">Total</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {lineItems.length === 0 ? (
              <TableRow>
                <TableCell colSpan={4} className="text-center text-muted-foreground py-4">
                  No items in this order.
                </TableCell>
              </TableRow>
            ) : (
              lineItems.map((item, idx) => (
                <TableRow key={`${item.id}-${idx}`}>
                  <TableCell>
                    {item.name}
                    {item.treatmentDescription && (
                      <div className="text-xs text-muted-foreground mt-1">
                        Treatment: {item.treatmentDescription}
                      </div>
                    )}
                  </TableCell>
                  <TableCell className="text-right">{item.quantity}</TableCell>
                  <TableCell className="text-right">
                    {formatCurrency(item.unitPrice)}
                  </TableCell>
                  <TableCell className="text-right">
                    {formatCurrency(item.total)}
                  </TableCell>
                </TableRow>
              ))
            )}
            
            {/* Show add-ons if applicable */}
            {(order.useDetergent || order.useFabricConditioner || order.useStainRemover) && (
              <TableRow>
                <TableCell colSpan={4} className="text-sm">
                  <strong>Add-ons:</strong>{' '}
                  {[
                    order.useDetergent ? `Detergent (${order.detergentType || 'Regular'})` : null,
                    order.useFabricConditioner ? `Fabric Conditioner (${order.conditionerType || 'Regular'})` : null,
                    order.useStainRemover ? 'Stain Removal' : null
                  ].filter(Boolean).join(', ')}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
