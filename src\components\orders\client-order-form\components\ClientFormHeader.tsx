
import React from "react";
import { UseFormReturn } from "react-hook-form";
import { OrderFormValues } from "../../OrderFormTypes";
import { FileText } from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { useIsMobile } from "@/hooks/use-mobile";

interface ClientFormHeaderProps {
  form: UseFormReturn<OrderFormValues>;
}

export function ClientFormHeader({ form }: ClientFormHeaderProps) {
  const isMobile = useIsMobile();
  const clientId = form.watch("clientId");
  
  return (
    <div className="pb-2">
      <h1 className={`flex items-center font-semibold ${isMobile ? 'text-xl' : 'text-2xl'} text-laundry-blue`}>
        <FileText className={`${isMobile ? 'h-5 w-5 mr-2' : 'h-6 w-6 mr-3'}`} />
        {clientId ? 'Create Client Order' : 'New Client Order'}
      </h1>
      <p className="text-muted-foreground mb-3 mt-1">
        Create a new order for a client with customized service items and options.
      </p>
      <Separator />
    </div>
  );
}
