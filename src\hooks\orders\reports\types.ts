
import { DateRange } from "react-day-picker";

export interface OrderItem {
  name: string;
  quantity: number;
  unitPrice: number;
  total: number;
}

export interface OrderSummary {
  id: string;
  referenceCode: string | null;
  orderDate: string;
  amount: number;
  paidAmount: number;
  dueDate: string;
  status: string;
  isOverdue: boolean;
  items: OrderItem[];
}

export interface OrdersSummary {
  totalOrders: number;
  totalAmount: number;
  totalPaid: number;
  totalPayable: number;
  paidOrdersCount: number;
  unpaidOrdersCount: number;
  overdueOrdersCount: number;
  paidOrders: OrderSummary[];
  unpaidOrders: OrderSummary[];
  overdueOrders: OrderSummary[];
}

export interface OrdersFilter {
  dateRange: DateRange | undefined;
  overdueOnly: boolean | null;
}
