
import React from 'react';
import { PriceBreakdown } from '../OrderFormTypes';
import { formatCurrency } from './utils';

interface TotalSectionProps {
  priceBreakdown: PriceBreakdown;
}

export function TotalSection({ priceBreakdown }: TotalSectionProps) {
  return (
    <div className="border-t pt-3">
      <div className="space-y-1">
        <div className="flex justify-between text-sm">
          <span>Subtotal:</span>
          <span>{formatCurrency(priceBreakdown.subtotal)}</span>
        </div>
        
        <div className="flex justify-between text-sm">
          <span>VAT (12%):</span>
          <span>{formatCurrency(priceBreakdown.vatAmount)}</span>
        </div>
        
        <div className="flex justify-between font-semibold text-base mt-2">
          <span>Total:</span>
          <span>{formatCurrency(priceBreakdown.totalPrice)}</span>
        </div>
      </div>
    </div>
  );
}
