
import React from 'react';
import { PriceBreakdown } from '../OrderFormTypes';

interface TotalSectionProps {
  priceBreakdown: PriceBreakdown;
}

export function TotalSection({ priceBreakdown }: TotalSectionProps) {
  return (
    <div className="border-t pt-3">
      <div className="space-y-1">
        <div className="flex justify-between text-sm">
          <span>Subtotal:</span>
          <span>₱{priceBreakdown.subtotal.toFixed(2)}</span>
        </div>
        
        <div className="flex justify-between text-sm">
          <span>VAT (12%):</span>
          <span>₱{priceBreakdown.vatAmount.toFixed(2)}</span>
        </div>
        
        <div className="flex justify-between font-semibold text-base mt-2">
          <span>Total:</span>
          <span>₱{priceBreakdown.totalPrice.toFixed(2)}</span>
        </div>
      </div>
    </div>
  );
}
