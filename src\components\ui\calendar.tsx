
import { ChevronLeft, ChevronRight } from "lucide-react";
import * as React from "react";
import { DayPicker } from "react-day-picker";

import { cn } from "@/lib/utils";
import { buttonVariants } from "@/components/ui/button";

export type CalendarProps = React.ComponentProps<typeof DayPicker>;

function Calendar({
  className,
  classNames,
  showOutsideDays = true,
  components: userComponents,
  ...props
}: CalendarProps) {
  const defaultClassNames = {
    months: "flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",
    month: "space-y-4 w-full",
    caption: "flex justify-center pt-1 relative items-center px-10",
    caption_label: "text-sm font-medium text-center flex-1",
    nav: "flex items-center absolute inset-0 justify-between px-1",
    button_previous: cn(
      buttonVariants({ variant: "outline", size: "icon" }),
      "h-7 w-7 bg-transparent p-0 opacity-70 hover:opacity-100"
    ),
    button_next: cn(
      buttonVariants({ variant: "outline", size: "icon" }),
      "h-7 w-7 bg-transparent p-0 opacity-70 hover:opacity-100"
    ),
    weekday: "size-9 p-0 text-xs font-medium text-muted-foreground/80",
    day_button:
      "relative flex size-9 items-center justify-center whitespace-nowrap rounded-lg p-0 text-foreground outline-offset-2 group-[[data-selected]:not(.range-middle)]:[transition-property:color,background-color,border-radius,box-shadow] group-[[data-selected]:not(.range-middle)]:duration-150 focus:outline-none group-data-[disabled]:pointer-events-none focus-visible:z-10 hover:bg-accent group-data-[selected]:bg-primary hover:text-foreground group-data-[selected]:text-primary-foreground group-data-[disabled]:text-foreground/30 group-data-[disabled]:line-through group-data-[outside]:text-foreground/30 group-data-[outside]:group-data-[selected]:text-primary-foreground focus-visible:outline focus-visible:outline-2 focus-visible:outline-ring/70 group-[.range-start:not(.range-end)]:rounded-e-none group-[.range-end:not(.range-start)]:rounded-s-none group-[.range-middle]:rounded-none group-data-[selected]:group-[.range-middle]:bg-accent group-data-[selected]:group-[.range-middle]:text-foreground",
    day: "group size-9 px-0 text-sm",
    range_start: "range-start",
    range_end: "range-end",
    range_middle: "range-middle",
    today:
      "*:after:pointer-events-none *:after:absolute *:after:bottom-1 *:after:start-1/2 *:after:z-10 *:after:size-[5px] *:after:-translate-x-1/2 *:after:rounded-full *:after:bg-primary *:after:shadow-[0_0_0_1px_rgba(255,255,255,0.2)] [&[data-selected]:not(.range-middle)>*]:after:bg-primary-foreground [&[data-disabled]>*]:after:bg-foreground/30 *:after:transition-colors group-data-[today]:font-semibold",
    outside: "text-muted-foreground data-selected:bg-accent/50 data-selected:text-muted-foreground opacity-50",
    past_day: "text-muted-foreground opacity-50",
    hidden: "invisible",
    week_number: "size-9 p-0 text-xs font-medium text-muted-foreground/80",
  };

  const mergedClassNames: typeof defaultClassNames = Object.keys(defaultClassNames).reduce(
    (acc, key) => ({
      ...acc,
      [key]: classNames?.[key as keyof typeof classNames]
        ? cn(
            defaultClassNames[key as keyof typeof defaultClassNames],
            classNames[key as keyof typeof classNames],
          )
        : defaultClassNames[key as keyof typeof defaultClassNames],
    }),
    {} as typeof defaultClassNames,
  );

  const defaultComponents = {
    Chevron: (props: any) => {
      if (props.orientation === "left") {
        return <ChevronLeft className="h-4 w-4" {...props} aria-hidden="true" />;
      }
      return <ChevronRight className="h-4 w-4" {...props} aria-hidden="true" />;
    },
  };

  const mergedComponents = {
    ...defaultComponents,
    ...userComponents,
  };

  // Add modifiers to highlight today and style past days
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  // Create a function to identify past dates
  const isPastDay = (day: Date) => {
    const date = new Date(day);
    date.setHours(0, 0, 0, 0);
    return date < today;
  };

  return (
    <DayPicker
      showOutsideDays={showOutsideDays}
      className={cn("w-fit pointer-events-auto touch-action-manipulation", className)}
      classNames={mergedClassNames}
      components={mergedComponents}
      modifiers={{
        today: today,
        past: { before: today }
      }}
      modifiersClassNames={{
        today: mergedClassNames.today,
        past: mergedClassNames.past_day
      }}
      {...props}
    />
  );
}
Calendar.displayName = "Calendar";

export { Calendar };
