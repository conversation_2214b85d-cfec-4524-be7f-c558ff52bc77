
import { supabase } from "@/integrations/supabase/client";
import { ADDON_PRICES, BASE_PRICE_PER_KILO, VAT_RATE, DRY_CLEANING_PRICES } from "@/components/orders/pricing/constants";

// Define types for pricing settings
export interface DatabasePricingSettings {
  baseWeightPrice: string;
  detergentRegularPrice: string;
  detergentColorPrice: string;
  conditionerRegularPrice: string;
  conditionerFreshPrice: string;
  conditionerFloralPrice: string;
  stainRemoverPrice: string;
  bleachPrice: string;
  vatRate: string;
  dryCleaningPrices?: Record<string, number>;
  specialServices?: {
    comfortersPrice: number;
    towelsCurtainsLinensPrice: number;
    minimumWeightComforters: number;
    minimumWeightSpecial: number;
  };
}

export interface PricingConstants {
  BASE_PRICE_PER_KILO: number;
  ADDON_PRICES: {
    detergent: {
      regular: number;
      color: number;
    },
    fabricConditioner: {
      regular: number;
      fresh: number;
      floral: number;
    },
    stainRemover: number;
    bleach: number;
  };
  VAT_RATE: number;
  DRY_CLEANING_PRICES?: Record<string, number>;
  SPECIAL_SERVICES?: {
    comfortersPrice: number;
    towelsCurtainsLinensPrice: number;
    minimumWeightComforters: number;
    minimumWeightSpecial: number;
  };
}

// Cache settings in memory to avoid too many database calls
let cachedSettings: PricingConstants | null = null;
let lastFetchTime = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Helper function to safely convert JSON to our settings type
export function convertToSettingsType(jsonData: any): DatabasePricingSettings {
  if (typeof jsonData !== 'object' || jsonData === null) {
    console.error("Invalid settings data format:", jsonData);
    // Return default values if data is invalid
    return {
      baseWeightPrice: BASE_PRICE_PER_KILO.toString(),
      detergentRegularPrice: ADDON_PRICES.detergent.regular.toString(),
      detergentColorPrice: ADDON_PRICES.detergent.color.toString(),
      conditionerRegularPrice: ADDON_PRICES.fabricConditioner.regular.toString(),
      conditionerFreshPrice: ADDON_PRICES.fabricConditioner.fresh.toString(),
      conditionerFloralPrice: ADDON_PRICES.fabricConditioner.floral.toString(),
      stainRemoverPrice: ADDON_PRICES.stainRemover.toString(),
      bleachPrice: ADDON_PRICES.bleach.toString(),
      vatRate: (VAT_RATE * 100).toString(),
      dryCleaningPrices: DRY_CLEANING_PRICES,
      specialServices: {
        comfortersPrice: 150,
        towelsCurtainsLinensPrice: 120,
        minimumWeightComforters: 3,
        minimumWeightSpecial: 2
      }
    };
  }
  
  return {
    baseWeightPrice: jsonData.baseWeightPrice?.toString() || BASE_PRICE_PER_KILO.toString(),
    detergentRegularPrice: jsonData.detergentRegularPrice?.toString() || ADDON_PRICES.detergent.regular.toString(),
    detergentColorPrice: jsonData.detergentColorPrice?.toString() || ADDON_PRICES.detergent.color.toString(),
    conditionerRegularPrice: jsonData.conditionerRegularPrice?.toString() || ADDON_PRICES.fabricConditioner.regular.toString(),
    conditionerFreshPrice: jsonData.conditionerFreshPrice?.toString() || ADDON_PRICES.fabricConditioner.fresh.toString(),
    conditionerFloralPrice: jsonData.conditionerFloralPrice?.toString() || ADDON_PRICES.fabricConditioner.floral.toString(),
    stainRemoverPrice: jsonData.stainRemoverPrice?.toString() || ADDON_PRICES.stainRemover.toString(),
    bleachPrice: jsonData.bleachPrice?.toString() || ADDON_PRICES.bleach.toString(),
    vatRate: jsonData.vatRate?.toString() || (VAT_RATE * 100).toString(),
    dryCleaningPrices: jsonData.dryCleaningPrices || DRY_CLEANING_PRICES,
    specialServices: jsonData.specialServices || {
      comfortersPrice: 150,
      towelsCurtainsLinensPrice: 120,
      minimumWeightComforters: 3,
      minimumWeightSpecial: 2
    }
  };
}

// Function to fetch the latest pricing settings from the database
export async function getPricingSettings(): Promise<PricingConstants> {
  const currentTime = Date.now();
  
  // Return cached settings if they're recent enough
  if (cachedSettings && (currentTime - lastFetchTime < CACHE_DURATION)) {
    return cachedSettings;
  }
  
  // Otherwise fetch from database
  try {
    const { data, error } = await supabase
      .from('pricing_settings')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(1);
      
    if (error) {
      console.error("Error fetching pricing settings:", error);
      // Fall back to default constants
      return getDefaultPricingConstants();
    }
    
    if (data && data.length > 0) {
      // Safely cast the settings to our expected type
      const dbSettings = convertToSettingsType(data[0].settings);
      
      // Convert string values to numbers
      const settings: PricingConstants = {
        BASE_PRICE_PER_KILO: parseFloat(dbSettings.baseWeightPrice),
        ADDON_PRICES: {
          detergent: {
            regular: parseFloat(dbSettings.detergentRegularPrice),
            color: parseFloat(dbSettings.detergentColorPrice),
          },
          fabricConditioner: {
            regular: parseFloat(dbSettings.conditionerRegularPrice),
            fresh: parseFloat(dbSettings.conditionerFreshPrice),
            floral: parseFloat(dbSettings.conditionerFloralPrice),
          },
          stainRemover: parseFloat(dbSettings.stainRemoverPrice),
          bleach: parseFloat(dbSettings.bleachPrice),
        },
        VAT_RATE: parseFloat(dbSettings.vatRate) / 100, // Convert percentage to decimal
        DRY_CLEANING_PRICES: dbSettings.dryCleaningPrices || DRY_CLEANING_PRICES,
        SPECIAL_SERVICES: dbSettings.specialServices
      };
      
      // Update the cache
      cachedSettings = settings;
      lastFetchTime = currentTime;
      
      return settings;
    }
  } catch (err) {
    console.error("Error in pricing service:", err);
  }
  
  // Fall back to default constants if anything fails
  return getDefaultPricingConstants();
}

// Function to get default pricing constants from the constants file
function getDefaultPricingConstants(): PricingConstants {
  return {
    BASE_PRICE_PER_KILO,
    ADDON_PRICES,
    VAT_RATE,
    DRY_CLEANING_PRICES
  };
}

// Invalidate cache function for when settings are updated
export function invalidatePricingCache(): void {
  cachedSettings = null;
  lastFetchTime = 0;
}

// Helper function to check if user has permission to update pricing settings
export async function checkPricingPermissions(): Promise<{hasPermission: boolean, message: string}> {
  try {
    // Attempt to query a single row from the pricing settings
    const { data, error } = await supabase
      .from('pricing_settings')
      .select('id')
      .limit(1);
      
    if (error) {
      if (error.code === '42501' || error.message.includes('permission denied') || error.message.includes('policy')) {
        return { 
          hasPermission: false, 
          message: "You don't have permission to access pricing settings. Only administrators can manage pricing." 
        };
      }
      
      return { 
        hasPermission: false, 
        message: `Database error: ${error.message}` 
      };
    }
    
    return { hasPermission: true, message: "" };
  } catch (err) {
    console.error("Error checking pricing permissions:", err);
    return { 
      hasPermission: false, 
      message: "Unable to verify permissions. Please try again later." 
    };
  }
}

// Export the getDryCleaningPrices function separately
export async function getDryCleaningPrices(): Promise<Record<string, number>> {
  const settings = await getPricingSettings();
  return settings.DRY_CLEANING_PRICES || DRY_CLEANING_PRICES;
}
