
import React, { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Loader2, Save, AlertTriangle } from "lucide-react";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { 
  getDryCleaningPrices, 
  invalidatePricingCache 
} from "@/services/pricing/pricingService";

export function DryCleaningPricing() {
  const [prices, setPrices] = useState<Record<string, number>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load initial prices
  useEffect(() => {
    async function loadPrices() {
      try {
        setIsLoading(true);
        setError(null);
        const currentPrices = await getDryCleaningPrices();
        console.log("DryCleaningPricing loaded prices:", currentPrices);
        setPrices(currentPrices);
      } catch (err) {
        console.error("Error loading dry cleaning prices:", err);
        setError("Failed to load current prices");
      } finally {
        setIsLoading(false);
      }
    }

    loadPrices();
  }, []);

  // Handle price changes
  const handlePriceChange = (key: string, value: string) => {
    const numericValue = parseFloat(value) || 0;
    setPrices(prev => ({
      ...prev,
      [key]: numericValue
    }));
  };

  // Save prices to database
  const handleSave = async () => {
    try {
      setIsSaving(true);
      setError(null);
      
      console.log("Saving dry cleaning prices:", prices);
      
      // Get the latest pricing settings first
      const { data, error: fetchError } = await supabase
        .from('pricing_settings')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(1);
        
      if (fetchError) {
        throw new Error(`Failed to fetch current settings: ${fetchError.message}`);
      }
      
      // Prepare the settings object
      let settings = data && data.length > 0 
        ? Object.assign({}, data[0].settings as Record<string, any>)
        : {};
        
      // Create a properly typed object for DRY_CLEANING_PRICES
      // This fixes the TypeScript error by ensuring we're using an object type
      settings.DRY_CLEANING_PRICES = Object.assign({}, prices);
      
      // Insert new pricing settings with updated dry cleaning prices
      const { error: saveError } = await supabase
        .from('pricing_settings')
        .insert({
          settings,
          created_by: (await supabase.auth.getUser()).data.user?.id || null
        });
        
      if (saveError) {
        throw new Error(`Failed to save settings: ${saveError.message}`);
      }
      
      // Invalidate cache to ensure latest prices are used
      invalidatePricingCache();
      
      toast.success("Dry cleaning prices updated successfully");
    } catch (err: any) {
      console.error("Error saving pricing:", err);
      setError(err.message || "Failed to save pricing");
      toast.error("Error saving pricing", {
        description: err.message || "Please try again"
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Define items to display
  const dryCleaningItems = [
    { key: "BARONG_TAGALOG_JUSI", label: "Barong Tagalog (Jusi)" },
    { key: "BARONG_TAGALOG_PINA", label: "Barong Tagalog (Piña)" },
    { key: "COAT", label: "Coat" },
    { key: "LONG_PANTS", label: "Long Pants" },
    { key: "SIMPLE_GOWN", label: "Simple Gown" },
    { key: "BEADED_GOWN", label: "Beaded Gown" },
    { key: "WEDDING_GOWN", label: "Wedding Gown" },
    { key: "SHOES", label: "Shoes" },
    { key: "BLOUSE", label: "Blouse" },
    { key: "SKIRT", label: "Skirt" },
    { key: "POLO", label: "Polo" },
    { key: "SLACKS_TROUSERS", label: "Slacks/Trousers" }
  ];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Loading dry cleaning prices...</span>
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Dry Cleaning Prices</CardTitle>
        <CardDescription>
          Set prices for dry cleaning services
        </CardDescription>
      </CardHeader>
      <CardContent>
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-3 mb-4 text-red-700">
            <div className="flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2" />
              <div>{error}</div>
            </div>
          </div>
        )}
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {dryCleaningItems.map(item => (
            <div key={item.key} className="space-y-2">
              <Label htmlFor={`price-${item.key}`}>{item.label}</Label>
              <div className="flex">
                <div className="bg-muted px-3 flex items-center rounded-l-md border-y border-l">
                  ₱
                </div>
                <Input
                  id={`price-${item.key}`}
                  type="number"
                  min="0"
                  step="0.01"
                  value={prices[item.key] || 0}
                  onChange={(e) => handlePriceChange(item.key, e.target.value)}
                  className="rounded-l-none"
                />
              </div>
            </div>
          ))}
        </div>
        
        <Button
          onClick={handleSave}
          className="mt-6"
          disabled={isSaving}
        >
          {isSaving ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" />
              Save Prices
            </>
          )}
        </Button>
      </CardContent>
    </Card>
  );
}
