
import { ReceiptFormatter } from './ReceiptFormatter';
import { JobOrderFormatter } from './JobOrderFormatter';
import { PrintOrderData } from '../types';

// Create formatter factory functions
export const createReceiptFormatter = (data: PrintOrderData) => new ReceiptFormatter(data);
export const createJobOrderFormatter = (data: PrintOrderData) => new JobOrderFormatter(data);

// Export for convenience
export type { ReceiptFormatter, JobOrderFormatter };
