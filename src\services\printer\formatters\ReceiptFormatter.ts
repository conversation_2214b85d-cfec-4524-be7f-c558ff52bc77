
import { PrintOrderData } from "../types";
import { formatDateToLocaleString, formatCurrency } from "@/lib/utils";

export class ReceiptFormatter {
  private data: PrintOrderData;
  
  constructor(data: PrintOrderData) {
    this.data = data;
    // Log the data being passed to the receipt formatter to verify it's complete
    console.log('Receipt formatter data:', data);
  }
  
  public getReceiptContent(): string {
    const { id, orderDate, customer, amount, paidAmount, status, lineItems = [] } = this.data;
    
    const lineItemsHtml = lineItems.length > 0 
      ? this.renderLineItems(lineItems) 
      : "<p>No items specified</p>";
    
    const isDryCleaning = this.data.isDryCleaning || false;
    const isClient = this.data.customerType === 'client';
    
    // Generate service details section based on order type
    const serviceDetails = this.renderServiceDetails(isClient, isDryCleaning);
    
    // Determine if any washing supplements are used
    const hasSupplements = this.data.useDetergent || 
                          this.data.useFabricConditioner || 
                          this.data.useStainRemover || 
                          this.data.useBleach;
    
    // Generate washing supplements section if applicable
    const supplementsSection = hasSupplements && !isDryCleaning
      ? this.renderSupplements()
      : '';
    
    // Generate dry cleaning items section if applicable
    const dryCleaningItemsSection = isDryCleaning && this.data.dryCleaningItems?.length
      ? this.renderDryCleaningItems()
      : '';

    // Update status name for "fulfilled" status
    const displayStatus = status === "fulfilled" ? "Complete Delivery" : status.replace(/_/g, ' ');

    return `
      <div class="receipt">
        <div class="receipt-header">
          <h1>CMC Express Laundry</h1>
          <p>Official Receipt</p>
        </div>
        
        <div class="receipt-details">
          <p><strong>Order #:</strong> ${id}</p>
          <p><strong>Date:</strong> ${formatDateToLocaleString(orderDate)}</p>
          <p><strong>Customer:</strong> ${customer.name}</p>
          <p><strong>Phone:</strong> ${customer.phone}</p>
          ${customer.email ? `<p><strong>Email:</strong> ${customer.email}</p>` : ''}
          <p><strong>Status:</strong> ${displayStatus.toUpperCase()}</p>
        </div>

        <div class="receipt-service">
          <h2>Service Details</h2>
          ${serviceDetails}
        </div>
        
        ${supplementsSection}
        
        ${dryCleaningItemsSection}
        
        <div class="receipt-items">
          <h2>Items</h2>
          ${lineItemsHtml}
        </div>
        
        <div class="receipt-payment">
          <div class="total-line">
            <span>Subtotal:</span>
            <span>${formatCurrency(this.data.subtotalBeforeVAT || 0)}</span>
          </div>
          <div class="total-line">
            <span>VAT (${Math.round((this.data.vatAmount || 0) / (this.data.subtotalBeforeVAT || 1) * 100)}%):</span>
            <span>${formatCurrency(this.data.vatAmount || 0)}</span>
          </div>
          <div class="total-line total">
            <span>Total:</span>
            <span>${formatCurrency(amount)}</span>
          </div>
          <div class="total-line">
            <span>Paid:</span>
            <span>${formatCurrency(paidAmount)}</span>
          </div>
          <div class="total-line balance">
            <span>Balance:</span>
            <span>${formatCurrency(amount - paidAmount)}</span>
          </div>
        </div>
        
        <div class="receipt-footer">
          <p>Thank you for choosing CMC Express Laundry!</p>
          <p>For inquiries, please call: (02) 8123-4567</p>
        </div>
      </div>
      
      <style>
        .receipt {
          font-family: Arial, sans-serif;
          width: 100%;
          max-width: 350px;
          margin: 0 auto;
          padding: 15px 0;
        }
        .receipt-header {
          text-align: center;
          margin-bottom: 15px;
        }
        .receipt-header h1 {
          margin: 0;
          font-size: 1.2rem;
        }
        .receipt-header p {
          margin: 5px 0 0;
          font-size: 0.9rem;
        }
        .receipt-details p {
          margin: 5px 0;
          font-size: 0.8rem;
        }
        .receipt-details, .receipt-service, .receipt-items, .receipt-payment, .receipt-footer {
          margin-bottom: 15px;
        }
        .receipt-service h2, .receipt-items h2, .receipt-supplements h2 {
          font-size: 0.9rem;
          margin: 10px 0 5px;
          border-bottom: 1px dotted #000;
          padding-bottom: 3px;
        }
        .service-detail, .supplement-detail {
          display: flex;
          justify-content: space-between;
          font-size: 0.8rem;
          margin: 3px 0;
        }
        .item-row {
          display: flex;
          justify-content: space-between;
          font-size: 0.8rem;
          margin: 5px 0;
        }
        .item-name {
          flex: 3;
        }
        .item-qty {
          flex: 1;
          text-align: center;
        }
        .item-total {
          flex: 1;
          text-align: right;
        }
        .total-line {
          display: flex;
          justify-content: space-between;
          font-size: 0.8rem;
          margin: 3px 0;
        }
        .total {
          font-weight: bold;
        }
        .balance {
          font-weight: bold;
          border-top: 1px dotted #000;
          padding-top: 3px;
        }
        .receipt-footer {
          text-align: center;
          font-size: 0.7rem;
          border-top: 1px dotted #000;
          padding-top: 10px;
        }
      </style>
    `;
  }
  
  private renderServiceDetails(isClient: boolean, isDryCleaning: boolean): string {
    if (isDryCleaning) {
      return `
        <div class="service-detail">
          <span>Service:</span>
          <span>Dry Cleaning</span>
        </div>
      `;
    }
    
    if (isClient) {
      // Client-specific service details without weight
      return `
        <div class="service-detail">
          <span>Service Type:</span>
          <span>${this.data.serviceType || 'Standard Service'}</span>
        </div>
        <div class="service-detail">
          <span>Pieces:</span>
          <span>${this.data.numberOfPieces || 0}</span>
        </div>
      `;
    } else {
      // Walk-in service details with weight
      return `
        <div class="service-detail">
          <span>Service Type:</span>
          <span>${this.data.serviceType || 'Standard Service'}</span>
        </div>
        <div class="service-detail">
          <span>Weight:</span>
          <span>${this.data.weightKilos || 0} kg</span>
        </div>
        <div class="service-detail">
          <span>Pieces:</span>
          <span>${this.data.numberOfPieces || 0}</span>
        </div>
      `;
    }
  }
  
  private renderSupplements(): string {
    const { useDetergent, useFabricConditioner, useStainRemover, useBleach, 
            detergentType, conditionerType, detergentQuantity, conditionerQuantity } = this.data;
    
    if (!useDetergent && !useFabricConditioner && !useStainRemover && !useBleach) {
      return '';
    }
    
    let supplementsHtml = '<div class="receipt-supplements"><h2>Washing Supplements</h2>';
    
    if (useDetergent && detergentType) {
      supplementsHtml += `
        <div class="supplement-detail">
          <span>Detergent (${detergentType}):</span>
          <span>${detergentQuantity || 1} ${Number(detergentQuantity) > 1 ? 'units' : 'unit'}</span>
        </div>`;
    }
    
    if (useFabricConditioner && conditionerType) {
      supplementsHtml += `
        <div class="supplement-detail">
          <span>Fabric Conditioner (${conditionerType}):</span>
          <span>${conditionerQuantity || 1} ${Number(conditionerQuantity) > 1 ? 'units' : 'unit'}</span>
        </div>`;
    }
    
    if (useStainRemover) {
      supplementsHtml += `
        <div class="supplement-detail">
          <span>Stain Remover:</span>
          <span>Yes</span>
        </div>`;
    }
    
    if (useBleach) {
      supplementsHtml += `
        <div class="supplement-detail">
          <span>Bleach:</span>
          <span>Yes</span>
        </div>`;
    }
    
    supplementsHtml += '</div>';
    return supplementsHtml;
  }
  
  private renderDryCleaningItems(): string {
    if (!this.data.dryCleaningItems || this.data.dryCleaningItems.length === 0) {
      return '';
    }
    
    let itemsHtml = '<div class="receipt-items"><h2>Dry Cleaning Items</h2>';
    
    // Table header
    itemsHtml += `
      <div class="item-row">
        <span class="item-name"><strong>Item</strong></span>
        <span class="item-qty"><strong>Qty</strong></span>
        <span class="item-total"><strong>Total</strong></span>
      </div>
    `;
    
    // Table rows
    this.data.dryCleaningItems.forEach(item => {
      const total = (item.price * item.quantity) || 0;
      
      itemsHtml += `
        <div class="item-row">
          <span class="item-name">${item.name || 'Item'}</span>
          <span class="item-qty">${item.quantity || 1}</span>
          <span class="item-total">${formatCurrency(total)}</span>
        </div>
      `;
    });
    
    itemsHtml += '</div>';
    return itemsHtml;
  }
  
  private renderLineItems(lineItems: Array<{name: string, quantity: number, total: number}>): string {
    if (lineItems.length === 0) return "<p>No items specified</p>";
    
    let itemsHtml = `
      <div class="item-row">
        <span class="item-name"><strong>Item</strong></span>
        <span class="item-qty"><strong>Qty</strong></span>
        <span class="item-total"><strong>Total</strong></span>
      </div>
    `;
    
    lineItems.forEach(item => {
      itemsHtml += `
        <div class="item-row">
          <span class="item-name">${item.name}</span>
          <span class="item-qty">${item.quantity}</span>
          <span class="item-total">${formatCurrency(item.total)}</span>
        </div>
      `;
    });
    
    return itemsHtml;
  }
}
