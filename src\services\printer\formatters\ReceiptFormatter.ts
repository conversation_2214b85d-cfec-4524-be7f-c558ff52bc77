
import { COMMANDS } from '../constants';
import { PrintOrderData } from '../types';
import { FormattingUtils } from './FormattingUtils';

/**
 * Formats customer receipts for printing
 */
export class ReceiptFormatter {
  private formatting: FormattingUtils;
  
  constructor() {
    this.formatting = new FormattingUtils();
  }

  /**
   * Format order data into a printable receipt
   */
  public formatReceipt(orderData: PrintOrderData): string {
    try {
      // Get add-on quantities from order data
      const detergentQty = orderData.detergentQuantity || 1;
      const conditionerQty = orderData.conditionerQuantity || 1;
      
      // Build receipt content with ESC/POS commands
      let receiptContent = '';
      
      // Header
      receiptContent += COMMANDS.ALIGN_CENTER;
      receiptContent += COMMANDS.BOLD_ON;
      receiptContent += "CMC LAUNDRY\n";
      receiptContent += COMMANDS.BOLD_OFF;
      receiptContent += "-".repeat(this.formatting.getLineWidth()) + "\n";
      receiptContent += "ORDER SLIP\n";
      receiptContent += "-".repeat(this.formatting.getLineWidth()) + "\n";
      
      // Order info
      receiptContent += COMMANDS.ALIGN_LEFT;
      receiptContent += `Order ID: ${orderData.id}\n`;
      receiptContent += `Date: ${orderData.orderDate}\n`;
      receiptContent += `Customer: ${orderData.customer.name}\n`;
      receiptContent += `Phone: ${orderData.customer.phone}\n\n`;
      
      // Items
      receiptContent += COMMANDS.BOLD_ON;
      receiptContent += "Items:\n";
      receiptContent += COMMANDS.BOLD_OFF;
      
      if (orderData.lineItems && orderData.lineItems.length) {
        orderData.lineItems.forEach((item) => {
          receiptContent += `${this.formatting.formatLine(item.name + ' x' + item.quantity)}\n`;
          receiptContent += `${this.formatting.formatLine('', this.formatting.getLineWidth() - 8)}₱${item.total.toFixed(2)}\n`;
        });
      } else {
        receiptContent += "No items\n";
      }
      receiptContent += "\n";
      
      // Additional information
      if (orderData.weightKilos) {
        receiptContent += `Weight: ${orderData.weightKilos} kg\n`;
      }
      
      if (orderData.numberOfPieces) {
        receiptContent += `Pieces: ${orderData.numberOfPieces}\n`;
      }
      
      // Add-ons
      if (orderData.useDetergent || orderData.useFabricConditioner || 
          orderData.useStainRemover || orderData.useBleach) {
        receiptContent += COMMANDS.BOLD_ON;
        receiptContent += "\nAdd-ons:\n";
        receiptContent += COMMANDS.BOLD_OFF;
        
        if (orderData.useDetergent) {
          receiptContent += `- Detergent x${detergentQty}\n`;
        }
        if (orderData.useFabricConditioner) {
          receiptContent += `- Fabric Conditioner x${conditionerQty}\n`;
        }
        if (orderData.useStainRemover) {
          receiptContent += `- Stain Remover\n`;
        }
        if (orderData.useBleach) {
          receiptContent += `- Bleach Treatment\n`;
        }
      }
      
      receiptContent += "\n";
      
      // Payment information
      if (orderData.subtotalBeforeVAT) {
        receiptContent += `Subtotal: ₱${orderData.subtotalBeforeVAT.toFixed(2)}\n`;
      }
      
      if (orderData.vatAmount) {
        receiptContent += `VAT: ₱${orderData.vatAmount.toFixed(2)}\n`;
      }
      
      receiptContent += `Total Amount: ₱${orderData.amount.toFixed(2)}\n`;
      receiptContent += `Paid Amount: ₱${orderData.paidAmount.toFixed(2)}\n`;
      receiptContent += `Balance: ₱${(orderData.amount - orderData.paidAmount).toFixed(2)}\n\n`;
      
      // Status
      receiptContent += COMMANDS.BOLD_ON;
      receiptContent += `Status: ${orderData.status.toUpperCase()}\n`;
      receiptContent += COMMANDS.BOLD_OFF;
      
      // Footer
      receiptContent += COMMANDS.ALIGN_CENTER;
      receiptContent += "-".repeat(this.formatting.getLineWidth()) + "\n";
      receiptContent += "Thank you for your business!\n";
      receiptContent += "Please keep your receipt for pickup\n";
      
      return receiptContent;
    } catch (error) {
      console.error('Error formatting receipt:', error);
      return "ERROR FORMATTING RECEIPT";
    }
  }
}
