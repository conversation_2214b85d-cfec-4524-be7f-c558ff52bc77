export type CustomerType = 'client' | 'walk-in';

export interface Order {
  id: string;
  uuid: string;
  clientId: string;
  orderDate: string;
  deliveryDate: string;
  customer: {
    name: string;
    phone: string;
    contactPerson?: string;
  };
  amount: number;
  paidAmount: number;
  status: string;
  lineItems: LineItem[];
  customerType: CustomerType;
  weightKilos: number;
  numberOfPieces: number;
  useDetergent: boolean;
  useFabricConditioner: boolean;
  useStainRemover: boolean;
  useBleach: boolean;
  detergentType: string;
  conditionerType: string;
  detergentQuantity: number;
  conditionerQuantity: number;
  prefix: string;
  serviceType: string;
  notes: string;
  vatAmount: number;
  subtotalBeforeVAT: number;
  reference_code?: string;
  items?: string | any[]; // Items field
  selectedClientItems?: any[]; // Added this property
  isDryCleaning?: boolean; // Add isDryCleaning property
  dryCleaningItems?: DryCleaningItem[]; // Add dryCleaningItems property
  updated_at?: string; // Add this field to make it available for the updateOrder function
}

export interface LineItem {
  id: string;
  name: string;
  quantity: number;
  unitPrice: number;
  total: number;
  treatmentDescription?: string;
}

export interface DryCleaningItem {
  type: string;
  name: string;
  price: number;
  quantity: number;
  total?: number;
}

export type InvoiceStatus = 'pending' | 'paid' | 'overdue' | 'canceled';

export interface Invoice {
  id: string;
  invoice_number: string;
  client_id: string;
  total_amount: number;
  due_date: string;
  created_at: string;
  paid_at: string | null;
  status: InvoiceStatus;
  notes: string | null;
}

export interface Payment {
  id: string;
  invoice_id: string;
  amount: number;
  payment_date: string;
  payment_method: string;
  reference_number: string | null;
  notes: string | null;
  created_at: string;
}

export type JournalType = 'sales' | 'purchases' | 'expenses' | 'general';

export type TransactionStatus = 'pending' | 'posted' | 'draft';

export interface Transaction {
  id: string;
  date: string;
  description: string;
  amount: number;
  journal: JournalType;
  debit_account: string;
  credit_account: string;
  reference: string;
  status: TransactionStatus;
  receipt_url: string | null;
  receipt_filename: string | null;
}
