import { Order, LineItem } from "@/types";
import { getTreatmentPrice } from "@/utils/orderCalculations";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { useState } from "react";
import { EditableItemRow } from "./EditableItemRow";
import { useOrder } from "@/contexts/OrderContext";
import { updateOrderItems } from "@/services/orders/mutations/updateOrderItems";
import { useToast } from "@/hooks/use-toast";
interface ItemsTableProps {
  lineItems: LineItem[];
  order: Order;
}
export function ItemsTable({
  lineItems,
  order
}: ItemsTableProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editableItems, setEditableItems] = useState<LineItem[]>([]);
  const {
    refreshOrder
  } = useOrder();
  const {
    toast
  } = useToast();

  // Initialize editable items when starting edit mode
  const handleStartEdit = () => {
    setEditableItems([...lineItems]);
    setIsEditing(true);
  };

  // Handle item change when editing
  const handleItemChange = (updatedItem: LineItem, index: number) => {
    const newItems = [...editableItems];
    newItems[index] = updatedItem;
    setEditableItems(newItems);
  };

  // Add a new blank item
  const handleAddItem = () => {
    setEditableItems([...editableItems, {
      id: `temp-${Date.now()}`,
      name: "",
      quantity: 1,
      unitPrice: 0,
      total: 0
    }]);
  };

  // Remove an item
  const handleRemoveItem = (index: number) => {
    const newItems = [...editableItems];
    newItems.splice(index, 1);
    setEditableItems(newItems);
  };

  // Save changes
  const handleSaveChanges = async () => {
    try {
      // Filter out empty items
      const validItems = editableItems.filter(item => item.name && item.quantity > 0);
      const result = await updateOrderItems(order, validItems);
      if (!result.success) {
        throw new Error("Failed to update order items");
      }
      toast({
        title: "Success",
        description: "Order items have been updated"
      });

      // Refresh order data
      await refreshOrder();

      // Exit edit mode
      setIsEditing(false);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update order items",
        variant: "destructive"
      });
      console.error("Failed to save item changes:", error);
    }
  };

  // Cancel editing
  const handleCancelEdit = () => {
    setIsEditing(false);
  };

  // Standard view mode
  if (!isEditing) {
    return <div className="space-y-3">
        <div className="flex justify-between items-center">
          <h3 className="text-sm font-medium">Order Items</h3>
          
        </div>
        
        <div className="border rounded-md">
          <table className="w-full">
            <thead className="bg-muted/50">
              <tr>
                <th className="text-left p-2">Item</th>
                <th className="text-center p-2">Quantity</th>
                <th className="text-right p-2">Unit Price</th>
                <th className="text-right p-2">Total</th>
              </tr>
            </thead>
            <tbody>
              {lineItems.length === 0 ? <tr>
                  <td colSpan={4} className="p-4 text-center text-muted-foreground">
                    No items added to this order.
                  </td>
                </tr> : lineItems.map((item, index) => {
              const basePricePerItem = item.unitPrice;
              const treatmentCost = getTreatmentPrice(item.treatmentDescription);
              return <tr key={`${item.id}-${index}`} className="border-t">
                      <td className="p-2">
                        {item.name}
                        {item.treatmentDescription && <div className="text-xs text-muted-foreground mt-1">
                            Treatment: {item.treatmentDescription} 
                            {treatmentCost > 0 && <span className="text-xs ml-1 font-medium text-amber-600">
                                (+₱{treatmentCost.toFixed(2)}/item)
                              </span>}
                          </div>}
                      </td>
                      <td className="p-2 text-center">{item.quantity}</td>
                      <td className="p-2 text-right">₱ {basePricePerItem.toFixed(2)}</td>
                      <td className="p-2 text-right">₱ {item.total.toFixed(2)}</td>
                    </tr>;
            })}
              
              {/* Display summary row if there are treatments or add-ons */}
              {lineItems.length > 0 || order.useDetergent || order.useFabricConditioner || order.useStainRemover ? <tr className="border-t bg-muted/30">
                  <td colSpan={3} className="text-right p-2 font-medium">Total:</td>
                  <td className="p-2 text-right font-medium">₱ {order.amount.toFixed(2)}</td>
                </tr> : null}
            </tbody>
          </table>
        </div>
        
        {/* Display add-ons if present */}
        {(order.useDetergent || order.useFabricConditioner || order.useStainRemover) && <div className="text-sm text-muted-foreground">
            <p>Additional services:</p>
            <ul className="list-disc pl-5">
              {order.useDetergent && <li>Detergent</li>}
              {order.useFabricConditioner && <li>Fabric Conditioner</li>}
              {order.useStainRemover && <li>Stain Remover</li>}
            </ul>
          </div>}
      </div>;
  }

  // Edit mode
  return <div className="space-y-3">
      <div className="flex justify-between items-center">
        <h3 className="text-sm font-medium">Edit Order Items</h3>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={handleCancelEdit}>
            Cancel
          </Button>
          <Button size="sm" onClick={handleSaveChanges}>
            Save Changes
          </Button>
        </div>
      </div>

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Item Name</TableHead>
            <TableHead>Quantity</TableHead>
            <TableHead>Unit Price (₱)</TableHead>
            <TableHead>Treatment</TableHead>
            <TableHead className="text-right">Total (₱)</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {editableItems.length === 0 ? <TableRow>
              <TableCell colSpan={6} className="text-center py-4 text-muted-foreground">
                No items. Add an item to this order.
              </TableCell>
            </TableRow> : editableItems.map((item, index) => <EditableItemRow key={`${item.id}-${index}`} item={item} clientId={order.clientId} onChange={updatedItem => handleItemChange(updatedItem, index)} onRemove={() => handleRemoveItem(index)} />)}
        </TableBody>
      </Table>

      <div className="flex justify-between items-center">
        <Button type="button" variant="outline" size="sm" onClick={handleAddItem}>
          <Plus className="h-4 w-4 mr-1" /> Add Item
        </Button>
        
        <div className="text-right">
          <p className="font-semibold">
            Total: ₱{editableItems.reduce((sum, item) => sum + item.total, 0).toFixed(2)}
          </p>
        </div>
      </div>
    </div>;
}