
import { Order, LineItem } from "@/types";
import { ReadOnlyItemsTable } from "./ReadOnlyItemsTable";
import { OrderAddOnsInfo } from "./OrderAddOnsInfo";
import { EditableItemsTable } from "./EditableItemsTable";
import { useItemsEditor } from "./hooks/useItemsEditor";

interface ItemsTableProps {
  lineItems: LineItem[];
  order: Order;
}

export function ItemsTable({ lineItems, order }: ItemsTableProps) {
  const {
    isEditing,
    editableItems,
    handleStartEdit,
    handleItemChange,
    handleAddItem,
    handleRemoveItem,
    handleSaveChanges,
    handleCancelEdit
  } = useItemsEditor(order, lineItems);

  // Standard view mode
  if (!isEditing) {
    return (
      <div className="space-y-3">
        <div className="flex justify-between items-center">
          <h3 className="text-sm font-medium">Order Items</h3>
          {/* Removed Edit Items button */}
        </div>
        
        <ReadOnlyItemsTable lineItems={lineItems} order={order} />
        
        <OrderAddOnsInfo order={order} />
      </div>
    );
  }

  // Edit mode
  return (
    <EditableItemsTable
      editableItems={editableItems}
      onItemChange={handleItemChange}
      onRemoveItem={handleRemoveItem}
      onAddItem={handleAddItem}
      onSaveChanges={handleSaveChanges}
      onCancelEdit={handleCancelEdit}
      clientId={order.clientId}
    />
  );
}
