
import { useState } from 'react';
import { LineItem } from "@/types";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { X } from "lucide-react";

interface JobOrderItemRowProps {
  item: LineItem;
  clientId?: string;
  clientItems: any[];
  isDryCleaning: boolean;
  onChange: (updatedItem: LineItem) => void;
  onRemove: () => void;
}

export function JobOrderItemRow({
  item,
  clientId,
  clientItems,
  isDryCleaning,
  onChange,
  onRemove
}: JobOrderItemRowProps) {
  const [itemName, setItemName] = useState(item.name);
  const [quantity, setQuantity] = useState(item.quantity);
  const [unitPrice, setUnitPrice] = useState(item.unitPrice);
  const [treatment, setTreatment] = useState(item.treatmentDescription || '');

  const handleNameChange = (value: string) => {
    setItemName(value);
    
    // If selecting from client items dropdown, update price too
    const selectedClientItem = clientItems.find(ci => ci.name === value);
    if (selectedClientItem) {
      setUnitPrice(selectedClientItem.unit_price);
      updateItem({
        name: value,
        unitPrice: selectedClientItem.unit_price
      });
    } else {
      updateItem({ name: value });
    }
  };

  const handleQuantityChange = (value: string) => {
    const newQuantity = parseInt(value) || 1;
    setQuantity(newQuantity);
    updateItem({ quantity: newQuantity });
  };

  const handlePriceChange = (value: string) => {
    const newPrice = parseFloat(value) || 0;
    setUnitPrice(newPrice);
    updateItem({ unitPrice: newPrice });
  };

  const handleTreatmentChange = (value: string) => {
    setTreatment(value);
    updateItem({ treatmentDescription: value });
  };

  const updateItem = (updates: Partial<LineItem>) => {
    const updatedItem = { ...item, ...updates };
    // Calculate total
    updatedItem.total = updatedItem.quantity * updatedItem.unitPrice;
    onChange(updatedItem);
  };

  const treatments = isDryCleaning ? [
    "Dry Clean",
    "Dry Clean Delicate",
    "Press Only",
    "Starch Light",
    "Starch Medium",
    "Starch Heavy"
  ] : [
    "Regular Wash",
    "Delicate Wash",
    "Stain Treatment",
    "Whitening",
    "Bleach"
  ];

  return (
    <tr>
      <td className="px-6 py-2">
        {clientId && clientItems.length > 0 ? (
          <Select 
            value={itemName} 
            onValueChange={handleNameChange}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select item..." />
            </SelectTrigger>
            <SelectContent>
              {clientItems.map(item => (
                <SelectItem key={item.id} value={item.name}>
                  {item.name} - ₱{item.unit_price.toFixed(2)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        ) : (
          <Input 
            type="text" 
            value={itemName} 
            onChange={(e) => handleNameChange(e.target.value)}
            placeholder={isDryCleaning ? "Dress shirt, suit, etc." : "Item name"}
          />
        )}
      </td>
      <td className="px-6 py-2">
        <Input 
          type="number" 
          value={quantity} 
          onChange={(e) => handleQuantityChange(e.target.value)}
          min="1"
        />
      </td>
      <td className="px-6 py-2">
        <Input 
          type="number" 
          value={unitPrice} 
          onChange={(e) => handlePriceChange(e.target.value)}
          min="0"
          step="0.01"
        />
      </td>
      <td className="px-6 py-2">
        <Select 
          value={treatment || "none"} 
          onValueChange={handleTreatmentChange}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select treatment..." />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="none">None</SelectItem>
            {treatments.map(t => (
              <SelectItem key={t} value={t}>{t}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </td>
      <td className="px-6 py-2 text-left">
        ₱{(item.total || (quantity * unitPrice)).toFixed(2)}
      </td>
      <td className="px-6 py-2 text-right">
        <Button 
          type="button" 
          variant="ghost" 
          size="sm" 
          onClick={onRemove}
        >
          <X className="h-4 w-4" />
        </Button>
      </td>
    </tr>
  );
}
