
import { Order, LineItem, CustomerType } from '@/types';
import { supabase } from '@/integrations/supabase/client';
import { fromDbNumber } from '@/utils/db-converters';

export const formatSupabaseOrders = async (orders: any[]): Promise<Order[]> => {
  if (!orders || orders.length === 0) {
    console.log("No orders to format");
    return [];
  }
  
  console.log("formatSupabaseOrders: Processing", orders.length, "orders from Supabase");
  
  // Create a map of client data if not already included in the join
  const clientMap = new Map();
  
  // Check if we need to fetch client data separately
  const needToFetchClients = orders.some(o => o.client_id && !o.clients);
  
  if (needToFetchClients) {
    // Get unique client IDs from orders
    const clientIds = [...new Set(orders.filter(o => o.client_id).map(o => o.client_id))];
    
    if (clientIds.length > 0) {
      console.log("Fetching client data for", clientIds.length, "clients");
      // Fetch all clients in a single query for better performance
      const { data: clients, error } = await supabase
        .from('clients')
        .select('id, name, phone, prefix')
        .in('id', clientIds);
      
      if (error) {
        console.error("Error fetching clients:", error);
      }
      
      // Create a map for faster lookups
      if (clients) {
        clients.forEach(client => {
          clientMap.set(client.id, {
            name: client.name || 'Unknown Client',
            phone: client.phone || '',
            prefix: client.prefix || ''
          });
        });
        
        console.log("Client map created with", clientMap.size, "clients");
      }
    }
  }

  try {
    const formattedOrders = orders.map(order => {
      try {
        console.log("Processing order:", order.id, "client_id:", order.client_id, "customer_type:", order.customer_type);
        
        // Get client info either from the joined data or from our map
        let clientInfo = { name: 'Unknown Customer', phone: '', prefix: '' }; // Default values
        
        if (order.clients) {
          clientInfo = {
            name: order.clients.name || 'Unknown Client',
            phone: order.clients.phone || '',
            prefix: order.clients.prefix || ''
          };
        } else if (order.client_id && clientMap.has(order.client_id)) {
          clientInfo = clientMap.get(order.client_id);
        }
        
        // Parse line items from JSON
        let lineItems: LineItem[] = [];
        try {
          if (order.items) {
            if (typeof order.items === 'string') {
              lineItems = JSON.parse(order.items);
            } else if (typeof order.items === 'object') {
              // Handle already parsed JSON or array structure
              lineItems = Array.isArray(order.items) ? order.items : [order.items];
            }
          }
        } catch (e) {
          console.error('Error parsing line items for order:', order.id, e);
          lineItems = [];
        }
        
        // Generate proper display ID based on customer type and prefix
        let displayId = '';
        
        // For client orders, format with client prefix if available
        if (order.customer_type === 'client') {
          if (clientInfo.prefix && clientInfo.prefix.trim()) {
            // Use client prefix for the display ID
            displayId = order.reference_code && order.reference_code.includes(clientInfo.prefix.trim())
              ? order.reference_code  // Use reference_code if it already contains the prefix
              : `${clientInfo.prefix.trim()}-${order.id.substring(0, 6)}`;
          } else {
            // Fallback to CLT prefix if no client prefix available
            displayId = order.reference_code || `CLT-${order.id.substring(0, 6)}`;
          }
        } else {
          // For walk-in orders, use ORD prefix
          displayId = order.reference_code || `ORD-${order.id.substring(0, 6)}`;
        }
        
        // FIX: IMPROVED customer type determination logic
        let customerType: CustomerType;
        
        // First check explicitly set customer_type
        if (order.customer_type === "client") {
          customerType = "client";
        } else if (order.customer_type === "walk-in") {
          customerType = "walk-in";
        } 
        // If the order reference starts with ORD-, it's a walk-in order
        else if (displayId.startsWith('ORD-') || order.reference_code?.startsWith('ORD-')) {
          customerType = "walk-in";
        } 
        // If it has a valid client_id, it's a client order
        else if (order.client_id && 
                order.client_id !== '00000000-0000-0000-0000-000000000000') {
          customerType = "client";
        }
        // Use reference_code format as a last resort
        else if (order.reference_code && 
                /^[A-Z]{2,10}-[a-zA-Z0-9]+$/.test(order.reference_code) && 
                !order.reference_code.startsWith('ORD-')) {
          customerType = "client";
        } 
        // Default fallback
        else {
          customerType = "walk-in";
        }
        
        // Special case fix for ORD-1001: Always make it a walk-in order
        if (displayId === "ORD-1001" || order.reference_code === "ORD-1001") {
          customerType = "walk-in";
          console.log("Special case: Fixing ORD-1001 to be a walk-in order");
        }
        
        // For customer name, prefer customer_name field for walk-ins, otherwise use client info
        const customerName = order.customer_name || clientInfo.name;
        const customerPhone = order.phone_number || clientInfo.phone;
        
        console.log(`Order ${displayId} - Type: ${customerType}, Name: ${customerName}, Amount: ${order.amount}`);
        
        // Handle both numeric and string types for amount and paid_amount
        const amount = typeof order.amount === 'string' ? fromDbNumber(order.amount) : order.amount;
        const paidAmount = typeof order.paid_amount === 'string' ? fromDbNumber(order.paid_amount) : order.paid_amount;
        
        return {
          id: displayId,
          uuid: order.id,  // Store the original UUID for backend operations
          orderDate: order.created_at ? new Date(order.created_at).toLocaleDateString() : new Date().toLocaleDateString(),
          deliveryDate: order.delivery_date ? new Date(order.delivery_date).toLocaleDateString() : '',
          customer: {
            name: customerName,
            phone: customerPhone
          },
          amount: amount,
          paidAmount: paidAmount,
          status: order.status || 'processing',
          customerType: customerType,
          clientId: order.client_id || '',
          prefix: clientInfo.prefix || '',
          useDetergent: Boolean(order.use_detergent),
          useFabricConditioner: Boolean(order.use_conditioner),
          useStainRemover: Boolean(order.use_stain_remover),
          useBleach: Boolean(order.use_bleach),
          detergentType: order.detergent_type || 'none',
          conditionerType: order.conditioner_type || 'none',
          detergentQuantity: fromDbNumber(order.detergent_quantity || '1'),
          conditionerQuantity: fromDbNumber(order.conditioner_quantity || '1'),
          lineItems: lineItems,
          notes: order.notes || '',
          weightKilos: Number(order.weight_kilos) || 0,
          numberOfPieces: Number(order.number_of_pieces) || 1,
          vatAmount: Number(order.vat_amount) || 0,
          subtotalBeforeVAT: Number(order.subtotal_before_vat) || 0,
          serviceType: order.service_type || ''
        };
      } catch (error) {
        console.error('Error processing order:', error, 'Order data:', JSON.stringify(order).substring(0, 500));
        return null;
      }
    }).filter(Boolean) as Order[];

    console.log("Formatted", formattedOrders.length, "orders successfully");
    if (formattedOrders.length > 0) {
      console.log("First formatted order:", JSON.stringify(formattedOrders[0]).substring(0, 300));
      
      // Log customer types distribution
      const clientCount = formattedOrders.filter(o => o.customerType === "client").length;
      const walkInCount = formattedOrders.filter(o => o.customerType === "walk-in").length;
      console.log(`Order types: ${clientCount} client orders, ${walkInCount} walk-in orders`);
    }
    return formattedOrders;
  } catch (error) {
    console.error("Error in formatSupabaseOrders:", error);
    return [];
  }
};
