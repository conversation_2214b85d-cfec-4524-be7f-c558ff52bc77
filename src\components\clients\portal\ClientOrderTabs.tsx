
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Clock, CheckCircle2, Package } from 'lucide-react';
import { ClientOrderTable } from './ClientOrderTable';
import { Order } from '@/types';

interface ClientOrderTabsProps {
  orders: Order[];
  activeTab: string;
  onTabChange: (value: string) => void;
}

export function ClientOrderTabs({ orders, activeTab, onTabChange }: ClientOrderTabsProps) {
  return (
    <Tabs value={activeTab} onValueChange={onTabChange} className="w-full">
      <TabsList className="grid grid-cols-4 mb-4">
        <TabsTrigger value="all">All</TabsTrigger>
        <TabsTrigger value="ready_for_pickup">
          <CheckCircle2 className="h-4 w-4 mr-1" /> Ready For Pickup
        </TabsTrigger>
        <TabsTrigger value="processing">
          <Clock className="h-4 w-4 mr-1" /> Processing
        </TabsTrigger>
        <TabsTrigger value="fulfilled">
          <Package className="h-4 w-4 mr-1" /> Delivery Complete
        </TabsTrigger>
      </TabsList>
      
      <TabsContent value="all">
        <ClientOrderTable filteredOrders={orders} status="all" />
      </TabsContent>
      
      <TabsContent value="ready_for_pickup">
        <ClientOrderTable 
          filteredOrders={orders.filter(o => o.status === 'ready_for_pickup')} 
          status="ready_for_pickup" 
        />
      </TabsContent>
      
      <TabsContent value="processing">
        <ClientOrderTable 
          filteredOrders={orders.filter(o => o.status === 'processing')} 
          status="processing" 
        />
      </TabsContent>
      
      <TabsContent value="fulfilled">
        <ClientOrderTable 
          filteredOrders={orders.filter(o => o.status === 'fulfilled')} 
          status="fulfilled" 
        />
      </TabsContent>
    </Tabs>
  );
}
