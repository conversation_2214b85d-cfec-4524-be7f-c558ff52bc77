
import { supabase } from "@/integrations/supabase/client";
import { Order, LineItem } from "@/types";
import { auditService } from '@/services/audit/auditService';
import { formatItemsForDatabase, calculateOrderTotal } from "@/utils/orderCalculations";

export const updateOrderItems = async (
  order: Order,
  items: LineItem[],
): Promise<{ success: boolean; updatedOrder?: Partial<Order> }> => {
  try {
    if (!order || !order.uuid) {
      console.error("Invalid order provided to updateOrderItems");
      return { success: false };
    }
    
    // Format items for database with recalculated totals
    const itemsForDb = formatItemsForDatabase(items);
    
    // Calculate the new order total
    const newTotal = calculateOrderTotal(itemsForDb);
    
    console.log("Updating order items:", {
      orderId: order.id,
      orderUuid: order.uuid,
      itemCount: items.length,
      newTotal,
      items: itemsForDb
    });
    
    // Convert items to a valid JSON format that Supabase accepts
    const itemsJson = JSON.stringify(itemsForDb);
    
    // Update the order items and total amount in the database
    const { error } = await supabase
      .from('orders')
      .update({
        items: itemsJson,
        amount: newTotal,
        updated_at: new Date().toISOString()
      })
      .eq('id', order.uuid);
      
    if (error) {
      console.error('Error updating order items in Supabase:', error);
      throw error;
    }
    
    // Log the action in audit logs
    await auditService.logOrderAction('update_items', order.id, {
      previous_items_count: order.lineItems?.length || 0,
      new_items_count: items.length,
      previous_total: order.amount,
      new_total: newTotal
    });
    
    console.log("Order items updated successfully");
    
    const updatedOrderData = {
      lineItems: itemsForDb,
      amount: newTotal
    };
    
    // Return the updated order data for local state updates
    return { 
      success: true,
      updatedOrder: updatedOrderData
    };
  } catch (error) {
    console.error("Failed to update order items:", error);
    return { success: false };
  }
};
