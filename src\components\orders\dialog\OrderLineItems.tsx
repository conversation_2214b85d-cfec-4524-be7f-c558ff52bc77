
import { Order } from "@/types";

interface OrderLineItemsProps {
  order: Order;
}

export function OrderLineItems({ order }: OrderLineItemsProps) {
  // Determine if this is a client order
  const isClientOrder = order.customerType === "client" || 
                       (!!order.clientId && order.clientId !== '00000000-0000-0000-0000-000000000000');
  
  // If the order has line items, display them in a table
  if (order.lineItems && order.lineItems.length > 0) {
    return (
      <div className="space-y-3">
        <h3 className="text-sm font-medium">Items</h3>
        <div className="border rounded-md">
          <table className="w-full">
            <thead className="bg-muted/50">
              <tr>
                <th className="text-left p-2">Item</th>
                <th className="text-center p-2">Quantity</th>
                <th className="text-right p-2">Unit Price</th>
                <th className="text-right p-2">Total</th>
              </tr>
            </thead>
            <tbody>
              {order.lineItems.map((item, index) => (
                <tr key={index} className="border-t">
                  <td className="p-2">
                    {item.name}
                    {item.treatmentDescription && (
                      <div className="text-xs text-muted-foreground mt-1">
                        {item.treatmentDescription}
                      </div>
                    )}
                  </td>
                  <td className="p-2 text-center">{item.quantity}</td>
                  <td className="p-2 text-right">₱ {item.unitPrice.toFixed(2)}</td>
                  <td className="p-2 text-right">₱ {item.total.toFixed(2)}</td>
                </tr>
              ))}
              {/* Display summary row if there are treatments or add-ons */}
              {order.useDetergent || order.useFabricConditioner || order.useStainRemover ? (
                <tr className="border-t bg-muted/30">
                  <td colSpan={3} className="text-right p-2 font-medium">Total:</td>
                  <td className="p-2 text-right font-medium">₱ {order.amount.toFixed(2)}</td>
                </tr>
              ) : null}
            </tbody>
          </table>
        </div>
        
        {/* Display add-ons if present */}
        {(order.useDetergent || order.useFabricConditioner || order.useStainRemover) && (
          <div className="text-sm text-muted-foreground">
            <strong>Add-ons:</strong>{' '}
            {[
              order.useDetergent ? `Detergent (${order.detergentType || 'Regular'})` : null,
              order.useFabricConditioner ? `Fabric Conditioner (${order.conditionerType || 'Regular'})` : null,
              order.useStainRemover ? 'Stain Removal' : null
            ].filter(Boolean).join(', ')}
          </div>
        )}
      </div>
    );
  } else if (order.serviceType) {
    // Try to parse service items from the order items if it exists
    let serviceItems = [];
    try {
      if (typeof order.items === 'string' && order.items) {
        serviceItems = JSON.parse(order.items);
      } else if (Array.isArray(order.items)) {
        serviceItems = order.items;
      }
    } catch (e) {
      console.error("Failed to parse service items:", e);
    }

    // For orders with serviceType but no line items, show service information
    // Different display format based on customer type
    if (isClientOrder) {
      // Client order service display
      return (
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Client Service Details</h4>
          <div className="text-sm border rounded-md p-3 bg-blue-50">
            <div className="flex justify-between py-1">
              <span className="font-medium">{order.serviceType.replace(/_/g, ' ').toUpperCase()} service</span>
              <span>₱{order.subtotalBeforeVAT?.toFixed(2) || order.amount.toFixed(2)}</span>
            </div>
            
            {/* Show weight/pieces information */}
            <div className="mt-2 text-sm text-muted-foreground">
              {order.weightKilos ? `Weight: ${order.weightKilos} kg` : ''}
              {order.weightKilos && order.numberOfPieces ? ' · ' : ''}
              {order.numberOfPieces ? `Pieces: ${order.numberOfPieces}` : ''}
            </div>
            
            {/* Display service items if they exist */}
            {serviceItems && serviceItems.length > 0 && (
              <div className="mt-3 border-t pt-2">
                <p className="font-medium mb-1">Service Items:</p>
                <ul className="list-disc list-inside space-y-1 pl-2">
                  {serviceItems.map((item, idx) => (
                    <li key={idx} className="text-sm">
                      {item.name || item.type} 
                      {item.quantity ? ` × ${item.quantity}` : ''} 
                      {item.unitPrice || item.price ? ` - ₱${(item.unitPrice || item.price).toFixed(2)}` : ''}
                      {item.treatmentDescription && (
                        <span className="text-xs text-muted-foreground ml-1">
                          ({item.treatmentDescription})
                        </span>
                      )}
                    </li>
                  ))}
                </ul>
              </div>
            )}
            
            {/* Show VAT information if present */}
            {order.vatAmount > 0 && (
              <div className="flex justify-between pt-2 mt-2 border-t">
                <span>VAT ({(order.vatAmount / order.subtotalBeforeVAT * 100).toFixed(0)}%):</span>
                <span>₱{order.vatAmount.toFixed(2)}</span>
              </div>
            )}
            
            {/* Show total */}
            <div className="flex justify-between pt-2 mt-2 border-t font-medium">
              <span>Total:</span>
              <span>₱{order.amount.toFixed(2)}</span>
            </div>
          </div>
        </div>
      );
    } else {
      // Walk-in customer service display with distinct styling
      return (
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Walk-in Service Details</h4>
          <div className="text-sm border rounded-md p-3 bg-green-50">
            <div className="flex justify-between py-1">
              <span className="font-medium">{order.serviceType.replace(/_/g, ' ').toUpperCase()} service</span>
              <span>₱{order.subtotalBeforeVAT?.toFixed(2) || order.amount.toFixed(2)}</span>
            </div>
            
            {/* Show customer name */}
            <div className="mt-1 text-sm">
              <span className="font-medium">Customer:</span> {order.customer?.name}
            </div>
            
            {/* Show weight/pieces information */}
            <div className="mt-2 text-sm text-muted-foreground">
              {order.weightKilos ? `Weight: ${order.weightKilos} kg` : ''}
              {order.weightKilos && order.numberOfPieces ? ' · ' : ''}
              {order.numberOfPieces ? `Pieces: ${order.numberOfPieces}` : ''}
            </div>
            
            {/* Display service items if they exist */}
            {serviceItems && serviceItems.length > 0 && (
              <div className="mt-3 border-t pt-2">
                <p className="font-medium mb-1">Service Items:</p>
                <ul className="list-disc list-inside space-y-1 pl-2">
                  {serviceItems.map((item, idx) => (
                    <li key={idx} className="text-sm">
                      {item.name || item.type} 
                      {item.quantity ? ` × ${item.quantity}` : ''} 
                      {item.unitPrice || item.price ? ` - ₱${(item.unitPrice || item.price).toFixed(2)}` : ''}
                      {item.treatmentDescription && (
                        <span className="text-xs text-muted-foreground ml-1">
                          ({item.treatmentDescription})
                        </span>
                      )}
                    </li>
                  ))}
                </ul>
              </div>
            )}
            
            {/* Show VAT information if present */}
            {order.vatAmount > 0 && (
              <div className="flex justify-between pt-2 mt-2 border-t">
                <span>VAT ({(order.vatAmount / order.subtotalBeforeVAT * 100).toFixed(0)}%):</span>
                <span>₱{order.vatAmount.toFixed(2)}</span>
              </div>
            )}
            
            {/* Show total */}
            <div className="flex justify-between pt-2 mt-2 border-t font-medium">
              <span>Total:</span>
              <span>₱{order.amount.toFixed(2)}</span>
            </div>
          </div>
        </div>
      );
    }
  } else {
    // Fallback for orders without line items or service type
    return (
      <div className="space-y-2">
        <h4 className="text-sm font-medium">Service Details</h4>
        <div className="text-muted text-sm p-3 border rounded-md">
          No service items or details available
        </div>
      </div>
    );
  }
}

