
import { useEffect } from "react";
import { Order } from "@/types";
import { ItemsTable } from "./order-items/ItemsTable";
import { ClientServiceDetails } from "./order-items/ClientServiceDetails";
import { WalkInServiceDetails } from "./order-items/WalkInServiceDetails";
import { NoServiceDetails } from "./order-items/NoServiceDetails";
import { isClientOrder } from "./order-items/utils";
import { useOrder } from "@/contexts/OrderContext";

interface OrderLineItemsProps {
  order: Order;
  onOrderUpdated?: () => void;
}

export function OrderLineItems({ order, onOrderUpdated }: OrderLineItemsProps) {
  const { order: contextOrder, refreshOrder } = useOrder();
  
  // Use the order from context if available, otherwise fall back to props
  const currentOrder = contextOrder || order;

  // Refresh when the order ID changes
  useEffect(() => {
    if (contextOrder?.id !== order.id) {
      refreshOrder();
    }
  }, [order.id, contextOrder?.id]);

  // If the order has line items, display them in a table
  if (currentOrder.lineItems && currentOrder.lineItems.length > 0) {
    return <ItemsTable lineItems={currentOrder.lineItems} order={currentOrder} />;
  } else if (currentOrder.serviceType) {
    // For orders with serviceType but no line items, show service information
    // Different display format based on customer type
    if (isClientOrder(currentOrder)) {
      // Client order service display
      return <ClientServiceDetails order={currentOrder} onOrderUpdated={onOrderUpdated} />;
    } else {
      // Walk-in customer service display with distinct styling
      return <WalkInServiceDetails order={currentOrder} />;
    }
  } else {
    // Fallback for orders without line items or service type
    return <NoServiceDetails order={currentOrder} />;
  }
}
