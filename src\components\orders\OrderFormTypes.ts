
import { z } from "zod";

export const clientItemSchema = z.object({
  id: z.string(),
  name: z.string(),
  quantity: z.number().min(1, "Quantity must be at least 1").default(1),
  unitPrice: z.number().nonnegative("Price must be a positive number").default(0),
  item_type: z.string().optional(),
  treatments: z.object({
    useStainRemoval: z.boolean().default(false),
    useBeachTreatment: z.boolean().default(false),
    detergentType: z.enum(["none", "regular", "color"]).default("none"),
    conditionerType: z.enum(["none", "regular", "fresh", "floral"]).default("none"),
  }).optional().default({
    useStainRemoval: false,
    useBeachTreatment: false,
    detergentType: "none",
    conditionerType: "none"
  }),
  // Add a unique instance ID to differentiate between multiple instances of the same item
  instanceId: z.string().optional(),
});

export type ClientItemWithQuantity = z.infer<typeof clientItemSchema>;

// Define DryCleaningItem type needed in multiple components
export interface DryCleaningItem {
  type: string;
  price: number;
  quantity: number;
  total: number;
  name?: string;
  id?: string; // Add id property for compatibility
}

// Define type for service types
export type ServiceType = string;

// Define AddOnSelections type for pricing calculations
export interface AddOnSelections {
  detergentType: "none" | "regular" | "color";
  detergentQuantity: number;
  conditionerType: "none" | "regular" | "fresh" | "floral";
  conditionerQuantity: number;
  useStainRemover: boolean;
  useBleach: boolean;
}

// Define the PriceBreakdown type needed in PaymentInput.tsx
export interface PriceBreakdown {
  basePrice: number;
  addOnPrice: number;
  subtotal: number;
  vatAmount: number;
  totalPrice: number;
}

// Define ServiceWeight type for multi-service support
export interface ServiceWeight {
  serviceType: string;
  weightKilos: number;
}

export const orderFormSchema = z.object({
  clientId: z.string().optional(),
  serviceType: z.string().min(1, "Service type is required"),
  selectedServiceTypes: z.array(z.string()).min(1, "At least one service type is required"),
  isDryCleaning: z.boolean().optional().default(false),
  weight: z.number().optional(),
  pieces: z.number().optional(),
  deliveryDate: z.date().or(z.string()),
  deliveryFee: z.number().nonnegative().optional(),
  paymentMethod: z.string().optional(),
  amountPaid: z.number().nonnegative().optional(),
  notes: z.string().optional(),
  selectedClientItems: z.array(clientItemSchema).optional(),
  dryCleaningItems: z.array(z.any()).optional(), // Use any to be more flexible
  totalAmount: z.number().nonnegative().optional(),
  
  // Add missing fields used in the components
  customerName: z.string().optional(),
  phoneNumber: z.string().optional(),
  orderType: z.enum(["walk-in", "client"]).optional(),
  paidAmount: z.string().optional(),
  weightKilos: z.union([z.string(), z.number()]).optional(),
  numberOfPieces: z.union([z.string(), z.number()]).optional(),
  detergentType: z.enum(["none", "regular", "color"]).optional(),
  detergentQuantity: z.union([z.string(), z.number()]).optional(),
  conditionerType: z.enum(["none", "regular", "fresh", "floral"]).optional(),
  conditionerQuantity: z.union([z.string(), z.number()]).optional(),
  useStainRemover: z.boolean().optional(),
  useBleach: z.boolean().optional(),
  useDetergent: z.boolean().optional(),
  useFabricConditioner: z.boolean().optional(),
  serviceWeights: z.array(z.any()).optional(),
  pricingMethod: z.enum(["weight", "client_item", "dry_cleaning"]).optional(),
  
  // Add selectedItems for portal form
  selectedItems: z.array(z.any()).optional(),
})
// Add conditional validation based on orderType
.refine((data) => {
  // For client orders, clientId is required
  if (data.orderType === "client" && !data.clientId) {
    return false;
  }
  // For walk-in orders, clientId is not required
  return true;
}, {
  message: "Client is required for client orders",
  path: ["clientId"]
})
// Add validation for walk-in order customer information
.refine((data) => {
  // For walk-in orders, customer name is required
  if (data.orderType === "walk-in" && (!data.customerName || data.customerName.trim() === "")) {
    return false;
  }
  return true;
}, {
  message: "Customer name is required for walk-in orders",
  path: ["customerName"]
})
.refine((data) => {
  // For walk-in orders, phone number is required
  if (data.orderType === "walk-in" && (!data.phoneNumber || data.phoneNumber.trim() === "")) {
    return false;
  }
  return true;
}, {
  message: "Phone number is required for walk-in orders",
  path: ["phoneNumber"]
});

export type OrderFormValues = z.infer<typeof orderFormSchema>;

// Adding OrderFormProps type for order-form/OrderForm.tsx
export interface OrderFormProps {
  onSubmit: (data: OrderFormValues & { 
    orderAmount: number;
    vatAmount: number;
    subtotal: number;
    reference_code?: string;
    isDryCleaning: boolean; // Make sure isDryCleaning is required, not optional
  }) => void;
  initialOrderType?: "walk-in" | "client";
  initialValues?: Record<string, any>;
  isSubmitting?: boolean;
}
