
import { z } from "zod";
import { SERVICE_TYPES } from "./pricing/constants";

// Define the client item with quantity type
export interface ClientItemWithQuantity {
  id: string;
  name: string;
  description?: string;
  unit_price: number;
  quantity: number;
  total: number;
}

// Define dry cleaning item with quantity type
export interface DryCleaningItem {
  type: string;
  price: number;
  quantity: number;
  total: number;
}

// Define service type
export type ServiceType = typeof SERVICE_TYPES[keyof typeof SERVICE_TYPES];

// Define the price breakdown interface
export interface PriceBreakdown {
  basePrice: number;
  addOnPrice: number;
  subtotal: number;
  vatAmount: number;
  totalPrice: number;
}

// Define add-on selections interface
export interface AddOnSelections {
  detergentType: "none" | "regular" | "color";
  detergentQuantity: number;
  conditionerType: "none" | "regular" | "fresh" | "floral";
  conditionerQuantity: number;
  useStainRemover: boolean;
  useBleach: boolean;
}

// Basic validation schema for order form
export const orderFormSchema = z.object({
  // Order type
  orderType: z.enum(["walk-in", "client"]).default("walk-in"),
  
  // Service type for walk-in orders
  serviceType: z.string().default(SERVICE_TYPES.WASH_DRY_FOLD),
  
  // Client fields (only required for client orders)
  clientId: z.string().optional(),
  clientName: z.string().optional(),
  clientPrefix: z.string().optional(),
  
  // Customer fields (only required for walk-in orders)
  customerName: z.string().optional(),
  phoneNumber: z.string().optional(),
  
  // Conditional validation for customer fields
  // Only required for walk-in orders
  deliveryDate: z.string().min(1, "Delivery date is required"),
  
  // Laundry details
  weightKilos: z.string().refine(val => {
    const num = parseFloat(val);
    return !isNaN(num) && num > 0;
  }, "Weight must be greater than 0"),
  numberOfPieces: z.string().min(1, "Number of pieces is required"),
  pricingMethod: z.enum(["weight", "client_item", "dry_cleaning"]).default("weight"),
  
  // Add-ons
  detergentType: z.enum(["none", "regular", "color"]).default("none"),
  detergentQuantity: z.string().optional(),
  conditionerType: z.enum(["none", "regular", "fresh", "floral"]).default("none"),
  conditionerQuantity: z.string().optional(),
  useStainRemover: z.boolean().optional(),
  useBleach: z.boolean().optional(),
  useDetergent: z.boolean().optional(),
  useFabricConditioner: z.boolean().optional(),
  isDryCleaning: z.boolean().default(false), // Make isDryCleaning required with a default value
  
  // Payment
  paidAmount: z.string(),
  
  // Client items for client orders
  selectedClientItems: z.array(z.any()).optional(),
  
  // Dry cleaning items for walk-in orders with dry cleaning service
  dryCleaningItems: z.array(z.any()).optional(),
}).superRefine((data, ctx) => {
  // Custom validation based on order type
  if (data.orderType === "walk-in") {
    // For walk-in orders, customer name and phone are required
    if (!data.customerName || data.customerName.trim() === "") {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Customer name is required for walk-in orders",
        path: ["customerName"],
      });
    }
    
    if (!data.phoneNumber || data.phoneNumber.trim() === "") {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Valid phone number is required for walk-in orders",
        path: ["phoneNumber"],
      });
    }
    
    // For walk-in orders with weight pricing, validate weight
    if (data.pricingMethod === "weight") {
      const weight = parseFloat(data.weightKilos);
      if (isNaN(weight) || weight <= 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Valid weight is required for weight-based pricing",
          path: ["weightKilos"],
        });
      }
      
      // Service type specific validations
      if (data.serviceType === SERVICE_TYPES.WASH_DRY_FOLD || 
          data.serviceType === SERVICE_TYPES.WASH_DRY_PRESS || 
          data.serviceType === SERVICE_TYPES.WASH_DRY_FOLD_SPECIAL) {
        // Just display a warning if less than minimum, pricing already handles this
      }
    }
    
    // For dry cleaning service, require at least one item
    if (data.serviceType === SERVICE_TYPES.DRY_CLEANING) {
      if (!data.dryCleaningItems || data.dryCleaningItems.length === 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Please select at least one dry cleaning item",
          path: ["dryCleaningItems"],
        });
      }
    }
  } else if (data.orderType === "client") {
    // For client orders, client ID is required
    if (!data.clientId || data.clientId.trim() === "") {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Please select a client",
        path: ["clientId"],
      });
    }
    
    // For client orders with client_item pricing, selected items are required
    if (data.pricingMethod === "client_item" && 
        (!data.selectedClientItems || data.selectedClientItems.length === 0)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Please select at least one item",
        path: ["selectedClientItems"],
      });
    }
  }
});

// Export the type of the form values
export type OrderFormValues = z.infer<typeof orderFormSchema>;
