
import { useState } from "react";
import { Order, LineItem } from "@/types";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { Loader2, ClipboardCheck, Package2, X } from "lucide-react";
import { OrderItemsEditor } from "./OrderItemsEditor";
import { OrderAddOnsEditor } from "./OrderAddOnsEditor";
import { OrderStatusWorkflow } from "@/components/orders/workflow/OrderStatusWorkflow";
import { updateOrderStatus } from "@/services/orders";
import { useOrder } from "@/contexts/OrderContext";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { PrintButtons } from "../dialog/PrintButtons";
import { usePrintFunctions } from "../dialog/PrintingUtils";

interface JobOrderProcessDialogProps {
  order: Order;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onOrderUpdated: () => Promise<void>;
}

export function JobOrderProcessDialog({
  order,
  open,
  onOpenChange,
  onOrderUpdated,
}: JobOrderProcessDialogProps) {
  const [activeTab, setActiveTab] = useState("items");
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDryCleaning, setIsDryCleaning] = useState(order.isDryCleaning || false);
  const { toast } = useToast();
  const { refreshOrder } = useOrder();
  const { isPrinting, handlePrintReceipt, handlePrintJobOrder } = usePrintFunctions();

  const handleStatusChange = async (orderId: string, newStatus: string) => {
    setIsUpdating(true);
    try {
      await updateOrderStatus(orderId, newStatus);
      toast({
        title: "Status Updated",
        description: `Order status changed to ${newStatus}`
      });
      await refreshOrder();
      await onOrderUpdated();
    } catch (error) {
      console.error("Failed to update status:", error);
      toast({
        title: "Update Failed",
        description: "Could not update order status",
        variant: "destructive"
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const handleClose = () => {
    onOpenChange(false);
  };

  const serviceIsDryCleaning = order.serviceType === "dry_cleaning";
  const { userRole } = useOrder();
  const isStaff = userRole === 'staff' || userRole === 'admin';

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between">
          <DialogHeader className="flex-1">
            <DialogTitle className="flex items-center gap-2">
              {serviceIsDryCleaning ? 
                <Package2 className="h-5 w-5" /> :
                <ClipboardCheck className="h-5 w-5" />
              }
              Process Job Order {order.reference_code || order.id}
            </DialogTitle>
            <DialogDescription>
              {serviceIsDryCleaning 
                ? "Manage dry cleaning items, treatments, and order status" 
                : "Process laundry order, manage items, add-ons, and update status"}
            </DialogDescription>
          </DialogHeader>
          
          {/* Add styled close button */}
          <Button 
            variant="outline" 
            size="icon" 
            className="rounded-full"
            onClick={handleClose}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        <div className="py-4">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
            <div>
              <h3 className="font-medium">{order.customer?.name}</h3>
              <p className="text-sm text-muted-foreground">
                Delivery Date: {order.deliveryDate}
              </p>
            </div>
            <OrderStatusBadge status={order.status} className="mt-2 md:mt-0"/>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="items">Items & Treatments</TabsTrigger>
              <TabsTrigger value="add-ons">Add-ons & Status</TabsTrigger>
            </TabsList>
            
            <TabsContent value="items" className="space-y-4 py-4">
              <OrderItemsEditor 
                order={order}
                onOrderUpdated={onOrderUpdated}
                isDryCleaning={isDryCleaning || serviceIsDryCleaning}
              />

              {isStaff && (
                <div className="mt-6 border-t pt-4">
                  <h4 className="font-medium mb-3">Print Job Order</h4>
                  <PrintButtons
                    onPrintReceipt={() => handlePrintReceipt(order)}
                    onPrintJobOrder={() => handlePrintJobOrder(order)}
                    isPrinting={isPrinting}
                    isStaff={isStaff}
                  />
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="add-ons" className="space-y-4 py-4">
              <OrderAddOnsEditor 
                order={order}
                onOrderUpdated={onOrderUpdated}
                isDryCleaning={isDryCleaning}
                setIsDryCleaning={setIsDryCleaning}
              />
              
              <div className="mt-6 border-t pt-6">
                <h3 className="text-lg font-medium mb-4">Update Status</h3>
                <OrderStatusWorkflow
                  order={order}
                  onStatusChange={handleStatusChange}
                />
              </div>
            </TabsContent>
          </Tabs>
        </div>

        <DialogFooter>
          <Button
            onClick={handleClose}
            disabled={isUpdating}
          >
            {isUpdating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              "Close"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

// Import at the top of the file but add the component here for clarity
function OrderStatusBadge({ status, className = "" }: { status: string, className?: string }) {
  let badgeClass = "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium " + className;
  
  switch (status) {
    case "processing":
      badgeClass += " bg-blue-100 text-blue-800";
      break;
    case "ready_for_pickup":
      badgeClass += " bg-green-100 text-green-800";
      break;
    case "fulfilled":
      badgeClass += " bg-purple-100 text-purple-800";
      break;
    case "cancelled":
      badgeClass += " bg-red-100 text-red-800";
      break;
    default:
      badgeClass += " bg-gray-100 text-gray-800";
  }
  
  return (
    <span className={badgeClass}>
      {status.replace(/_/g, " ")}
    </span>
  );
}
