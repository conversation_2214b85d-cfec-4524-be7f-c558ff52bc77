
import { useState, useEffect } from "react";
import { Order } from "@/types";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { updateOrderStatus } from "@/services/orders";
import { useOrder } from "@/contexts/OrderContext";

// Import refactored components
import { ProcessDialogHeader } from "./process-dialog/ProcessDialogHeader";
import { OrderSummary } from "./process-dialog/OrderSummary";
import { DryCleaningDetails } from "./process-dialog/DryCleaningDetails";
import { OrderTabs } from "./process-dialog/OrderTabs";
import { DialogFooterActions } from "./process-dialog/DialogFooterActions";

interface JobOrderProcessDialogProps {
  order: Order;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onOrderUpdated: () => Promise<void>;
}

export function JobOrderProcessDialog({
  order,
  open,
  onOpenChange,
  onOrderUpdated,
}: JobOrderProcessDialogProps) {
  const [isUpdating, setIsUpdating] = useState(false);
  const { toast } = useToast();
  const { refreshOrder } = useOrder();

  // Log the dry cleaning information when the dialog opens
  useEffect(() => {
    if (open) {
      console.log("JobOrderProcessDialog - Order details:", {
        id: order.id, 
        reference: order.reference_code,
        isDryCleaning: order.isDryCleaning,
        serviceType: order.serviceType,
        dryCleaningItemCount: order.dryCleaningItems?.length || 0,
        dryCleaningItems: order.dryCleaningItems
      });
    }
  }, [open, order]);

  const handleStatusChange = async (orderId: string, newStatus: string) => {
    setIsUpdating(true);
    try {
      await updateOrderStatus(orderId, newStatus);
      toast({
        title: "Status Updated",
        description: `Order status changed to ${newStatus}`
      });
      await refreshOrder();
      await onOrderUpdated();
    } catch (error) {
      console.error("Failed to update status:", error);
      toast({
        title: "Update Failed",
        description: "Could not update order status",
        variant: "destructive"
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const handleClose = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <ProcessDialogHeader order={order} onClose={handleClose} />

        <div className="py-4">
          <OrderSummary order={order} />
          <DryCleaningDetails order={order} />

          <div className="mt-4">
            <OrderTabs 
              order={order} 
              onOrderUpdated={onOrderUpdated}
              handleStatusChange={handleStatusChange}
            />
          </div>
        </div>

        <DialogFooterActions isUpdating={isUpdating} onClose={handleClose} />
      </DialogContent>
    </Dialog>
  );
}
