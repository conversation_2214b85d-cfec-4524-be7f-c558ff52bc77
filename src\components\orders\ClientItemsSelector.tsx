
import React from "react";
import { UseFormReturn } from "react-hook-form";
import { OrderFormValues } from "@/components/orders/OrderFormTypes";
import { ShoppingCart, AlertTriangle } from "lucide-react";
import { useClientItems } from "@/hooks/useClientItems";
import { useClientItemsSelection } from "@/hooks/useClientItemSelection";
import { ClientItemsTable } from "./ClientItemsTable";
import { useIsMobile } from "@/hooks/use-mobile";
import { ClientItemsLoadingSkeleton } from "@/components/clients/items/ClientItemsLoadingSkeleton";
import { ClientItem } from "@/services/clientItem/types";

interface ClientItemSelectorProps {
  form: UseFormReturn<OrderFormValues>;
}

export function ClientItemsSelector({ form }: ClientItemSelectorProps) {
  const clientId = form.watch("clientId");
  const { items: availableItems, isLoading } = useClientItems(clientId);
  const { 
    selectedItems, 
    handleItemToggle, 
    updateItemQuantity,
    DEFAULT_UNIT_PRICE
  } = useClientItemsSelection(form);
  const isMobile = useIsMobile();
  
  const formErrors = form.formState.errors;
  const hasItemErrors = !!formErrors.selectedClientItems;

  console.log("ClientItemsSelector rendering with clientId:", clientId);
  console.log("Current selected items:", selectedItems);

  if (isLoading) {
    return <ClientItemsLoadingSkeleton />;
  }

  if (!clientId) {
    return null;
  }

  // Group items by name for better organization
  const itemsByName = availableItems.reduce((acc: Record<string, ClientItem[]>, item) => {
    if (!acc[item.name]) {
      acc[item.name] = [];
    }
    acc[item.name].push(item);
    return acc;
  }, {});

  if (availableItems.length === 0) {
    return (
      <div className="p-6 bg-gray-50 rounded-lg border border-gray-200">
        <p className="text-center text-muted-foreground">No items available for this client.</p>
      </div>
    );
  }

  return (
    <div className={isMobile ? "mt-2" : "mt-4"}>
      <div className={`flex justify-between items-center ${isMobile ? "mb-2 flex-wrap" : "mb-4"}`}>
        <h3 className={`font-semibold ${isMobile ? "text-base" : "text-lg"} flex items-center`}>
          <ShoppingCart className={`${isMobile ? "mr-1 h-4 w-4" : "mr-2 h-5 w-5"} text-laundry-blue`} />
          Service Items
          <span className="text-red-500 ml-1">*</span>
        </h3>
      </div>

      <div className={`bg-white rounded-lg border ${hasItemErrors ? 'border-red-500' : 'border-gray-200'} shadow-sm overflow-hidden`}>
        <ClientItemsTable
          items={availableItems}
          selectedItems={selectedItems}
          onItemToggle={handleItemToggle}
          onQuantityChange={updateItemQuantity}
          unitPrice={DEFAULT_UNIT_PRICE}
        />
      </div>
      
      {hasItemErrors && (
        <div className="mt-2 flex items-center text-red-500 text-sm">
          <AlertTriangle className="h-4 w-4 mr-1" />
          <span>Please select at least one item</span>
        </div>
      )}
    </div>
  );
}
