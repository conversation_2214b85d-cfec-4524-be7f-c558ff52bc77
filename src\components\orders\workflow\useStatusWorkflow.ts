
import { useState, useCallback } from "react";
import { Order } from "@/types";

interface UseStatusWorkflowProps {
  order: Order;
  onStatusChange: (orderId: string, newStatus: string) => Promise<void>;
}

export function useStatusWorkflow({ order, onStatusChange }: UseStatusWorkflowProps) {
  const [isUpdating, setIsUpdating] = useState(false);
  const [updatingToStatus, setUpdatingToStatus] = useState<string | null>(null);
  
  const handleStatusChange = useCallback(async (newStatus: string) => {
    if (!newStatus) return;
    
    setIsUpdating(true);
    setUpdatingToStatus(newStatus);
    
    try {
      await onStatusChange(order.id, newStatus);
    } catch (error) {
      console.error("Error updating order status:", error);
    } finally {
      setIsUpdating(false);
      setUpdatingToStatus(null);
    }
  }, [order.id, onStatusChange]);

  return {
    isUpdating,
    updatingToStatus,
    handleStatusChange
  };
}
