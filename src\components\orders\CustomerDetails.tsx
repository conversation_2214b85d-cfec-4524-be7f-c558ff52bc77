
import { Input } from "@/components/ui/input";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { UseFormReturn } from "react-hook-form";
import { OrderFormValues } from "./OrderFormTypes";
import { User, Phone } from "lucide-react";

interface CustomerDetailsProps {
  form: UseFormReturn<OrderFormValues>;
}

export function CustomerDetails({ form }: CustomerDetailsProps) {
  // Use 'orderType' field from the form, with correct typing
  const orderType = form.watch("orderType");
  const isWalkIn = orderType === "walk-in";
  
  return (
    <>
      <h3 className="font-semibold text-lg mb-4 flex items-center">
        <User className="mr-2 h-5 w-5 text-laundry-blue" />
        Customer Information
        {isWalkIn && <span className="text-red-500 ml-1">*</span>}
      </h3>
      
      <div className="space-y-4">
        <FormField
          control={form.control}
          name="customerName"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-base">
                Customer Name
                {isWalkIn && <span className="text-red-500 ml-1">*</span>}
              </FormLabel>
              <FormControl>
                <div className="relative">
                  <User className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                  <Input 
                    placeholder="Enter customer name" 
                    className="pl-10 h-12 text-base" 
                    {...field} 
                    value={field.value || ""}
                  />
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="phoneNumber"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-base">
                Phone Number
                {isWalkIn && <span className="text-red-500 ml-1">*</span>}
              </FormLabel>
              <FormControl>
                <div className="relative">
                  <Phone className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                  <Input 
                    placeholder="Enter phone number" 
                    className="pl-10 h-12 text-base"
                    {...field}
                    value={field.value || ""}
                  />
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </>
  );
}
