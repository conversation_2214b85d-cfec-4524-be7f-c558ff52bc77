
import { Input } from "@/components/ui/input";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { UseFormReturn } from "react-hook-form";
import { OrderFormValues, PriceBreakdown } from "./OrderFormTypes";
import { CreditCard, PlusCircle, MinusCircle, Clock, CheckCircle2, Zap } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useState } from "react";

interface PaymentInputProps {
  form: UseFormReturn<OrderFormValues>;
  priceBreakdown?: PriceBreakdown;
}

export function PaymentInput({ form, priceBreakdown }: PaymentInputProps) {
  const commonAmounts = [100, 200, 500, 1000];
  // Fix the orderType access with correct typing
  const orderType = form.watch("orderType");
  
  // Helper function to increment payment by specific amount
  const addToPayment = (amount: number) => {
    const currentAmount = parseFloat(form.watch("paidAmount") || "0");
    form.setValue("paidAmount", (currentAmount + amount).toString());
  };
  
  // Helper function to clear payment
  const clearPayment = () => {
    form.setValue("paidAmount", "0");
  };
  
  // Set payment to 0 (for payment on pickup)
  const setPaymentOnPickup = () => {
    form.setValue("paidAmount", "0");
  };
  
  // Set payment to full amount (for complete payment)
  const setCompletePayment = () => {
    // Use the totalPrice from priceBreakdown if available, or fall back to 0
    const totalPrice = priceBreakdown?.totalPrice || 0;
    form.setValue("paidAmount", totalPrice.toString());
  };
  
  // Set payment to double amount (for rush order)
  const setRushOrderPayment = () => {
    // Calculate total price and double it
    const totalPrice = priceBreakdown?.totalPrice || 0;
    const doubledPrice = totalPrice * 2;
    form.setValue("paidAmount", doubledPrice.toString());
  };

  return (
    <div className="space-y-4">
      <h3 className="font-semibold text-lg flex items-center">
        <CreditCard className="mr-2 h-5 w-5 text-laundry-blue" />
        Payment Details
      </h3>
      
      {form && (
        <FormField
          control={form.control}
          name="paidAmount"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-base">Payment Received (₱)</FormLabel>
              <FormControl>
                <div className="relative">
                  <span className="absolute left-3 top-3 text-gray-500 text-lg">₱</span>
                  <Input 
                    type="number" 
                    min="0" 
                    step="0.01" 
                    placeholder="0.00" 
                    className="pl-8 h-12 text-lg font-semibold" 
                    {...field} 
                    value={field.value || ""}
                  />
                </div>
              </FormControl>
              <FormMessage />
              
              {/* Different buttons based on order type */}
              {orderType === "walk-in" ? (
                <div className="grid grid-cols-3 gap-2 mt-3">
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={setPaymentOnPickup}
                    className="h-12 border-amber-200 text-amber-600 hover:bg-amber-50"
                  >
                    <Clock className="mr-1 h-4 w-4" />
                    Payment on pickup
                  </Button>
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={setCompletePayment}
                    className="h-12 border-green-200 text-green-600 hover:bg-green-50"
                  >
                    <CheckCircle2 className="mr-1 h-4 w-4" />
                    Complete payment
                  </Button>
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={setRushOrderPayment}
                    className="h-12 border-red-200 text-red-600 hover:bg-red-50"
                  >
                    <Zap className="mr-1 h-4 w-4" />
                    Rush order
                  </Button>
                </div>
              ) : (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mt-3">
                  {commonAmounts.map((amount) => (
                    <Button 
                      key={amount} 
                      type="button" 
                      variant="outline" 
                      onClick={() => addToPayment(amount)}
                      className="h-12"
                    >
                      <PlusCircle className="mr-1 h-4 w-4 text-laundry-green" />
                      ₱{amount}
                    </Button>
                  ))}
                </div>
              )}
              
              <Button 
                type="button" 
                variant="outline" 
                onClick={clearPayment} 
                className="w-full mt-2 border-red-200 text-red-500 hover:bg-red-50 h-12"
              >
                <MinusCircle className="mr-1 h-4 w-4" />
                Clear Amount
              </Button>
            </FormItem>
          )}
        />
      )}
    </div>
  );
}
