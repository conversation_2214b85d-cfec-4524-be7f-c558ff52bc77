import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { UseFormReturn } from "react-hook-form";
import { OrderFormValues, DryCleaningItem } from "../OrderFormTypes";
import { Plus, Minus, AlertTriangle } from "lucide-react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { getDryCleaningPrices } from "@/services/pricing/pricingService";

interface DryCleaningItemsProps {
  form: UseFormReturn<OrderFormValues>;
}

export function DryCleaningItems({ form }: DryCleaningItemsProps) {
  const [dryCleaningItems, setDryCleaningItems] = useState<DryCleaningItem[]>([]);
  const [prices, setPrices] = useState<Record<string, number>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Load dry cleaning prices from the service
  useEffect(() => {
    async function loadPrices() {
      try {
        setIsLoading(true);
        setError(null);
        const currentPrices = await getDryCleaningPrices();
        console.log("Loaded dry cleaning prices:", currentPrices);
        setPrices(currentPrices);
        setIsLoading(false);
      } catch (err) {
        console.error("Error loading dry cleaning prices:", err);
        setError("Failed to load pricing information");
        setIsLoading(false);
      }
    }
    
    loadPrices();
  }, []);
  
  // Update form when items change
  useEffect(() => {
    form.setValue("dryCleaningItems", dryCleaningItems);
  }, [dryCleaningItems, form]);
  
  // Define the dry cleaning item types and their display names
  const itemTypes = [
    { type: "BARONG_TAGALOG_JUSI", displayName: "Barong Tagalog (Jusi)", priceKey: "BARONG_TAGALOG_JUSI" },
    { type: "BARONG_TAGALOG_PINA", displayName: "Barong Tagalog (Piña)", priceKey: "BARONG_TAGALOG_PINA" },
    { type: "COAT", displayName: "Coat", priceKey: "COAT" },
    { type: "LONG_PANTS", displayName: "Long Pants", priceKey: "LONG_PANTS" },
    { type: "SIMPLE_GOWN", displayName: "Simple Gown", priceKey: "SIMPLE_GOWN" },
    { type: "BEADED_GOWN", displayName: "Beaded Gown", priceKey: "BEADED_GOWN" },
    { type: "WEDDING_GOWN", displayName: "Wedding Gown", priceKey: "WEDDING_GOWN" },
    { type: "SHOES", displayName: "Shoes", priceKey: "SHOES" },
    { type: "BLOUSE", displayName: "Blouse", priceKey: "BLOUSE" },
    { type: "SKIRT", displayName: "Skirt", priceKey: "SKIRT" },
    { type: "POLO", displayName: "Polo", priceKey: "POLO" },
    { type: "SLACKS_TROUSERS", displayName: "Slacks/Trousers", priceKey: "SLACKS_TROUSERS" },
  ];
  
  const updateItemQuantity = (itemType: string, displayName: string, price: number, amount: number) => {
    const existingItemIndex = dryCleaningItems.findIndex(item => item.type === itemType);
    
    if (existingItemIndex >= 0) {
      // Update existing item
      const updatedItems = [...dryCleaningItems];
      const newQuantity = Math.max(0, updatedItems[existingItemIndex].quantity + amount);
      
      if (newQuantity === 0) {
        // Remove item if quantity is zero
        updatedItems.splice(existingItemIndex, 1);
      } else {
        // Update quantity
        updatedItems[existingItemIndex] = {
          ...updatedItems[existingItemIndex],
          quantity: newQuantity,
          total: price * newQuantity
        };
      }
      
      setDryCleaningItems(updatedItems);
    } else if (amount > 0) {
      // Add new item
      const newItem: DryCleaningItem = {
        type: itemType,
        price,
        quantity: 1,
        total: price
      };
      
      setDryCleaningItems([...dryCleaningItems, newItem]);
    }
  };
  
  const getItemQuantity = (itemType: string): number => {
    const item = dryCleaningItems.find(item => item.type === itemType);
    return item ? item.quantity : 0;
  };
  
  const formatPrice = (price: number): string => {
    return `₱${price.toFixed(2)}`;
  };
  
  if (isLoading) {
    return <div className="p-4 text-center">Loading dry cleaning prices...</div>;
  }
  
  if (error) {
    return (
      <div className="p-4 border rounded-md bg-red-50 text-red-700">
        <AlertTriangle className="inline-block mr-2 h-5 w-5" />
        {error}
      </div>
    );
  }
  
  // Calculate subtotal for all selected items
  const subtotal = dryCleaningItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  
  return (
    <div className="space-y-4">
      <h3 className="font-medium mb-3">Select Dry Cleaning Items</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        {itemTypes.map(({ type, displayName, priceKey }) => {
          const price = prices[priceKey] || 0;
          return (
            <div key={type} className="border rounded-md p-3 bg-white">
              <div className="flex justify-between items-center">
                <div>
                  <h5 className="font-medium text-sm">{displayName}</h5>
                  <p className="text-xs text-gray-500">{formatPrice(price)}</p>
                </div>
                <div className="flex items-center space-x-2">
                  <Button 
                    type="button" 
                    size="sm" 
                    variant="outline" 
                    className="h-7 w-7 p-0"
                    onClick={() => updateItemQuantity(type, displayName, price, -1)}
                    disabled={getItemQuantity(type) === 0}
                  >
                    <Minus className="h-3 w-3" />
                  </Button>
                  <span className="w-4 text-center text-sm">{getItemQuantity(type)}</span>
                  <Button
                    type="button"
                    size="sm"
                    variant="outline"
                    className="h-7 w-7 p-0"
                    onClick={() => updateItemQuantity(type, displayName, price, 1)}
                  >
                    <Plus className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            </div>
          );
        })}
      </div>
      
      {dryCleaningItems.length > 0 && (
        <div className="mt-4 p-3 border rounded-md bg-amber-50">
          <h5 className="font-medium">Selected Items</h5>
          <div className="space-y-1 mt-2">
            {dryCleaningItems.map((item, index) => {
              const itemType = itemTypes.find(t => t.type === item.type);
              const displayName = itemType?.displayName || item.type;
              return (
                <div key={index} className="flex justify-between text-sm">
                  <span>{displayName} x{item.quantity}</span>
                  <span>{formatPrice(item.price * item.quantity)}</span>
                </div>
              );
            })}
            <div className="border-t pt-2 mt-2 font-medium flex justify-between">
              <span>Total</span>
              <span>{formatPrice(subtotal)}</span>
            </div>
          </div>
        </div>
      )}
      
      {dryCleaningItems.length === 0 && (
        <div className="text-amber-600 text-sm mt-2">
          <AlertTriangle className="inline-block mr-1 h-4 w-4" />
          Please select at least one dry cleaning item
        </div>
      )}
    </div>
  );
}
