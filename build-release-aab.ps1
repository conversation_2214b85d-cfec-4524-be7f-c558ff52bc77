# Script to build Android App Bundle (AAB) for Google Play Store
Write-Host "Building Android App Bundle (AAB) for CMC Laundry POS..." -ForegroundColor Cyan

# Set Android SDK environment variable
$env:ANDROID_HOME = "C:\Users\<USER>\AppData\Local\Android\Sdk"
Write-Host "Using Android SDK at: $env:ANDROID_HOME" -ForegroundColor Green

# Build the web app
Write-Host "Building web app..." -ForegroundColor Yellow
npm run build

# Sync with Capacitor
Write-Host "Syncing with Capacitor..." -ForegroundColor Yellow
npx cap sync

# Build the AAB
Write-Host "Building Android App Bundle (AAB)..." -ForegroundColor Yellow
Set-Location -Path android
./gradlew.bat bundleRelease
Set-Location -Path ..

# Check if build was successful
$aabPath = "android\app\build\outputs\bundle\release\app-release.aab"
if (Test-Path $aabPath) {
    Write-Host "AAB built successfully at: $aabPath" -ForegroundColor Green

    # Copy AAB to root directory for easier access
    Copy-Item $aabPath -Destination "cmc-laundry-pos.aab"
    Write-Host "AAB copied to: cmc-laundry-pos.aab" -ForegroundColor Green
} else {
    Write-Host "AAB build failed!" -ForegroundColor Red
}
