import React from "react";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { UseFormReturn } from "react-hook-form";
import { OrderFormValues } from "../OrderFormTypes";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { AndroidDatePicker } from "@/components/ui/android-date-picker";

interface DeliveryDateInputProps {
  form: UseFormReturn<OrderFormValues>;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

export function DeliveryDateInput({
  form,
  onChange
}: DeliveryDateInputProps) {
  // Function to handle date selection
  const handleDateSelect = (date: Date) => {
    const dateStr = format(date, "yyyy-MM-dd");
    console.log("DeliveryDateInput: Date selected:", { date, dateStr });
    form.setValue("deliveryDate", dateStr);

    // Create synthetic event to trigger onChange
    const syntheticEvent = {
      target: {
        value: dateStr,
        name: "deliveryDate",
      },
    } as React.ChangeEvent<HTMLInputElement>;
    onChange(syntheticEvent);
  };

  return (
    <FormField
      control={form.control}
      name="deliveryDate"
      render={({ field }) => (
        <FormItem>
          <FormLabel className="text-base flex items-center">
            <CalendarIcon className="mr-2 h-4 w-4" />
            Delivery Date
          </FormLabel>
          <FormControl>
            <AndroidDatePicker
              value={field.value ? new Date(field.value) : undefined}
              onChange={handleDateSelect}
              placeholder="Select date"
              minDate={new Date()}
              className="w-full"
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
