import React, { useState } from "react";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { UseFormReturn } from "react-hook-form";
import { OrderFormValues } from "../OrderFormTypes";
import { format } from "date-fns";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { CalendarIcon } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";

interface DeliveryDateInputProps {
  form: UseFormReturn<OrderFormValues>;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

export function DeliveryDateInput({
  form,
  onChange
}: DeliveryDateInputProps) {
  const [open, setOpen] = useState(false);
  const isMobile = useIsMobile();

  // Function to handle date selection
  const handleDateSelect = (date: Date | undefined) => {
    if (date) {
      const dateStr = format(date, "yyyy-MM-dd");
      form.setValue("deliveryDate", dateStr);

      // Create synthetic event to trigger onChange
      const syntheticEvent = {
        target: {
          value: dateStr,
          name: "deliveryDate",
        },
      } as React.ChangeEvent<HTMLInputElement>;
      onChange(syntheticEvent);
      setOpen(false);
    }
  };

  return (
    <FormField
      control={form.control}
      name="deliveryDate"
      render={({ field }) => (
        <FormItem>
          <FormLabel className="text-base flex items-center">
            <CalendarIcon className="mr-2 h-4 w-4" />
            Delivery Date
          </FormLabel>
          <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
              <FormControl>
                <Button
                  type="button"
                  variant="outline"
                  className={cn(
                    "w-full h-12 pl-10 text-base text-left font-normal touch-button flex justify-between items-center",
                    !field.value && "text-muted-foreground"
                  )}
                >
                  {field.value ? (
                    format(new Date(field.value), "PPP")
                  ) : (
                    <span>Select date</span>
                  )}
                  <CalendarIcon className="absolute left-3 top-3.5 h-5 w-5 text-gray-400 pointer-events-none" />
                </Button>
              </FormControl>
            </PopoverTrigger>
            <PopoverContent
              className={cn(
                "w-auto p-0 z-50",
                isMobile ? "touch-action-manipulation" : ""
              )}
              align="start"
              sideOffset={5}
            >
              <div className="touch-action-manipulation">
                <Calendar
                  mode="single"
                  selected={field.value ? new Date(field.value) : undefined}
                  onSelect={handleDateSelect}
                  disabled={(date) => date < new Date()}
                  className={cn(
                    isMobile ? "touch-action-manipulation" : ""
                  )}
                />
              </div>
            </PopoverContent>
          </Popover>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
