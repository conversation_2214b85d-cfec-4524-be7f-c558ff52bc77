
import React, { useState } from "react";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { UseFormReturn } from "react-hook-form";
import { OrderFormValues } from "../OrderFormTypes";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { 
  Popover, 
  PopoverContent, 
  PopoverTrigger 
} from "@/components/ui/popover";
import { Calendar as CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";

interface DeliveryDateInputProps {
  form: UseFormReturn<OrderFormValues>;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

export function DeliveryDateInput({ form, onChange }: DeliveryDateInputProps) {
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);
  
  // Set a default date (tomorrow) if no date is selected
  React.useEffect(() => {
    const currentValue = form.watch("deliveryDate");
    if (!currentValue) {
      // Set default delivery date to tomorrow
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      form.setValue("deliveryDate", tomorrow);
    }
  }, [form]);

  // Format date for display
  const formatDate = (date: Date | undefined) => {
    if (!date) return "Select a date";
    return format(date, "PPP");
  };

  return (
    <FormField
      control={form.control}
      name="deliveryDate"
      render={({ field }) => (
        <FormItem className="w-full">
          <FormLabel className="text-base flex items-center">
            <CalendarIcon className="mr-2 h-4 w-4" />
            Delivery Date
          </FormLabel>
          <Popover open={isPopoverOpen} onOpenChange={setIsPopoverOpen}>
            <PopoverTrigger asChild>
              <FormControl>
                <Button
                  variant="outline"
                  className={cn(
                    'w-full h-12 px-3 py-2 flex justify-between items-center text-left',
                    !field.value && 'text-muted-foreground'
                  )}
                >
                  <span className="truncate">{formatDate(field.value as Date)}</span>
                  <CalendarIcon className="h-4 w-4 ml-auto opacity-50" />
                </Button>
              </FormControl>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0 pointer-events-auto" align="start">
              <Calendar
                mode="single"
                selected={field.value as Date}
                onSelect={(date) => {
                  field.onChange(date);
                  setIsPopoverOpen(false);
                  // Create a synthetic event to pass to the onChange handler
                  const syntheticEvent = {
                    target: {
                      value: date ? date.toISOString() : '',
                      name: field.name
                    },
                    currentTarget: {
                      value: date ? date.toISOString() : '',
                      name: field.name
                    }
                  } as React.ChangeEvent<HTMLInputElement>;
                  
                  onChange(syntheticEvent);
                }}
                disabled={(date) => {
                  const today = new Date();
                  today.setHours(0, 0, 0, 0);
                  return date < today;
                }}
                initialFocus
                className="border rounded-md shadow-sm"
              />
            </PopoverContent>
          </Popover>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
