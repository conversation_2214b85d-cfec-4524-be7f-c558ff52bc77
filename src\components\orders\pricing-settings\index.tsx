
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form } from "@/components/ui/form";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ADDON_PRICES, BASE_PRICE_PER_KILO, VAT_RATE } from "../pricing/constants";
import { pricingSchema, PricingValues } from "./types";
import { GeneralPricing } from "./GeneralPricing";
import { DetergentOptions } from "./DetergentOptions";
import { ConditionerOptions } from "./ConditionerOptions";
import { SpecialTreatments } from "./SpecialTreatments";
import { InventorySync } from "./InventorySync";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { Loader2, AlertTriangle } from "lucide-react";
import { useAuth } from "@/contexts/auth";
import { 
  invalidatePricingCache, 
  DatabasePricingSettings, 
  convertToSettingsType,
  checkPricingPermissions
} from "@/services/pricing/pricingService";
import { OrdersNavigation } from "@/components/orders/page/OrdersNavigation";
import { DryCleaningPricing } from "./DryCleaningPricing";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

export default function PricingSettings() {
  const [isSaved, setIsSaved] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("general");
  const [permissionError, setPermissionError] = useState<string | null>(null);
  const { toast } = useToast();
  const { userRole } = useAuth();
  const isAdmin = userRole === 'admin';
  
  const form = useForm<PricingValues>({
    resolver: zodResolver(pricingSchema),
    defaultValues: {
      baseWeightPrice: BASE_PRICE_PER_KILO.toString(),
      detergentRegularPrice: ADDON_PRICES.detergent.regular.toString(),
      detergentColorPrice: ADDON_PRICES.detergent.color.toString(),
      conditionerRegularPrice: ADDON_PRICES.fabricConditioner.regular.toString(),
      conditionerFreshPrice: ADDON_PRICES.fabricConditioner.fresh.toString(),
      conditionerFloralPrice: ADDON_PRICES.fabricConditioner.floral.toString(),
      stainRemoverPrice: ADDON_PRICES.stainRemover.toString(),
      bleachPrice: ADDON_PRICES.bleach.toString(),
      vatRate: (VAT_RATE * 100).toString()
    }
  });

  // Check permissions and fetch pricing settings
  useEffect(() => {
    const initialize = async () => {
      setIsLoading(true);
      try {
        // First check if user has permissions to access pricing settings
        const { hasPermission, message } = await checkPricingPermissions();
        
        if (!hasPermission) {
          setPermissionError(message);
          setIsLoading(false);
          return;
        }
        
        // If permissions check passed, fetch current settings
        const { data, error } = await supabase
          .from('pricing_settings')
          .select('*')
          .order('created_at', { ascending: false })
          .limit(1);
          
        if (error) {
          console.error("Error fetching pricing settings:", error);
          toast({
            title: "Error",
            description: "Failed to load pricing settings",
            variant: "destructive"
          });
          return;
        }
        
        if (data && data.length > 0) {
          // Safely convert JSON to our expected type
          const settings = convertToSettingsType(data[0].settings);

          // Set form values from database
          form.reset({
            baseWeightPrice: settings.baseWeightPrice,
            detergentRegularPrice: settings.detergentRegularPrice,
            detergentColorPrice: settings.detergentColorPrice,
            conditionerRegularPrice: settings.conditionerRegularPrice,
            conditionerFreshPrice: settings.conditionerFreshPrice,
            conditionerFloralPrice: settings.conditionerFloralPrice,
            stainRemoverPrice: settings.stainRemoverPrice,
            bleachPrice: settings.bleachPrice,
            vatRate: settings.vatRate
          });
        }
      } catch (error) {
        console.error("Error initializing pricing settings:", error);
        toast({
          title: "Error",
          description: "Failed to initialize pricing settings",
          variant: "destructive"
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    initialize();
  }, [form, toast]);
  
  async function onSubmit(data: PricingValues) {
    if (!isAdmin) {
      toast({
        title: "Permission Denied",
        description: "Only administrators can update pricing settings",
        variant: "destructive"
      });
      return;
    }
    
    setIsLoading(true);
    try {
      const numericData = {
        baseWeightPrice: data.baseWeightPrice,
        detergentRegularPrice: data.detergentRegularPrice,
        detergentColorPrice: data.detergentColorPrice,
        conditionerRegularPrice: data.conditionerRegularPrice,
        conditionerFreshPrice: data.conditionerFreshPrice,
        conditionerFloralPrice: data.conditionerFloralPrice,
        stainRemoverPrice: data.stainRemoverPrice,
        bleachPrice: data.bleachPrice,
        vatRate: data.vatRate
      };
      console.log("Saving pricing settings:", numericData);

      // Insert new pricing settings
      const { error } = await supabase
        .from('pricing_settings')
        .insert({
          settings: numericData,
          created_by: (await supabase.auth.getUser()).data.user?.id || null
        });
        
      if (error) {
        console.error("Error saving pricing settings:", error);
        
        // Check if this is a permissions error
        if (error.code === '42501' || error.message.includes('permission denied') || error.message.includes('policy')) {
          toast({
            title: "Permission Denied",
            description: "You don't have permission to update pricing settings. Only administrators can manage pricing.",
            variant: "destructive"
          });
          return;
        }
        
        toast({
          title: "Error",
          description: `Failed to save pricing settings: ${error.message}`,
          variant: "destructive"
        });
        return;
      }

      // Invalidate the pricing cache to fetch the new settings
      invalidatePricingCache();

      // Log audit record
      await supabase.from('audit_logs').insert({
        entity_type: 'pricing_settings',
        action: 'update',
        details: {
          newSettings: numericData
        }
      });
      
      toast({
        title: "Settings Updated",
        description: "Pricing settings have been successfully updated"
      });
      
      setIsSaved(true);
      setTimeout(() => setIsSaved(false), 3000);
    } catch (error: any) {
      console.error("Error saving pricing settings:", error);
      toast({
        title: "Error",
        description: `Failed to save pricing settings: ${error.message || "Unknown error"}`,
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }
  
  if (permissionError) {
    return (
      <>
        <OrdersNavigation />
        <Card className="bg-white shadow-md">
          <CardHeader>
            <CardTitle className="text-lg font-medium">Pricing Settings</CardTitle>
          </CardHeader>
          <CardContent>
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Access Restricted</AlertTitle>
              <AlertDescription>{permissionError}</AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </>
    );
  }
  
  if (!isAdmin) {
    return (
      <>
        <OrdersNavigation />
        <Card className="bg-white shadow-md">
          <CardHeader>
            <CardTitle className="text-lg font-medium">Pricing Settings</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-amber-50 border border-amber-200 p-4 rounded-md">
              <h2 className="text-amber-800 font-semibold">Access Restricted</h2>
              <p className="text-amber-700">
                You need administrator privileges to view pricing settings.
              </p>
            </div>
          </CardContent>
        </Card>
      </>
    );
  }
  
  return (
    <>
      <OrdersNavigation />
      <Card className="bg-white shadow-md">
        <CardHeader>
          <CardTitle className="text-lg font-medium">Walk-in Customer Price Settings</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading && (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2">Loading pricing settings...</span>
            </div>
          )}
          
          {!isLoading && (
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid grid-cols-4 mb-4">
                <TabsTrigger value="general">General</TabsTrigger>
                <TabsTrigger value="addons">Add-ons</TabsTrigger>
                <TabsTrigger value="drycleaning">Dry Cleaning</TabsTrigger>
                <TabsTrigger value="inventory">Inventory</TabsTrigger>
              </TabsList>
              
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                  <TabsContent value="general" className="space-y-4">
                    <GeneralPricing form={form} />
                  </TabsContent>
                  
                  <TabsContent value="addons" className="space-y-6">
                    <DetergentOptions form={form} />
                    <ConditionerOptions form={form} />
                    <SpecialTreatments form={form} />
                  </TabsContent>
                  
                  <TabsContent value="drycleaning" className="space-y-4">
                    <DryCleaningPricing />
                  </TabsContent>
                  
                  <TabsContent value="inventory" className="space-y-4">
                    <InventorySync />
                  </TabsContent>
                  
                  <Button type="submit" className="bg-laundry-blue hover:bg-laundry-darkBlue" disabled={isLoading}>
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : isSaved ? "Saved!" : "Save Settings"}
                  </Button>
                </form>
              </Form>
            </Tabs>
          )}
        </CardContent>
      </Card>
    </>
  );
}
