
import { OrderSummary } from '@/hooks/orders/reports/types';
import { DateRange } from 'react-day-picker';

export interface ReportSummary {
  totalOrders: number;
  totalAmount: number;
  totalPaid: number;
  totalPayable: number;
  paidOrdersCount: number;
  unpaidOrdersCount: number;
  overdueOrdersCount: number;
  paidOrders: OrderSummary[];
  unpaidOrders: OrderSummary[];
  overdueOrders: OrderSummary[];
}

export interface DatePreset {
  label: string;
  getValue: () => {
    from: Date;
    to: Date;
  };
}

export interface ReportFiltersState {
  dateRange: DateRange;
  overdueFilter: boolean | null;
}
