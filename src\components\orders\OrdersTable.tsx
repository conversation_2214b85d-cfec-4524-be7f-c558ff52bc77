import React, { useState, useEffect } from "react";
import { Order } from "@/types";
import { Table, TableBody } from "@/components/ui/table";
import { OrdersTableHeader } from "./table/components/TableHeader";
import { EmptyState } from "./table/components/EmptyState";
import { TableRow as OrderTableRow } from "./table/TableRow";
import { ExpandedRow } from "./table/components/ExpandedRow";
import { getOrdersStore } from "@/services/orders/store";

interface OrdersTableProps {
  orders: Order[];
  onStatusChange?: (orderId: string, newStatus: string) => void;
  onPrintOrder?: (order: Order) => void;
  onViewOrder?: (order: Order) => void;
  onOrderDeleted?: () => void;  // Added this prop
}

export function OrdersTable({ 
  orders, 
  onStatusChange, 
  onPrintOrder,
  onViewOrder,
  onOrderDeleted  // Received prop
}: OrdersTableProps) {
  const [expandedRows, setExpandedRows] = useState<Record<string, boolean>>({});
  const [selectedRows, setSelectedRows] = useState<Record<string, boolean>>({});
  const [allOrders, setAllOrders] = useState<Order[]>([]);
  
  const toggleRowExpanded = (orderId: string) => {
    setExpandedRows(prev => ({
      ...prev,
      [orderId]: !prev[orderId]
    }));
  };

  const toggleSelectRow = (orderId: string) => {
    setSelectedRows(prev => ({
      ...prev,
      [orderId]: !prev[orderId]
    }));
  };
  
  const toggleSelectAll = () => {
    if (Object.keys(selectedRows).length === allOrders.length) {
      setSelectedRows({});
    } else {
      const newSelection: Record<string, boolean> = {};
      allOrders.forEach(order => {
        newSelection[order.id] = true;
      });
      setSelectedRows(newSelection);
    }
  };
  
  useEffect(() => {
    const storeOrders = getOrdersStore();
    
    if (orders.length > 0) {
      setAllOrders(orders);
      console.log("Using orders from props:", orders.length);
    } else if (storeOrders.length > 0) {
      setAllOrders(storeOrders);
      console.log("Using orders from local store:", storeOrders.length);
    } else {
      setAllOrders([]);
      console.log("No orders available in props or store");
    }
  }, [orders]);
  
  const isAllSelected = allOrders.length > 0 && Object.keys(selectedRows).length === allOrders.length;

  if (!Array.isArray(allOrders)) {
    console.error("OrdersTable received invalid orders data");
    return (
      <div className="rounded-md border p-4 text-center">
        Error: Invalid orders data
      </div>
    );
  }

  // Safety function to handle potentially undefined callbacks
  const handleStatusChange = async (orderId: string, newStatus: string) => {
    if (onStatusChange) {
      await onStatusChange(orderId, newStatus);
    }
  };

  return (
    <div className="rounded-md border">
      <Table>
        <OrdersTableHeader 
          isAllSelected={isAllSelected}
          onToggleSelectAll={toggleSelectAll}
        />
        <TableBody>
          {allOrders.length === 0 ? (
            <EmptyState />
          ) : (
            allOrders.map((order) => (
              <React.Fragment key={order.id}>
                <OrderTableRow
                  order={order}
                  isExpanded={!!expandedRows[order.id]}
                  isSelected={!!selectedRows[order.id]}
                  onToggleExpand={() => toggleRowExpanded(order.id)}
                  onToggleSelect={() => toggleSelectRow(order.id)}
                  onViewOrder={onViewOrder || (() => {})}
                />
                
                {expandedRows[order.id] && (
                  <ExpandedRow 
                    order={order}
                    onStatusChange={handleStatusChange}
                    onPrintOrder={onPrintOrder || (() => {})}
                    onOrderDeleted={onOrderDeleted}
                  />
                )}
              </React.Fragment>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
}
