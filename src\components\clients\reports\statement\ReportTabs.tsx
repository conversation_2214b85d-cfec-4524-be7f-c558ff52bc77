
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { OrdersTable } from './OrdersTable';
import { OrderSummary } from '@/hooks/orders/reports/types';
import { ReportSummary } from './types';

interface ReportTabsProps {
  activeTab: string;
  setActiveTab: (value: string) => void;
  summary: ReportSummary;
  orders: OrderSummary[];
  isLoading: boolean;
}

export function ReportTabs({ activeTab, setActiveTab, summary, orders, isLoading }: ReportTabsProps) {
  return (
    <Tabs defaultValue="summary" value={activeTab} onValueChange={setActiveTab} className="w-full">
      <TabsList className="mb-4">
        <TabsTrigger value="summary">Summary</TabsTrigger>
        <TabsTrigger value="detail">Detailed View</TabsTrigger>
      </TabsList>
      
      <TabsContent value="summary">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
            <div className="text-sm text-blue-700 mb-1">Total Orders</div>
            <div className="text-2xl font-bold">{summary.totalOrders}</div>
          </div>
          <div className="bg-green-50 p-4 rounded-lg border border-green-100">
            <div className="text-sm text-green-700 mb-1">Total Amount</div>
            <div className="text-2xl font-bold">{Intl.NumberFormat('en-US', { style: 'currency', currency: 'PHP' }).format(summary.totalAmount)}</div>
          </div>
          <div className="bg-amber-50 p-4 rounded-lg border border-amber-100">
            <div className="text-sm text-amber-700 mb-1">Balance Due</div>
            <div className="text-2xl font-bold">{Intl.NumberFormat('en-US', { style: 'currency', currency: 'PHP' }).format(summary.totalPayable)}</div>
          </div>
        </div>
        
        {summary.totalPayable > 0 && (
          <div className="bg-red-50 p-3 rounded-md mt-4 text-sm text-red-700 flex items-center">
            <div className="font-medium">Payment Reminder:</div>
            <div className="ml-1">Please settle your outstanding balance of {Intl.NumberFormat('en-US', { style: 'currency', currency: 'PHP' }).format(summary.totalPayable)}</div>
          </div>
        )}
      </TabsContent>
      
      <TabsContent value="detail">
        <OrdersTable orders={orders} isLoading={isLoading} />
      </TabsContent>
    </Tabs>
  );
}
