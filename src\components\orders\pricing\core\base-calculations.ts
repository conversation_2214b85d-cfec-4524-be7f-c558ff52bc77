
import { SERVICE_TYPES, BASE_PRICE_PER_KILO, VAT_RATE, 
         MINIMUM_WEIGHT_REGULAR, MINIMUM_WEIGHT_COMFORTERS, 
         MINIMUM_WEIGHT_SPECIAL, COMFORTERS_PRICE_PER_KILO,
         TOWELS_CURTAINS_LINENS_PRICE_PER_KILO } from "../constants";

// Default pricing settings
const DEFAULT_PRICING_SETTINGS = {
  SERVICE_TYPES,
  BASE_PRICE_PER_KILO,
  VAT_RATE,
  MINIMUM_WEIGHT_REGULAR,
  MINIMUM_WEIGHT_COMFORTERS,
  MINIMUM_WEIGHT_SPECIAL,
  COMFORTERS_PRICE_PER_KILO,
  TOWELS_CURTAINS_LINENS_PRICE_PER_KILO,
  ADDON_PRICES: {
    detergent: {
      regular: 25,
      color: 30
    },
    fabricConditioner: {
      regular: 25,
      fresh: 30,
      floral: 35
    },
    stainRemover: 15,
    bleach: 20
  }
};

// Cache the pricing settings
let pricingSettings = DEFAULT_PRICING_SETTINGS;

/**
 * Get or initialize pricing settings
 */
export const ensurePricingSettings = async () => {
  try {
    // In a real app, you might fetch this from an API
    // For now, we'll just use the default values
    return pricingSettings;
  } catch (error) {
    console.error("Error loading pricing settings:", error);
    return DEFAULT_PRICING_SETTINGS;
  }
};

/**
 * Calculate base price based on weight and service type
 * This function handles minimum weight enforcement
 */
export const calculateBasePrice = async (
  weightKilos: number, 
  serviceType: string
): Promise<number> => {
  // Apply minimum weights based on service type
  let minimumWeight: number;
  let actualWeight: number;
  let pricePerKilo: number;
  
  // Determine service-specific pricing
  switch(serviceType) {
    case SERVICE_TYPES.WASH_DRY_FOLD:
    case SERVICE_TYPES.WASH_DRY_PRESS:
    case SERVICE_TYPES.WASH_DRY_FOLD_SPECIAL:
      minimumWeight = MINIMUM_WEIGHT_REGULAR;
      pricePerKilo = BASE_PRICE_PER_KILO;
      break;
    
    case SERVICE_TYPES.COMFORTERS:
      minimumWeight = MINIMUM_WEIGHT_COMFORTERS;
      pricePerKilo = COMFORTERS_PRICE_PER_KILO;
      break;
      
    case SERVICE_TYPES.TOWELS_CURTAINS_LINENS:
      minimumWeight = MINIMUM_WEIGHT_SPECIAL;
      pricePerKilo = TOWELS_CURTAINS_LINENS_PRICE_PER_KILO;
      break;
      
    default:
      minimumWeight = MINIMUM_WEIGHT_REGULAR;
      pricePerKilo = BASE_PRICE_PER_KILO;
  }
  
  // Apply minimum weight
  actualWeight = Math.max(weightKilos, minimumWeight);
  
  // Calculate price
  return actualWeight * pricePerKilo;
};

/**
 * Calculate VAT amount based on subtotal
 */
export const calculateVAT = async (subtotal: number): Promise<number> => {
  return subtotal * VAT_RATE;
};
