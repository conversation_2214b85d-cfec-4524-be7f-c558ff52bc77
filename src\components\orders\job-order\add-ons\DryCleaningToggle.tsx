
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";

interface DryCleaningToggleProps {
  isDryCleaning: boolean;
  isSaving: boolean;
  onChange: (checked: boolean) => void;
}

export function DryCleaningToggle({ 
  isDryCleaning, 
  isSaving, 
  onChange 
}: DryCleaningToggleProps) {
  return (
    <div className="flex items-center space-x-2 py-2 mb-4">
      <Switch 
        id="dry-cleaning-toggle" 
        checked={isDryCleaning}
        onCheckedChange={onChange}
        disabled={isSaving}
      />
      <Label htmlFor="dry-cleaning-toggle" className="font-medium">
        Dry Cleaning Service
      </Label>
    </div>
  );
}
