import React, { useEffect, useState } from "react";
import { LineItem } from "@/types";
import { TableCell, TableRow } from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Trash2 } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { calculateLineItemTotal } from "@/utils/orderCalculations";
import { useClientItems } from "@/hooks/useClientItems";
import { Combobox } from "@/components/ui/combobox";
import { useToast } from "@/hooks/use-toast";

interface EditableItemRowProps {
  item: LineItem;
  clientId?: string;
  onChange: (updatedItem: LineItem) => void;
  onRemove: () => void;
}

export function EditableItemRow({
  item,
  clientId,
  onChange,
  onRemove
}: EditableItemRowProps) {
  const [localItem, setLocalItem] = useState<LineItem>({
    ...item, 
    name: item.name || '', 
    quantity: item.quantity || 1, 
    unitPrice: item.unitPrice || 0, 
    total: item.total || 0
  });

  const { items: clientItems = [], isLoading, error } = useClientItems(clientId || '');
  const [availableItems, setAvailableItems] = useState<{label: string, value: string, price: number}[]>([]);
  const { toast } = useToast();
  
  // Make sure we always have at least the custom item option
  const defaultItems = [{
    label: "Custom Item",
    value: "custom",
    price: 0
  }];
  
  // Debug logging
  useEffect(() => {
    if (error) {
      console.error("Error loading client items:", error);
      toast({
        title: "Error loading items",
        description: "There was an issue loading client items. You can still enter a custom item.",
        variant: "destructive"
      });
    }
  }, [error, toast]);
  
  // Prepare client items for dropdown with better error handling
  useEffect(() => {
    try {
      // Always start with the default custom item
      let formattedItems = [...defaultItems];
      
      // Only add client items if they exist and are in array format
      if (clientItems && Array.isArray(clientItems) && clientItems.length > 0) {
        console.debug(`Processing ${clientItems.length} client items for dropdown`);
        
        const clientItemOptions = clientItems.map(clientItem => ({
          label: clientItem.name || "Unnamed Item",
          value: clientItem.name || `item-${clientItem.id}`,
          price: clientItem.unit_price || 0
        }));
        
        // Add client items to the available options
        formattedItems = [...formattedItems, ...clientItemOptions];
      } else {
        console.debug("No client items available or still loading", { isLoading, clientItems });
      }
      
      console.debug("Final formatted dropdown options:", formattedItems);
      setAvailableItems(formattedItems);
    } catch (err) {
      console.error("Failed to process client items:", err);
      setAvailableItems([...defaultItems]);
      toast({
        title: "Warning",
        description: "Some items couldn't be loaded properly"
      });
    }
  }, [clientItems, isLoading, toast]);

  // Update local item when prop changes
  useEffect(() => {
    setLocalItem({
      ...item,
      name: item.name || '',
      quantity: item.quantity || 1,
      unitPrice: item.unitPrice || 0,
      total: item.total || 0
    });
  }, [item]);

  const handleItemNameChange = (value: string) => {
    console.debug("Item selection changed:", value);
    
    // Find the selected item from available options
    const selectedItem = availableItems.find(i => i.value === value);
    
    if (selectedItem) {
      if (selectedItem.value === "custom") {
        // Keep the existing name/price if selecting "Custom Item"
        updateLocalItem("name", localItem.name || "");
      } else {
        // Update both name and price from client item
        console.debug("Selected item details:", selectedItem);
        updateLocalItem("name", selectedItem.label);
        updateLocalItem("unitPrice", selectedItem.price);
      }
    } else {
      console.warn("Selected item not found in available items:", value);
    }
  };

  const handleCustomNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateLocalItem("name", e.target.value);
  };

  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>, field: keyof LineItem) => {
    const value = parseFloat(e.target.value);
    if (!isNaN(value) && value >= 0) {
      updateLocalItem(field, value);
    }
  };

  const handleTreatmentChange = (value: string) => {
    // Don't convert "none" to empty string anymore, treat it as a valid value
    updateLocalItem("treatmentDescription", value);
  };

  const updateLocalItem = (field: keyof LineItem, value: any) => {
    const updatedItem = {
      ...localItem,
      [field]: value
    };
    
    // Recalculate total when quantity, unit price, or treatment changes
    if (field === "quantity" || field === "unitPrice" || field === "treatmentDescription") {
      updatedItem.total = calculateLineItemTotal(updatedItem);
    }
    
    setLocalItem(updatedItem);
    onChange(updatedItem);
  };

  // Determine the correct value for the select component
  // If treatmentDescription is empty, use "none" as the value
  const treatmentValue = localItem.treatmentDescription || "none";

  // Determine the correct item name value for the combobox
  // If the name doesn't match any client item, treat it as custom
  const itemNameValue = (() => {
    if (!localItem.name) return "custom";
    const matchingItem = availableItems.find(i => i.label === localItem.name);
    return matchingItem ? matchingItem.value : "custom";
  })();

  return (
    <TableRow>
      <TableCell>
        {clientId && availableItems.length > 0 ? (
          <div className="flex flex-col gap-1">
            <Combobox
              options={availableItems}
              value={itemNameValue}
              onValueChange={handleItemNameChange}
              placeholder={isLoading ? "Loading items..." : "Select an item"}
              emptyMessage={isLoading ? "Loading..." : "No items found"}
              disabled={isLoading}
            />
            {(localItem.name === "" || itemNameValue === "custom") && (
              <Input
                value={localItem.name || ""}
                onChange={handleCustomNameChange}
                placeholder="Enter custom item name"
                className="mt-1"
              />
            )}
          </div>
        ) : (
          <Input
            value={localItem.name || ""}
            onChange={handleCustomNameChange}
            className="w-full"
            placeholder="Enter item name"
          />
        )}
      </TableCell>
      <TableCell>
        <Input
          type="number"
          min="1"
          value={localItem.quantity || 1}
          onChange={(e) => handleNumberChange(e, "quantity")}
          className="w-20"
        />
      </TableCell>
      <TableCell>
        <Input
          type="number"
          min="0"
          step="0.01"
          value={localItem.unitPrice || 0}
          onChange={(e) => handleNumberChange(e, "unitPrice")}
          className="w-24"
        />
      </TableCell>
      <TableCell>
        <Select 
          value={treatmentValue}
          onValueChange={handleTreatmentChange}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="No treatment" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="none">No treatment</SelectItem>
            <SelectItem value="Stain removal">Stain removal</SelectItem>
            <SelectItem value="Bleaching">Bleaching</SelectItem>
            <SelectItem value="Deep clean">Deep clean</SelectItem>
            <SelectItem value="Delicate wash">Delicate wash</SelectItem>
          </SelectContent>
        </Select>
      </TableCell>
      <TableCell className="text-right">
        ₱ {(localItem.total || 0).toFixed(2)}
      </TableCell>
      <TableCell>
        <Button
          variant="ghost" 
          size="icon"
          onClick={onRemove}
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </TableCell>
    </TableRow>
  );
}
