
import React from "react";
import { Form } from "@/components/ui/form";
import { CustomerDetails } from "../CustomerDetails";
import { LaundryDetails } from "../laundry-details";
import { PriceCalculation } from "../PriceCalculation";
import { AddOns } from "../add-ons";
import { PaymentInput } from "../PaymentInput";
import { FormActions } from "./FormActions";
import { DryCleaningIndicator } from "../job-order/add-ons/DryCleaningIndicator";
import { ServiceWeightsSection } from "../laundry-details/ServiceWeightsSection";
import { FormError } from "./FormError";
import { UseFormReturn } from "react-hook-form";
import { OrderFormValues, PriceBreakdown, DryCleaningItem } from "../OrderFormTypes";
import { SERVICE_TYPES } from "../pricing/constants";
import { extractFormErrors, logFormErrors } from "../client-order-form/utils/errorHandlingUtils";

interface OrderFormContentProps {
  form: UseFormReturn<OrderFormValues>;
  priceBreakdown: PriceBreakdown;
  handleSubmit: (data: OrderFormValues) => void;
  isSubmitting: boolean;
  isMobile: boolean;
  handleDateChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

export function OrderFormContent({
  form,
  priceBreakdown,
  handleSubmit,
  isSubmitting,
  isMobile,
  handleDateChange
}: OrderFormContentProps) {
  // Get necessary values from form
  const orderType = form.watch("orderType");
  const getPricingMethod = () => {
    // Get the pricingMethod value safely
    return form.watch("pricingMethod") || "weight";
  };
  
  const serviceType = form.watch("serviceType");
  const selectedServiceTypes = form.watch("selectedServiceTypes") || [SERVICE_TYPES.WASH_DRY_FOLD];
  const isDryCleaning = selectedServiceTypes.includes(SERVICE_TYPES.DRY_CLEANING);
  
  // Use the new extractFormErrors utility
  const { errorMessages, hasErrors } = extractFormErrors(form);
  
  // Log errors for debugging (will be removed in production)
  React.useEffect(() => {
    if (hasErrors) {
      logFormErrors(form);
    }
  }, [form, hasErrors]);
  
  // Extract values for PriceCalculation
  const detergentType = form.watch("detergentType") as "none" | "regular" | "color";
  const conditionerType = form.watch("conditionerType") as "none" | "regular" | "fresh" | "floral";
  const detergentQuantity = parseInt(String(form.watch("detergentQuantity")) || "1");
  const conditionerQuantity = parseInt(String(form.watch("conditionerQuantity")) || "1");
  const useStainRemover = form.watch("useStainRemover") || false;
  const useBleach = form.watch("useBleach") || false;
  const weightKilos = parseFloat(String(form.watch("weightKilos")) || "0");
  const numberOfPieces = parseInt(String(form.watch("numberOfPieces")) || "0");
  const clientItems = form.watch("selectedClientItems") || [];
  const rawDryCleaningItems = form.watch("dryCleaningItems") || [];
  const sectionClasses = isMobile ? "bg-gray-50 p-3 rounded-lg shadow-inner mb-3" : "bg-gray-50 p-4 rounded-lg shadow-inner";
  
  // Convert dryCleaningItems to proper format
  const dryCleaningItems: DryCleaningItem[] = rawDryCleaningItems.map((item: any) => {
    if (item.type && item.price && item.quantity) {
      return item as DryCleaningItem;
    }
    return {
      type: item.name || 'unknown',
      price: item.unitPrice || 0,
      quantity: item.quantity || 0,
      total: (item.unitPrice || 0) * (item.quantity || 0),
      id: item.id
    };
  });
  
  // Extract service weights and ensure they have required properties
  const serviceWeights = (form.watch("serviceWeights") || []).map(sw => ({
    serviceType: sw.serviceType,
    weightKilos: sw.weightKilos
  }));
  
  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className={`${isMobile ? 'space-y-3' : 'space-y-6'}`}>
        {/* Display validation errors */}
        {hasErrors && errorMessages.length > 0 && (
          <FormError errorMessages={errorMessages} />
        )}
        
        {/* Only show customer details for walk-in orders */}
        {orderType === "walk-in" && (
          <div className={sectionClasses}>
            <CustomerDetails form={form} />
          </div>
        )}
        
        {/* Show service weights section for multiple services */}
        {getPricingMethod() === "weight" && selectedServiceTypes.length > 1 && (
          <div className={sectionClasses}>
            <ServiceWeightsSection form={form} />
          </div>
        )}
        
        <div className={sectionClasses}>
          {isDryCleaning && <DryCleaningIndicator isDryCleaning={true} />}
          <LaundryDetails form={form} onChange={handleDateChange} />
        </div>
        
        <div className={sectionClasses}>
          <PriceCalculation 
            priceBreakdown={priceBreakdown} 
            detergentType={detergentType} 
            detergentQuantity={detergentQuantity} 
            conditionerType={conditionerType} 
            conditionerQuantity={conditionerQuantity} 
            useStainRemover={useStainRemover} 
            useBleach={useBleach} 
            pricingMethod={getPricingMethod()} 
            serviceType={serviceType} 
            weightKilos={weightKilos} 
            numberOfPieces={numberOfPieces} 
            clientItems={clientItems} 
            dryCleaningItems={dryCleaningItems} 
            selectedServiceTypes={selectedServiceTypes}
            serviceWeights={serviceWeights}
          />
        </div>

        <div className={sectionClasses}>
          <AddOns form={form} />
        </div>

        <div className={sectionClasses}>
          <PaymentInput form={form} priceBreakdown={priceBreakdown} />
        </div>

        <FormActions form={form} isSubmitting={isSubmitting} />
      </form>
    </Form>
  );
}
