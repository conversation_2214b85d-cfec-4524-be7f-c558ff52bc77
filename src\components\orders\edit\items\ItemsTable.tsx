
import { LineItem } from "@/types";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { ItemTableRow } from "./ItemTableRow";
import { getTreatmentPrice } from "@/utils/orderCalculations";

interface ItemsTableProps {
  items: LineItem[];
  clientId?: string;
  onRemoveItem: (id: string) => void;
  onItemChange: (id: string, field: keyof LineItem, value: any) => void;
}

export function ItemsTable({ 
  items, 
  clientId, 
  onRemoveItem, 
  onItemChange 
}: ItemsTableProps) {
  console.log("ItemsTable rendering with items:", items.length, items);

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Item Name</TableHead>
          <TableHead>Quantity</TableHead>
          <TableHead>Unit Price (₱)</TableHead>
          <TableHead>Treatment</TableHead>
          <TableHead>Total (₱)</TableHead>
          <TableHead className="w-[50px]">Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {items.length === 0 ? (
          <TableRow>
            <TableCell colSpan={6} className="text-center py-4 text-muted-foreground">
              No items. Add an item to this order.
            </TableCell>
          </TableRow>
        ) : (
          items.map((item, index) => (
            <ItemTableRow 
              key={`${item.id || ''}-${index}`}
              item={item}
              clientId={clientId}
              onRemove={onRemoveItem}
              onChange={(field, value) => onItemChange(item.id, field, value)}
            />
          ))
        )}
      </TableBody>
    </Table>
  );
}
