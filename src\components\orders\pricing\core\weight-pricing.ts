
import { getPricingSettings } from "@/services/pricing/pricingService";

// Calculate price based on weight and service type
export async function calculateWeightPrice(
  weightKilos: number,
  serviceType: string
): Promise<number> {
  const settings = await getPricingSettings();
  let pricePerKilo = settings.BASE_PRICE_PER_KILO || 100; // Default to 100 if not set
  
  // Apply minimum weight for various service types
  let effectiveWeight = weightKilos;
  
  if (serviceType === "wash_dry_fold" || serviceType === "wash_dry_press") {
    // Minimum 3kg for regular services
    effectiveWeight = Math.max(3, weightKilos);
  } else if (serviceType === "comforters") {
    // Minimum 3kg for comforters (if SPECIAL_SERVICES is configured)
    const minWeight = settings.SPECIAL_SERVICES?.minimumWeightComforters || 3;
    effectiveWeight = Math.max(minWeight, weightKilos);
    
    // Adjust price per kilo for comforters
    pricePerKilo = settings.SPECIAL_SERVICES?.comfortersPrice || 150;
  } else if (serviceType === "towels_curtains_linens") {
    // Minimum 2kg for special items
    const minWeight = settings.SPECIAL_SERVICES?.minimumWeightSpecial || 2;
    effectiveWeight = Math.max(minWeight, weightKilos);
    
    // Adjust price per kilo for special items
    pricePerKilo = settings.SPECIAL_SERVICES?.towelsCurtainsLinensPrice || 120;
  }
  
  return effectiveWeight * pricePerKilo;
}
