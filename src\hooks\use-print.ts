
import { useRef } from 'react';

type PrintFunction = () => void;

interface UseReactToPrintOptions {
  content: () => HTMLElement | null;
  documentTitle?: string;
  onBeforePrint?: () => void;
  onAfterPrint?: () => void;
  onPrintError?: (errorLocation: string, error: Error) => void;
}

export function useReactToPrint(options: UseReactToPrintOptions): PrintFunction {
  const {
    content: contentGetter,
    documentTitle,
    onBeforePrint = () => {},
    onAfterPrint = () => {},
    onPrintError = () => {}
  } = options;

  const printContentRef = useRef<HTMLIFrameElement | null>(null);

  const handlePrint = () => {
    try {
      onBeforePrint();

      const content = contentGetter();
      
      if (!content) {
        throw new Error('Content to print was not found');
      }

      // Create a new iframe
      const iframe = document.createElement('iframe');
      iframe.style.position = 'absolute';
      iframe.style.width = '0';
      iframe.style.height = '0';
      iframe.style.left = '-9999px';
      iframe.style.top = '-9999px';
      
      document.body.appendChild(iframe);
      printContentRef.current = iframe;

      // Get the iframe's document
      const iframeDoc = iframe.contentDocument || (iframe.contentWindow && iframe.contentWindow.document);
      
      if (!iframeDoc) {
        throw new Error('Could not access iframe document');
      }

      // Write content to the iframe
      iframeDoc.open();
      iframeDoc.write(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>${documentTitle || 'Print Document'}</title>
            <style>
              @media print {
                body {
                  font-family: Arial, sans-serif;
                }
                .print-hidden {
                  display: none !important;
                }
              }
            </style>
            <style>
              ${Array.from(document.querySelectorAll('style'))
                .map(style => style.innerHTML)
                .join('\n')}
            </style>
          </head>
          <body>
            ${content.outerHTML}
          </body>
        </html>
      `);
      iframeDoc.close();

      // Add the external stylesheets
      const links = document.querySelectorAll('link[rel="stylesheet"]');
      Array.from(links).forEach(link => {
        // Use typecasting to access href property on HTMLLinkElement
        const linkElement = link as HTMLLinkElement;
        const newLink = iframeDoc.createElement('link');
        newLink.rel = 'stylesheet';
        newLink.href = linkElement.href; // Now TypeScript knows href exists
        iframeDoc.head.appendChild(newLink);
      });

      // Wait for stylesheets to load
      setTimeout(() => {
        try {
          if (!iframe.contentWindow) {
            throw new Error('Iframe contentWindow is not available');
          }
          
          // Print
          iframe.contentWindow.focus();
          iframe.contentWindow.print();
          
          // Clean up
          setTimeout(() => {
            document.body.removeChild(iframe);
            printContentRef.current = null;
            onAfterPrint();
          }, 500);
        } catch (printError) {
          onPrintError('print', printError as Error);
        }
      }, 500);
    } catch (error) {
      onPrintError('setup', error as Error);
    }
  };

  return handlePrint;
}
