
import React from 'react';
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { ClientItemWithQuantity } from "./OrderFormTypes";

interface TreatmentOptionsProps {
  treatments: ClientItemWithQuantity['treatments'];
  onTreatmentChange: (updates: Partial<ClientItemWithQuantity['treatments']>) => void;
}

export function TreatmentOptions({ treatments, onTreatmentChange }: TreatmentOptionsProps) {
  // Use treatments or default values if undefined
  const {
    useStainRemoval = false,
    useBeachTreatment = false,
  } = treatments || {};

  const handleSwitchChange = (field: keyof ClientItemWithQuantity['treatments'], checked: boolean) => {
    onTreatmentChange({ [field]: checked });
  };

  return (
    <div className="space-y-3 w-full">
      <h4 className="text-sm font-medium mb-3">Treatment Options</h4>
      
      <div className="flex flex-col space-y-4 w-full">
        {/* Stain Removal */}
        <div className="flex items-center gap-3">
          <Switch
            id="stain-removal"
            checked={useStainRemoval}
            onCheckedChange={(checked) => handleSwitchChange('useStainRemoval', checked)}
          />
          <Label htmlFor="stain-removal" className="text-sm whitespace-nowrap">Stain Removal</Label>
        </div>
        
        {/* Bleach Treatment */}
        <div className="flex items-center gap-3">
          <Switch
            id="beach-treatment"
            checked={useBeachTreatment}
            onCheckedChange={(checked) => handleSwitchChange('useBeachTreatment', checked)}
          />
          <Label htmlFor="beach-treatment" className="text-sm whitespace-nowrap">Bleach Treatment</Label>
        </div>
      </div>
    </div>
  );
}
