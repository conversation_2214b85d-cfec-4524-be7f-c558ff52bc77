
import { Order } from "@/types";
import { SERVICE_TYPES } from "../../pricing/constants";
import { OrderStatusBadge } from "./OrderStatusBadge";

interface OrderSummaryProps {
  order: Order;
}

export function OrderSummary({ order }: OrderSummaryProps) {
  const isDryCleaningService = order?.serviceType === SERVICE_TYPES.DRY_CLEANING;
  const dryCleaningItems = order?.dryCleaningItems || [];
  const hasDryCleaningItems = dryCleaningItems && dryCleaningItems.length > 0;
  
  // Calculate total items
  const totalItems = dryCleaningItems?.reduce((sum, item) => sum + (item.quantity || 0), 0) || 0;

  // Format service type for display
  const formatServiceType = (type?: string) => {
    if (!type) return "";
    return type.replace(/_/g, ' ').toUpperCase();
  };
  
  return (
    <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
      <div>
        <h3 className="font-medium">{order?.customer?.name}</h3>
        <p className="text-sm text-muted-foreground">
          Delivery Date: {order?.deliveryDate}
        </p>
        <p className="text-sm font-medium mt-1">
          Service Type: <span className={isDryCleaningService ? "text-amber-600" : ""}>
            {formatServiceType(order?.serviceType)}
          </span>
        </p>
        {isDryCleaningService && hasDryCleaningItems && (
          <p className="text-xs mt-1 text-amber-600">
            {totalItems} items for processing
          </p>
        )}
      </div>
      <OrderStatusBadge status={order?.status} className="mt-2 md:mt-0"/>
    </div>
  );
}
