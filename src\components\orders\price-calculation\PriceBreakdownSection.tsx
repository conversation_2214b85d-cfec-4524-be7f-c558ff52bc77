
import { PriceBreakdown } from "../OrderFormTypes";
import { formatCurrency } from "./utils";

interface PriceBreakdownSectionProps {
  priceBreakdown: PriceBreakdown;
  serviceLabel: string;
  serviceRateDisplay: string;
  weightKilos: number;
  serviceType: string; // Changed to string to be more flexible
}

export function PriceBreakdownSection({
  priceBreakdown,
  serviceLabel,
  serviceRateDisplay,
  weightKilos,
  serviceType
}: PriceBreakdownSectionProps) {
  // Determine if this is for a client order
  const isClientOrder = serviceType.includes('client_');

  // Calculate effective weight (minimum weights apply for certain services)
  const effectiveWeight = 
    (serviceType === "wash_dry_fold" || serviceType === "wash_dry_press" || 
     serviceType === "wash_dry_fold_special") 
      ? Math.max(3, weightKilos) 
      : (serviceType === "comforters" || serviceType === "towels_curtains_linens")
        ? Math.max(2, weightKilos)
        : weightKilos;

  return (
    <div className="p-4 border-b">
      <div>
        <div className="flex justify-between mb-2">
          <span className="text-base flex items-center">
            {serviceLabel}
            <span className="text-xs text-gray-500 ml-2">
              {serviceRateDisplay}
            </span>
          </span>
          <span className="text-base font-medium">{formatCurrency(priceBreakdown.basePrice)}</span>
        </div>
        
        {/* Display total weight for walk-in orders */}
        {!isClientOrder && (
          <div className="text-sm text-gray-600 flex justify-between items-center mt-1">
            <span>Total weight:</span>
            <span className="font-medium">{effectiveWeight.toFixed(1)} kg</span>
          </div>
        )}
        
        {/* Only show weight-related notices for walk-in orders */}
        {!isClientOrder && (
          <div className="text-sm text-gray-600 mt-1">
            {weightKilos < 3 && (serviceType === "wash_dry_fold" || 
                                serviceType === "wash_dry_press" || 
                                serviceType === "wash_dry_fold_special") && (
              <div className="text-amber-600">
                * Minimum charge for 3kg applied (actual weight: {weightKilos}kg)
              </div>
            )}
            {weightKilos < 2 && (serviceType === "comforters" || 
                                serviceType === "towels_curtains_linens") && (
              <div className="text-amber-600">
                * Minimum charge for 2kg applied (actual weight: {weightKilos}kg)
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
