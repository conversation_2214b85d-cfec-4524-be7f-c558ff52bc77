import { useState } from 'react';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { ClientOrderForm } from './ClientOrderForm';
import { useOrderSubmission } from './order-dialog/useOrderSubmission';
import { useClientItems } from './order-dialog/useClientItems';
import { DialogHeader } from './order-dialog/DialogHeader';

interface ClientAddOrderDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  clientId: string | null;
  onOrderAdded?: () => void;
}

export function ClientAddOrderDialog({ 
  open, 
  onOpenChange,
  clientId, 
  onOrderAdded 
}: ClientAddOrderDialogProps) {
  const { handleCreateOrder, isSubmitting } = useOrderSubmission(clientId, onOrderAdded);
  const { clientItems, clientPrefix, isLoading } = useClientItems(clientId, open);

  const handleSubmit = async (formData: any) => {
    const success = await handleCreateOrder({
      ...formData,
      clientPrefix
    });
    
    if (success) {
      onOpenChange(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader />
        
        <ClientOrderForm 
          clientItems={clientItems} 
          isLoading={isLoading}
          isSubmitting={isSubmitting}
          onSubmit={handleSubmit}
        />
      </DialogContent>
    </Dialog>
  );
}
